{"version": 3, "sources": ["../../src/server/server-route-utils.ts"], "sourcesContent": ["import type { BaseNextRequest } from './base-http'\nimport type { ParsedUrlQuery } from 'querystring'\n\nimport { getRequestMeta } from './request-meta'\nimport { stringify as stringifyQs } from 'querystring'\n\n// since initial query values are decoded by querystring.parse\n// we need to re-encode them here but still allow passing through\n// values from rewrites/redirects\nexport const stringifyQuery = (req: BaseNextRequest, query: ParsedUrlQuery) => {\n  const initialQuery = getRequestMeta(req, 'initQuery') || {}\n  const initialQueryValues = Object.values(initialQuery)\n\n  return stringifyQs(query, undefined, undefined, {\n    encodeURIComponent(value) {\n      if (\n        value in initialQuery ||\n        initialQueryValues.some((initialQueryVal) => {\n          // `value` always refers to a query value, even if it's nested in an array\n          return Array.isArray(initialQueryVal)\n            ? initialQueryVal.includes(value)\n            : initialQueryVal === value\n        })\n      ) {\n        // Encode keys and values from initial query\n        return encodeURIComponent(value)\n      }\n\n      return value\n    },\n  })\n}\n"], "names": ["stringifyQuery", "req", "query", "initialQuery", "getRequestMeta", "initialQueryValues", "Object", "values", "stringifyQs", "undefined", "encodeURIComponent", "value", "some", "initialQueryVal", "Array", "isArray", "includes"], "mappings": ";;;;+BASaA;;;eAAAA;;;6BANkB;6BACU;AAKlC,MAAMA,iBAAiB,CAACC,KAAsBC;IACnD,MAAMC,eAAeC,IAAAA,2BAAc,EAACH,KAAK,gBAAgB,CAAC;IAC1D,MAAMI,qBAAqBC,OAAOC,MAAM,CAACJ;IAEzC,OAAOK,IAAAA,sBAAW,EAACN,OAAOO,WAAWA,WAAW;QAC9CC,oBAAmBC,KAAK;YACtB,IACEA,SAASR,gBACTE,mBAAmBO,IAAI,CAAC,CAACC;gBACvB,0EAA0E;gBAC1E,OAAOC,MAAMC,OAAO,CAACF,mBACjBA,gBAAgBG,QAAQ,CAACL,SACzBE,oBAAoBF;YAC1B,IACA;gBACA,4CAA4C;gBAC5C,OAAOD,mBAAmBC;YAC5B;YAEA,OAAOA;QACT;IACF;AACF"}