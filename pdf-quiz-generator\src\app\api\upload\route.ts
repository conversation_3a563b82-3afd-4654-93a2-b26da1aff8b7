import { NextRequest, NextResponse } from 'next/server';
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { existsSync } from 'fs';
import pdfParse from 'pdf-parse';
import { chunkText, generateId, sanitizeText } from '@/lib/utils';
import { DocumentChunk, ProcessedDocument } from '@/types/quiz';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    
    if (!file) {
      return NextResponse.json({ error: 'No file uploaded' }, { status: 400 });
    }
    
    if (file.type !== 'application/pdf') {
      return NextResponse.json({ error: 'File must be a PDF' }, { status: 400 });
    }
    
    // Convert file to buffer
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    
    // Create uploads directory if it doesn't exist
    const uploadsDir = join(process.cwd(), 'uploads');
    if (!existsSync(uploadsDir)) {
      await mkdir(uploadsDir, { recursive: true });
    }
    
    // Save file temporarily
    const filename = `${generateId()}_${file.name}`;
    const filepath = join(uploadsDir, filename);
    await writeFile(filepath, buffer);
    
    try {
      // Extract text from PDF
      const pdfData = await pdfParse(buffer);
      const rawText = pdfData.text;
      
      if (!rawText || rawText.trim().length === 0) {
        return NextResponse.json({ error: 'No text found in PDF' }, { status: 400 });
      }
      
      // Clean and sanitize text
      const cleanText = sanitizeText(rawText);
      
      // Chunk the text
      const textChunks = chunkText(cleanText, 1000, 100);
      
      // Create document chunks
      const chunks: DocumentChunk[] = textChunks.map((chunk, index) => ({
        id: generateId(),
        content: chunk,
        pageNumber: Math.floor(index / 3) + 1, // Rough page estimation
        metadata: {
          chunkIndex: index,
          wordCount: chunk.split(' ').length,
        },
      }));
      
      // Create processed document
      const processedDoc: ProcessedDocument = {
        id: generateId(),
        filename: file.name,
        chunks,
        totalPages: pdfData.numpages,
        processedAt: new Date(),
      };
      
      return NextResponse.json({
        success: true,
        document: processedDoc,
        stats: {
          totalChunks: chunks.length,
          totalPages: pdfData.numpages,
          totalWords: cleanText.split(' ').length,
          fileSize: file.size,
        },
      });
      
    } catch (parseError) {
      console.error('PDF parsing error:', parseError);
      return NextResponse.json(
        { error: 'Failed to parse PDF. The file might be corrupted or password-protected.' },
        { status: 400 }
      );
    }
    
  } catch (error) {
    console.error('Upload error:', error);
    return NextResponse.json(
      { error: 'Internal server error during file processing' },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({ message: 'PDF upload endpoint' });
}
