import { NextRequest, NextResponse } from 'next/server';
import { generateEmbedding } from '@/lib/deepseek';
import { DocumentChunk } from '@/types/quiz';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { chunks } = body as { chunks: DocumentChunk[] };
    
    if (!chunks || chunks.length === 0) {
      return NextResponse.json({ error: 'No chunks provided' }, { status: 400 });
    }
    
    if (chunks.length > 100) {
      return NextResponse.json({ error: 'Too many chunks. Maximum 100 chunks per request.' }, { status: 400 });
    }
    
    try {
      // Generate embeddings for all chunks
      const chunksWithEmbeddings = await Promise.allSettled(
        chunks.map(async (chunk) => {
          try {
            const embedding = await generateEmbedding(chunk.content);
            return { ...chunk, embedding };
          } catch (error) {
            console.error(`Failed to generate embedding for chunk ${chunk.id}:`, error);
            throw error;
          }
        })
      );
      
      // Separate successful and failed embeddings
      const successful: DocumentChunk[] = [];
      const failed: { chunkId: string; error: string }[] = [];
      
      chunksWithEmbeddings.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          successful.push(result.value);
        } else {
          failed.push({
            chunkId: chunks[index].id,
            error: result.reason?.message || 'Unknown error',
          });
        }
      });
      
      return NextResponse.json({
        success: true,
        chunks: successful,
        stats: {
          total: chunks.length,
          successful: successful.length,
          failed: failed.length,
          failedChunks: failed,
        },
      });
      
    } catch (error) {
      console.error('Embedding generation error:', error);
      
      if (error instanceof Error && error.message.includes('API key')) {
        return NextResponse.json(
          { error: 'DeepSeek API key is not configured or invalid' },
          { status: 401 }
        );
      }
      
      return NextResponse.json(
        { error: 'Failed to generate embeddings' },
        { status: 500 }
      );
    }
    
  } catch (error) {
    console.error('Request processing error:', error);
    return NextResponse.json(
      { error: 'Invalid request format' },
      { status: 400 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Embeddings generation endpoint',
    maxChunksPerRequest: 100,
    embeddingModel: 'deepseek-embed',
  });
}
