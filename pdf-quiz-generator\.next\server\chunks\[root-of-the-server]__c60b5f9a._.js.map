{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 116, "column": 0}, "map": {"version": 3, "sources": ["file:///Z:/projectx/pdf-quiz-generator/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9)\n}\n\nexport function chunkText(text: string, maxChunkSize: number = 1000, overlap: number = 100): string[] {\n  const chunks: string[] = []\n  const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0)\n  \n  let currentChunk = ''\n  let currentSize = 0\n  \n  for (const sentence of sentences) {\n    const sentenceSize = sentence.trim().length\n    \n    if (currentSize + sentenceSize > maxChunkSize && currentChunk.length > 0) {\n      chunks.push(currentChunk.trim())\n      \n      // Create overlap by keeping the last few sentences\n      const overlapSentences = currentChunk.split(/[.!?]+/).slice(-2).join('. ')\n      currentChunk = overlapSentences + '. ' + sentence.trim()\n      currentSize = currentChunk.length\n    } else {\n      currentChunk += (currentChunk ? '. ' : '') + sentence.trim()\n      currentSize = currentChunk.length\n    }\n  }\n  \n  if (currentChunk.trim().length > 0) {\n    chunks.push(currentChunk.trim())\n  }\n  \n  return chunks.filter(chunk => chunk.length > 50) // Filter out very short chunks\n}\n\nexport function sanitizeText(text: string): string {\n  return text\n    .replace(/\\s+/g, ' ')\n    .replace(/[^\\w\\s.,!?;:()\\-\"']/g, '')\n    .trim()\n}\n\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes'\n  \n  const k = 1024\n  const sizes = ['Bytes', 'KB', 'MB', 'GB']\n  const i = Math.floor(Math.log(bytes) / Math.log(k))\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n}\n\nexport function validatePDF(file: File): { isValid: boolean; error?: string } {\n  if (!file) {\n    return { isValid: false, error: 'No file provided' }\n  }\n  \n  if (file.type !== 'application/pdf') {\n    return { isValid: false, error: 'File must be a PDF' }\n  }\n  \n  // 50MB limit\n  if (file.size > 50 * 1024 * 1024) {\n    return { isValid: false, error: 'File size must be less than 50MB' }\n  }\n  \n  return { isValid: true }\n}\n\nexport function downloadAsJSON(data: any, filename: string) {\n  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })\n  const url = URL.createObjectURL(blob)\n  const a = document.createElement('a')\n  a.href = url\n  a.download = filename\n  document.body.appendChild(a)\n  a.click()\n  document.body.removeChild(a)\n  URL.revokeObjectURL(url)\n}\n\nexport function downloadAsCSV(data: any[], filename: string) {\n  if (data.length === 0) return\n  \n  const headers = Object.keys(data[0])\n  const csvContent = [\n    headers.join(','),\n    ...data.map(row => \n      headers.map(header => {\n        const value = row[header]\n        if (typeof value === 'string' && (value.includes(',') || value.includes('\"'))) {\n          return `\"${value.replace(/\"/g, '\"\"')}\"`\n        }\n        return value\n      }).join(',')\n    )\n  ].join('\\n')\n  \n  const blob = new Blob([csvContent], { type: 'text/csv' })\n  const url = URL.createObjectURL(blob)\n  const a = document.createElement('a')\n  a.href = url\n  a.download = filename\n  document.body.appendChild(a)\n  a.click()\n  document.body.removeChild(a)\n  URL.revokeObjectURL(url)\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,uIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,UAAU,IAAY,EAAE,eAAuB,IAAI,EAAE,UAAkB,GAAG;IACxF,MAAM,SAAmB,EAAE;IAC3B,MAAM,YAAY,KAAK,KAAK,CAAC,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,GAAG,MAAM,GAAG;IAErE,IAAI,eAAe;IACnB,IAAI,cAAc;IAElB,KAAK,MAAM,YAAY,UAAW;QAChC,MAAM,eAAe,SAAS,IAAI,GAAG,MAAM;QAE3C,IAAI,cAAc,eAAe,gBAAgB,aAAa,MAAM,GAAG,GAAG;YACxE,OAAO,IAAI,CAAC,aAAa,IAAI;YAE7B,mDAAmD;YACnD,MAAM,mBAAmB,aAAa,KAAK,CAAC,UAAU,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC;YACrE,eAAe,mBAAmB,OAAO,SAAS,IAAI;YACtD,cAAc,aAAa,MAAM;QACnC,OAAO;YACL,gBAAgB,CAAC,eAAe,OAAO,EAAE,IAAI,SAAS,IAAI;YAC1D,cAAc,aAAa,MAAM;QACnC;IACF;IAEA,IAAI,aAAa,IAAI,GAAG,MAAM,GAAG,GAAG;QAClC,OAAO,IAAI,CAAC,aAAa,IAAI;IAC/B;IAEA,OAAO,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,GAAG,IAAI,+BAA+B;;AAClF;AAEO,SAAS,aAAa,IAAY;IACvC,OAAO,KACJ,OAAO,CAAC,QAAQ,KAChB,OAAO,CAAC,wBAAwB,IAChC,IAAI;AACT;AAEO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAEO,SAAS,YAAY,IAAU;IACpC,IAAI,CAAC,MAAM;QACT,OAAO;YAAE,SAAS;YAAO,OAAO;QAAmB;IACrD;IAEA,IAAI,KAAK,IAAI,KAAK,mBAAmB;QACnC,OAAO;YAAE,SAAS;YAAO,OAAO;QAAqB;IACvD;IAEA,aAAa;IACb,IAAI,KAAK,IAAI,GAAG,KAAK,OAAO,MAAM;QAChC,OAAO;YAAE,SAAS;YAAO,OAAO;QAAmC;IACrE;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAEO,SAAS,eAAe,IAAS,EAAE,QAAgB;IACxD,MAAM,OAAO,IAAI,KAAK;QAAC,KAAK,SAAS,CAAC,MAAM,MAAM;KAAG,EAAE;QAAE,MAAM;IAAmB;IAClF,MAAM,MAAM,IAAI,eAAe,CAAC;IAChC,MAAM,IAAI,SAAS,aAAa,CAAC;IACjC,EAAE,IAAI,GAAG;IACT,EAAE,QAAQ,GAAG;IACb,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,EAAE,KAAK;IACP,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,IAAI,eAAe,CAAC;AACtB;AAEO,SAAS,cAAc,IAAW,EAAE,QAAgB;IACzD,IAAI,KAAK,MAAM,KAAK,GAAG;IAEvB,MAAM,UAAU,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE;IACnC,MAAM,aAAa;QACjB,QAAQ,IAAI,CAAC;WACV,KAAK,GAAG,CAAC,CAAA,MACV,QAAQ,GAAG,CAAC,CAAA;gBACV,MAAM,QAAQ,GAAG,CAAC,OAAO;gBACzB,IAAI,OAAO,UAAU,YAAY,CAAC,MAAM,QAAQ,CAAC,QAAQ,MAAM,QAAQ,CAAC,IAAI,GAAG;oBAC7E,OAAO,CAAC,CAAC,EAAE,MAAM,OAAO,CAAC,MAAM,MAAM,CAAC,CAAC;gBACzC;gBACA,OAAO;YACT,GAAG,IAAI,CAAC;KAEX,CAAC,IAAI,CAAC;IAEP,MAAM,OAAO,IAAI,KAAK;QAAC;KAAW,EAAE;QAAE,MAAM;IAAW;IACvD,MAAM,MAAM,IAAI,eAAe,CAAC;IAChC,MAAM,IAAI,SAAS,aAAa,CAAC;IACjC,EAAE,IAAI,GAAG;IACT,EAAE,QAAQ,GAAG;IACb,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,EAAE,KAAK;IACP,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,IAAI,eAAe,CAAC;AACtB", "debugId": null}}, {"offset": {"line": 247, "column": 0}, "map": {"version": 3, "sources": ["file:///Z:/projectx/pdf-quiz-generator/src/app/api/upload/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { writeFile, mkdir } from 'fs/promises';\nimport { join } from 'path';\nimport { existsSync } from 'fs';\nimport pdfParse from 'pdf-parse';\nimport { chunkText, generateId, sanitizeText } from '@/lib/utils';\nimport { DocumentChunk, ProcessedDocument } from '@/types/quiz';\n\nexport async function POST(request: NextRequest) {\n  try {\n    const formData = await request.formData();\n    const file = formData.get('file') as File;\n    \n    if (!file) {\n      return NextResponse.json({ error: 'No file uploaded' }, { status: 400 });\n    }\n    \n    if (file.type !== 'application/pdf') {\n      return NextResponse.json({ error: 'File must be a PDF' }, { status: 400 });\n    }\n    \n    // Convert file to buffer\n    const bytes = await file.arrayBuffer();\n    const buffer = Buffer.from(bytes);\n    \n    // Create uploads directory if it doesn't exist\n    const uploadsDir = join(process.cwd(), 'uploads');\n    if (!existsSync(uploadsDir)) {\n      await mkdir(uploadsDir, { recursive: true });\n    }\n    \n    // Save file temporarily\n    const filename = `${generateId()}_${file.name}`;\n    const filepath = join(uploadsDir, filename);\n    await writeFile(filepath, buffer);\n    \n    try {\n      // Extract text from PDF\n      const pdfData = await pdfParse(buffer);\n      const rawText = pdfData.text;\n      \n      if (!rawText || rawText.trim().length === 0) {\n        return NextResponse.json({ error: 'No text found in PDF' }, { status: 400 });\n      }\n      \n      // Clean and sanitize text\n      const cleanText = sanitizeText(rawText);\n      \n      // Chunk the text\n      const textChunks = chunkText(cleanText, 1000, 100);\n      \n      // Create document chunks\n      const chunks: DocumentChunk[] = textChunks.map((chunk, index) => ({\n        id: generateId(),\n        content: chunk,\n        pageNumber: Math.floor(index / 3) + 1, // Rough page estimation\n        metadata: {\n          chunkIndex: index,\n          wordCount: chunk.split(' ').length,\n        },\n      }));\n      \n      // Create processed document\n      const processedDoc: ProcessedDocument = {\n        id: generateId(),\n        filename: file.name,\n        chunks,\n        totalPages: pdfData.numpages,\n        processedAt: new Date(),\n      };\n      \n      return NextResponse.json({\n        success: true,\n        document: processedDoc,\n        stats: {\n          totalChunks: chunks.length,\n          totalPages: pdfData.numpages,\n          totalWords: cleanText.split(' ').length,\n          fileSize: file.size,\n        },\n      });\n      \n    } catch (parseError) {\n      console.error('PDF parsing error:', parseError);\n      return NextResponse.json(\n        { error: 'Failed to parse PDF. The file might be corrupted or password-protected.' },\n        { status: 400 }\n      );\n    }\n    \n  } catch (error) {\n    console.error('Upload error:', error);\n    return NextResponse.json(\n      { error: 'Internal server error during file processing' },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function GET() {\n  return NextResponse.json({ message: 'PDF upload endpoint' });\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,QAAQ,QAAQ;QACvC,MAAM,OAAO,SAAS,GAAG,CAAC;QAE1B,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAmB,GAAG;gBAAE,QAAQ;YAAI;QACxE;QAEA,IAAI,KAAK,IAAI,KAAK,mBAAmB;YACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAqB,GAAG;gBAAE,QAAQ;YAAI;QAC1E;QAEA,yBAAyB;QACzB,MAAM,QAAQ,MAAM,KAAK,WAAW;QACpC,MAAM,SAAS,OAAO,IAAI,CAAC;QAE3B,+CAA+C;QAC/C,MAAM,aAAa,CAAA,GAAA,iGAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,GAAG,IAAI;QACvC,IAAI,CAAC,CAAA,GAAA,6FAAA,CAAA,aAAU,AAAD,EAAE,aAAa;YAC3B,MAAM,CAAA,GAAA,qHAAA,CAAA,QAAK,AAAD,EAAE,YAAY;gBAAE,WAAW;YAAK;QAC5C;QAEA,wBAAwB;QACxB,MAAM,WAAW,GAAG,CAAA,GAAA,qHAAA,CAAA,aAAU,AAAD,IAAI,CAAC,EAAE,KAAK,IAAI,EAAE;QAC/C,MAAM,WAAW,CAAA,GAAA,iGAAA,CAAA,OAAI,AAAD,EAAE,YAAY;QAClC,MAAM,CAAA,GAAA,qHAAA,CAAA,YAAS,AAAD,EAAE,UAAU;QAE1B,IAAI;YACF,wBAAwB;YACxB,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,UAAQ,AAAD,EAAE;YAC/B,MAAM,UAAU,QAAQ,IAAI;YAE5B,IAAI,CAAC,WAAW,QAAQ,IAAI,GAAG,MAAM,KAAK,GAAG;gBAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBAAE,OAAO;gBAAuB,GAAG;oBAAE,QAAQ;gBAAI;YAC5E;YAEA,0BAA0B;YAC1B,MAAM,YAAY,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD,EAAE;YAE/B,iBAAiB;YACjB,MAAM,aAAa,CAAA,GAAA,qHAAA,CAAA,YAAS,AAAD,EAAE,WAAW,MAAM;YAE9C,yBAAyB;YACzB,MAAM,SAA0B,WAAW,GAAG,CAAC,CAAC,OAAO,QAAU,CAAC;oBAChE,IAAI,CAAA,GAAA,qHAAA,CAAA,aAAU,AAAD;oBACb,SAAS;oBACT,YAAY,KAAK,KAAK,CAAC,QAAQ,KAAK;oBACpC,UAAU;wBACR,YAAY;wBACZ,WAAW,MAAM,KAAK,CAAC,KAAK,MAAM;oBACpC;gBACF,CAAC;YAED,4BAA4B;YAC5B,MAAM,eAAkC;gBACtC,IAAI,CAAA,GAAA,qHAAA,CAAA,aAAU,AAAD;gBACb,UAAU,KAAK,IAAI;gBACnB;gBACA,YAAY,QAAQ,QAAQ;gBAC5B,aAAa,IAAI;YACnB;YAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,UAAU;gBACV,OAAO;oBACL,aAAa,OAAO,MAAM;oBAC1B,YAAY,QAAQ,QAAQ;oBAC5B,YAAY,UAAU,KAAK,CAAC,KAAK,MAAM;oBACvC,UAAU,KAAK,IAAI;gBACrB;YACF;QAEF,EAAE,OAAO,YAAY;YACnB,QAAQ,KAAK,CAAC,sBAAsB;YACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA0E,GACnF;gBAAE,QAAQ;YAAI;QAElB;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iBAAiB;QAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA+C,GACxD;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe;IACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QAAE,SAAS;IAAsB;AAC5D", "debugId": null}}]}