<?xml version="1.0"?>
<!-- This file was automatically generated from C sources - DO NOT EDIT!
To affect the contents of this file, edit the original C definitions,
and/or use gtk-doc annotations.  -->
<repository version="1.2"
            xmlns="http://www.gtk.org/introspection/core/1.0"
            xmlns:c="http://www.gtk.org/introspection/c/1.0"
            xmlns:glib="http://www.gtk.org/introspection/glib/1.0">
  <include name="GObject" version="2.0"/>
  <include name="Gio" version="2.0"/>
  <include name="cairo" version="1.0"/>
  <package name="poppler-glib"/>
  <c:include name="poppler.h"/>
  <namespace name="Poppler"
             version="0.18"
             shared-library="@rpath/libpoppler-glib.8.dylib"
             c:identifier-prefixes="Poppler"
             c:symbol-prefixes="poppler">
    <constant name="ANNOT_TEXT_ICON_CIRCLE"
              value="Circle"
              c:type="POPPLER_ANNOT_TEXT_ICON_CIRCLE">
      <type name="utf8" c:type="gchar*"/>
    </constant>
    <constant name="ANNOT_TEXT_ICON_COMMENT"
              value="Comment"
              c:type="POPPLER_ANNOT_TEXT_ICON_COMMENT">
      <type name="utf8" c:type="gchar*"/>
    </constant>
    <constant name="ANNOT_TEXT_ICON_CROSS"
              value="Cross"
              c:type="POPPLER_ANNOT_TEXT_ICON_CROSS">
      <type name="utf8" c:type="gchar*"/>
    </constant>
    <constant name="ANNOT_TEXT_ICON_HELP"
              value="Help"
              c:type="POPPLER_ANNOT_TEXT_ICON_HELP">
      <type name="utf8" c:type="gchar*"/>
    </constant>
    <constant name="ANNOT_TEXT_ICON_INSERT"
              value="Insert"
              c:type="POPPLER_ANNOT_TEXT_ICON_INSERT">
      <type name="utf8" c:type="gchar*"/>
    </constant>
    <constant name="ANNOT_TEXT_ICON_KEY"
              value="Key"
              c:type="POPPLER_ANNOT_TEXT_ICON_KEY">
      <type name="utf8" c:type="gchar*"/>
    </constant>
    <constant name="ANNOT_TEXT_ICON_NEW_PARAGRAPH"
              value="NewParagraph"
              c:type="POPPLER_ANNOT_TEXT_ICON_NEW_PARAGRAPH">
      <type name="utf8" c:type="gchar*"/>
    </constant>
    <constant name="ANNOT_TEXT_ICON_NOTE"
              value="Note"
              c:type="POPPLER_ANNOT_TEXT_ICON_NOTE">
      <type name="utf8" c:type="gchar*"/>
    </constant>
    <constant name="ANNOT_TEXT_ICON_PARAGRAPH"
              value="Paragraph"
              c:type="POPPLER_ANNOT_TEXT_ICON_PARAGRAPH">
      <type name="utf8" c:type="gchar*"/>
    </constant>
    <union name="Action"
           c:type="PopplerAction"
           glib:type-name="PopplerAction"
           glib:get-type="poppler_action_get_type"
           c:symbol-prefix="action">
      <doc xml:space="preserve">A data structure for holding actions</doc>
      <field name="type" writable="1">
        <type name="ActionType" c:type="PopplerActionType"/>
      </field>
      <field name="any" writable="1">
        <type name="ActionAny" c:type="PopplerActionAny"/>
      </field>
      <field name="goto_dest" writable="1">
        <type name="ActionGotoDest" c:type="PopplerActionGotoDest"/>
      </field>
      <field name="goto_remote" writable="1">
        <type name="ActionGotoRemote" c:type="PopplerActionGotoRemote"/>
      </field>
      <field name="launch" writable="1">
        <type name="ActionLaunch" c:type="PopplerActionLaunch"/>
      </field>
      <field name="uri" writable="1">
        <type name="ActionUri" c:type="PopplerActionUri"/>
      </field>
      <field name="named" writable="1">
        <type name="ActionNamed" c:type="PopplerActionNamed"/>
      </field>
      <field name="movie" writable="1">
        <type name="ActionMovie" c:type="PopplerActionMovie"/>
      </field>
      <field name="rendition" writable="1">
        <type name="ActionRendition" c:type="PopplerActionRendition"/>
      </field>
      <field name="ocg_state" writable="1">
        <type name="ActionOCGState" c:type="PopplerActionOCGState"/>
      </field>
      <field name="javascript" writable="1">
        <type name="ActionJavascript" c:type="PopplerActionJavascript"/>
      </field>
      <method name="copy" c:identifier="poppler_action_copy">
        <doc xml:space="preserve">Copies @action, creating an identical #PopplerAction.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new action identical to @action</doc>
          <type name="Action" c:type="PopplerAction*"/>
        </return-value>
        <parameters>
          <instance-parameter name="action" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerAction</doc>
            <type name="Action" c:type="PopplerAction*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="free" c:identifier="poppler_action_free">
        <doc xml:space="preserve">Frees @action</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="action" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerAction</doc>
            <type name="Action" c:type="PopplerAction*"/>
          </instance-parameter>
        </parameters>
      </method>
    </union>
    <record name="ActionAny" c:type="PopplerActionAny">
      <field name="type" writable="1">
        <type name="ActionType" c:type="PopplerActionType"/>
      </field>
      <field name="title" writable="1">
        <type name="utf8" c:type="gchar*"/>
      </field>
    </record>
    <record name="ActionGotoDest" c:type="PopplerActionGotoDest">
      <field name="type" writable="1">
        <type name="ActionType" c:type="PopplerActionType"/>
      </field>
      <field name="title" writable="1">
        <type name="utf8" c:type="gchar*"/>
      </field>
      <field name="dest" writable="1">
        <type name="Dest" c:type="PopplerDest*"/>
      </field>
    </record>
    <record name="ActionGotoRemote" c:type="PopplerActionGotoRemote">
      <field name="type" writable="1">
        <type name="ActionType" c:type="PopplerActionType"/>
      </field>
      <field name="title" writable="1">
        <type name="utf8" c:type="gchar*"/>
      </field>
      <field name="file_name" writable="1">
        <type name="utf8" c:type="gchar*"/>
      </field>
      <field name="dest" writable="1">
        <type name="Dest" c:type="PopplerDest*"/>
      </field>
    </record>
    <record name="ActionJavascript" c:type="PopplerActionJavascript">
      <field name="type" writable="1">
        <type name="ActionType" c:type="PopplerActionType"/>
      </field>
      <field name="title" writable="1">
        <type name="utf8" c:type="gchar*"/>
      </field>
      <field name="script" writable="1">
        <type name="utf8" c:type="gchar*"/>
      </field>
    </record>
    <record name="ActionLaunch" c:type="PopplerActionLaunch">
      <field name="type" writable="1">
        <type name="ActionType" c:type="PopplerActionType"/>
      </field>
      <field name="title" writable="1">
        <type name="utf8" c:type="gchar*"/>
      </field>
      <field name="file_name" writable="1">
        <type name="utf8" c:type="gchar*"/>
      </field>
      <field name="params" writable="1">
        <type name="utf8" c:type="gchar*"/>
      </field>
    </record>
    <record name="ActionLayer" c:type="PopplerActionLayer">
      <doc xml:space="preserve">Action to perform over a list of layers</doc>
      <field name="action" writable="1">
        <doc xml:space="preserve">a #PopplerActionLayerAction</doc>
        <type name="ActionLayerAction" c:type="PopplerActionLayerAction"/>
      </field>
      <field name="layers" writable="1">
        <doc xml:space="preserve">list of #PopplerLayer&lt;!-- --&gt;s</doc>
        <type name="GLib.List" c:type="GList*">
          <type name="gpointer" c:type="gpointer"/>
        </type>
      </field>
    </record>
    <enumeration name="ActionLayerAction"
                 version="0.14"
                 glib:type-name="PopplerActionLayerAction"
                 glib:get-type="poppler_action_layer_action_get_type"
                 c:type="PopplerActionLayerAction">
      <doc xml:space="preserve">Layer actions</doc>
      <member name="on"
              value="0"
              c:identifier="POPPLER_ACTION_LAYER_ON"
              glib:nick="on">
        <doc xml:space="preserve">set layer visibility on</doc>
      </member>
      <member name="off"
              value="1"
              c:identifier="POPPLER_ACTION_LAYER_OFF"
              glib:nick="off">
        <doc xml:space="preserve">set layer visibility off</doc>
      </member>
      <member name="toggle"
              value="2"
              c:identifier="POPPLER_ACTION_LAYER_TOGGLE"
              glib:nick="toggle">
        <doc xml:space="preserve">reverse the layer visibility state</doc>
      </member>
    </enumeration>
    <record name="ActionMovie" c:type="PopplerActionMovie">
      <field name="type" writable="1">
        <type name="ActionType" c:type="PopplerActionType"/>
      </field>
      <field name="title" writable="1">
        <type name="utf8" c:type="gchar*"/>
      </field>
      <field name="operation" writable="1">
        <type name="ActionMovieOperation"
              c:type="PopplerActionMovieOperation"/>
      </field>
      <field name="movie" writable="1">
        <type name="Movie" c:type="PopplerMovie*"/>
      </field>
    </record>
    <enumeration name="ActionMovieOperation"
                 version="0.14"
                 glib:type-name="PopplerActionMovieOperation"
                 glib:get-type="poppler_action_movie_operation_get_type"
                 c:type="PopplerActionMovieOperation">
      <doc xml:space="preserve">Movie operations</doc>
      <member name="play"
              value="0"
              c:identifier="POPPLER_ACTION_MOVIE_PLAY"
              glib:nick="play">
        <doc xml:space="preserve">play movie</doc>
      </member>
      <member name="pause"
              value="1"
              c:identifier="POPPLER_ACTION_MOVIE_PAUSE"
              glib:nick="pause">
        <doc xml:space="preserve">pause playing movie</doc>
      </member>
      <member name="resume"
              value="2"
              c:identifier="POPPLER_ACTION_MOVIE_RESUME"
              glib:nick="resume">
        <doc xml:space="preserve">resume paused movie</doc>
      </member>
      <member name="stop"
              value="3"
              c:identifier="POPPLER_ACTION_MOVIE_STOP"
              glib:nick="stop">
        <doc xml:space="preserve">stop playing movie</doc>
      </member>
    </enumeration>
    <record name="ActionNamed" c:type="PopplerActionNamed">
      <field name="type" writable="1">
        <type name="ActionType" c:type="PopplerActionType"/>
      </field>
      <field name="title" writable="1">
        <type name="utf8" c:type="gchar*"/>
      </field>
      <field name="named_dest" writable="1">
        <type name="utf8" c:type="gchar*"/>
      </field>
    </record>
    <record name="ActionOCGState" c:type="PopplerActionOCGState">
      <field name="type" writable="1">
        <type name="ActionType" c:type="PopplerActionType"/>
      </field>
      <field name="title" writable="1">
        <type name="utf8" c:type="gchar*"/>
      </field>
      <field name="state_list" writable="1">
        <type name="GLib.List" c:type="GList*">
          <type name="gpointer" c:type="gpointer"/>
        </type>
      </field>
    </record>
    <record name="ActionRendition" c:type="PopplerActionRendition">
      <field name="type" writable="1">
        <type name="ActionType" c:type="PopplerActionType"/>
      </field>
      <field name="title" writable="1">
        <type name="utf8" c:type="gchar*"/>
      </field>
      <field name="op" writable="1">
        <type name="gint" c:type="gint"/>
      </field>
      <field name="media" writable="1">
        <type name="Media" c:type="PopplerMedia*"/>
      </field>
    </record>
    <enumeration name="ActionType"
                 glib:type-name="PopplerActionType"
                 glib:get-type="poppler_action_type_get_type"
                 c:type="PopplerActionType">
      <doc xml:space="preserve">Action types</doc>
      <member name="unknown"
              value="0"
              c:identifier="POPPLER_ACTION_UNKNOWN"
              glib:nick="unknown">
        <doc xml:space="preserve">unknown action</doc>
      </member>
      <member name="none"
              value="1"
              c:identifier="POPPLER_ACTION_NONE"
              glib:nick="none">
        <doc xml:space="preserve">no action specified</doc>
      </member>
      <member name="goto_dest"
              value="2"
              c:identifier="POPPLER_ACTION_GOTO_DEST"
              glib:nick="goto-dest">
        <doc xml:space="preserve">go to destination</doc>
      </member>
      <member name="goto_remote"
              value="3"
              c:identifier="POPPLER_ACTION_GOTO_REMOTE"
              glib:nick="goto-remote">
        <doc xml:space="preserve">go to destination in another document</doc>
      </member>
      <member name="launch"
              value="4"
              c:identifier="POPPLER_ACTION_LAUNCH"
              glib:nick="launch">
        <doc xml:space="preserve">launch app (or open document</doc>
      </member>
      <member name="uri"
              value="5"
              c:identifier="POPPLER_ACTION_URI"
              glib:nick="uri">
        <doc xml:space="preserve">URI</doc>
      </member>
      <member name="named"
              value="6"
              c:identifier="POPPLER_ACTION_NAMED"
              glib:nick="named">
        <doc xml:space="preserve">predefined action</doc>
      </member>
      <member name="movie"
              value="7"
              c:identifier="POPPLER_ACTION_MOVIE"
              glib:nick="movie">
        <doc xml:space="preserve">play movies. Since 0.14</doc>
      </member>
      <member name="rendition"
              value="8"
              c:identifier="POPPLER_ACTION_RENDITION"
              glib:nick="rendition">
        <doc xml:space="preserve">play multimedia content. Since 0.14</doc>
      </member>
      <member name="ocg_state"
              value="9"
              c:identifier="POPPLER_ACTION_OCG_STATE"
              glib:nick="ocg-state">
        <doc xml:space="preserve">state of layer. Since 0.14</doc>
      </member>
      <member name="javascript"
              value="10"
              c:identifier="POPPLER_ACTION_JAVASCRIPT"
              glib:nick="javascript">
        <doc xml:space="preserve">Javascript. Since 0.18</doc>
      </member>
    </enumeration>
    <record name="ActionUri" c:type="PopplerActionUri">
      <field name="type" writable="1">
        <type name="ActionType" c:type="PopplerActionType"/>
      </field>
      <field name="title" writable="1">
        <type name="utf8" c:type="gchar*"/>
      </field>
      <field name="uri" writable="1">
        <type name="utf8" c:type="char*"/>
      </field>
    </record>
    <class name="Annot"
           c:symbol-prefix="annot"
           c:type="PopplerAnnot"
           parent="GObject.Object"
           glib:type-name="PopplerAnnot"
           glib:get-type="poppler_annot_get_type">
      <method name="get_annot_type"
              c:identifier="poppler_annot_get_annot_type">
        <doc xml:space="preserve">Gets the type of @poppler_annot</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">#PopplerAnnotType of @poppler_annot.</doc>
          <type name="AnnotType" c:type="PopplerAnnotType"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_annot" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerAnnot</doc>
            <type name="Annot" c:type="PopplerAnnot*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_color" c:identifier="poppler_annot_get_color">
        <doc xml:space="preserve">Retrieves the color of @poppler_annot.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new allocated #PopplerColor with the color values of
              @poppler_annot, or %NULL. It must be freed with g_free() when done.</doc>
          <type name="Color" c:type="PopplerColor*"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_annot" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerAnnot</doc>
            <type name="Annot" c:type="PopplerAnnot*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_contents" c:identifier="poppler_annot_get_contents">
        <doc xml:space="preserve">Retrieves the contents of @poppler_annot.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new allocated string with the contents of @poppler_annot. It
              must be freed with g_free() when done.</doc>
          <type name="utf8" c:type="gchar*"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_annot" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerAnnot</doc>
            <type name="Annot" c:type="PopplerAnnot*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_flags" c:identifier="poppler_annot_get_flags">
        <doc xml:space="preserve">Retrieves the flag field specifying various characteristics of the
@poppler_annot.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">the flag field of @poppler_annot.</doc>
          <type name="AnnotFlag" c:type="PopplerAnnotFlag"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_annot" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerAnnot</doc>
            <type name="Annot" c:type="PopplerAnnot*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_modified" c:identifier="poppler_annot_get_modified">
        <doc xml:space="preserve">Retrieves the last modification data of @poppler_annot. The returned
string will be either a PDF format date or a text string.
See also #poppler_date_parse()</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new allocated string with the last modification data of
              @poppler_annot. It must be freed with g_free() when done.</doc>
          <type name="utf8" c:type="gchar*"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_annot" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerAnnot</doc>
            <type name="Annot" c:type="PopplerAnnot*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_name" c:identifier="poppler_annot_get_name">
        <doc xml:space="preserve">Retrieves the name of @poppler_annot.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new allocated string with the name of @poppler_annot. It must
              be freed with g_free() when done.</doc>
          <type name="utf8" c:type="gchar*"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_annot" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerAnnot</doc>
            <type name="Annot" c:type="PopplerAnnot*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_page_index"
              c:identifier="poppler_annot_get_page_index"
              version="0.14">
        <doc xml:space="preserve">Returns the page index to which @poppler_annot is associated, or -1 if unknown</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">page index or -1</doc>
          <type name="gint" c:type="gint"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_annot" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerAnnot</doc>
            <type name="Annot" c:type="PopplerAnnot*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_rectangle"
              c:identifier="poppler_annot_get_rectangle"
              version="0.26">
        <doc xml:space="preserve">Retrieves the rectangle representing the page coordinates where the
annotation @poppler_annot is placed.</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_annot" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerAnnot</doc>
            <type name="Annot" c:type="PopplerAnnot*"/>
          </instance-parameter>
          <parameter name="poppler_rect"
                     direction="out"
                     caller-allocates="1"
                     transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerRectangle to store the annotation's coordinates</doc>
            <type name="Rectangle" c:type="PopplerRectangle*"/>
          </parameter>
        </parameters>
      </method>
      <method name="set_color"
              c:identifier="poppler_annot_set_color"
              version="0.16">
        <doc xml:space="preserve">Sets the color of @poppler_annot.</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_annot" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerAnnot</doc>
            <type name="Annot" c:type="PopplerAnnot*"/>
          </instance-parameter>
          <parameter name="poppler_color"
                     transfer-ownership="none"
                     nullable="1"
                     allow-none="1">
            <doc xml:space="preserve">a #PopplerColor, or %NULL</doc>
            <type name="Color" c:type="PopplerColor*"/>
          </parameter>
        </parameters>
      </method>
      <method name="set_contents"
              c:identifier="poppler_annot_set_contents"
              version="0.12">
        <doc xml:space="preserve">Sets the contents of @poppler_annot to the given value,
replacing the current contents.</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_annot" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerAnnot</doc>
            <type name="Annot" c:type="PopplerAnnot*"/>
          </instance-parameter>
          <parameter name="contents" transfer-ownership="none">
            <doc xml:space="preserve">a text string containing the new contents</doc>
            <type name="utf8" c:type="const gchar*"/>
          </parameter>
        </parameters>
      </method>
      <method name="set_flags"
              c:identifier="poppler_annot_set_flags"
              version="0.22">
        <doc xml:space="preserve">Sets the flag field specifying various characteristics of the
@poppler_annot.</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_annot" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerAnnot</doc>
            <type name="Annot" c:type="PopplerAnnot*"/>
          </instance-parameter>
          <parameter name="flags" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerAnnotFlag</doc>
            <type name="AnnotFlag" c:type="PopplerAnnotFlag"/>
          </parameter>
        </parameters>
      </method>
      <method name="set_rectangle"
              c:identifier="poppler_annot_set_rectangle"
              version="0.26">
        <doc xml:space="preserve">Move the annotation to the rectangle representing the page coordinates
where the annotation @poppler_annot should be placed.</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_annot" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerAnnot</doc>
            <type name="Annot" c:type="PopplerAnnot*"/>
          </instance-parameter>
          <parameter name="poppler_rect" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerRectangle with the new annotation's coordinates</doc>
            <type name="Rectangle" c:type="PopplerRectangle*"/>
          </parameter>
        </parameters>
      </method>
    </class>
    <record name="AnnotCalloutLine"
            c:type="PopplerAnnotCalloutLine"
            glib:type-name="PopplerAnnotCalloutLine"
            glib:get-type="poppler_annot_callout_line_get_type"
            c:symbol-prefix="annot_callout_line">
      <field name="multiline" writable="1">
        <type name="gboolean" c:type="gboolean"/>
      </field>
      <field name="x1" writable="1">
        <type name="gdouble" c:type="gdouble"/>
      </field>
      <field name="y1" writable="1">
        <type name="gdouble" c:type="gdouble"/>
      </field>
      <field name="x2" writable="1">
        <type name="gdouble" c:type="gdouble"/>
      </field>
      <field name="y2" writable="1">
        <type name="gdouble" c:type="gdouble"/>
      </field>
      <field name="x3" writable="1">
        <type name="gdouble" c:type="gdouble"/>
      </field>
      <field name="y3" writable="1">
        <type name="gdouble" c:type="gdouble"/>
      </field>
      <constructor name="new" c:identifier="poppler_annot_callout_line_new">
        <doc xml:space="preserve">Creates a new empty #PopplerAnnotCalloutLine.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new allocated #PopplerAnnotCalloutLine, %NULL in other case.
              It must be freed when done.</doc>
          <type name="AnnotCalloutLine" c:type="PopplerAnnotCalloutLine*"/>
        </return-value>
      </constructor>
      <method name="copy" c:identifier="poppler_annot_callout_line_copy">
        <doc xml:space="preserve">It does copy @callout to a new #PopplerAnnotCalloutLine.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new allocated #PopplerAnnotCalloutLine as exact copy of
              @callout, %NULL in other case. It must be freed when done.</doc>
          <type name="AnnotCalloutLine" c:type="PopplerAnnotCalloutLine*"/>
        </return-value>
        <parameters>
          <instance-parameter name="callout" transfer-ownership="none">
            <doc xml:space="preserve">the #PopplerAnnotCalloutLine to be copied.</doc>
            <type name="AnnotCalloutLine" c:type="PopplerAnnotCalloutLine*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="free" c:identifier="poppler_annot_callout_line_free">
        <doc xml:space="preserve">Frees the memory used by #PopplerAnnotCalloutLine.</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="callout" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerAnnotCalloutLine</doc>
            <type name="AnnotCalloutLine" c:type="PopplerAnnotCalloutLine*"/>
          </instance-parameter>
        </parameters>
      </method>
    </record>
    <class name="AnnotCircle"
           c:symbol-prefix="annot_circle"
           c:type="PopplerAnnotCircle"
           parent="AnnotMarkup"
           glib:type-name="PopplerAnnotCircle"
           glib:get-type="poppler_annot_circle_get_type">
      <constructor name="new"
                   c:identifier="poppler_annot_circle_new"
                   version="0.26">
        <doc xml:space="preserve">Creates a new Circle annotation that will be
located on @rect when added to a page. See
poppler_page_add_annot()</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a newly created #PopplerAnnotCircle annotation</doc>
          <type name="Annot" c:type="PopplerAnnot*"/>
        </return-value>
        <parameters>
          <parameter name="doc" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerDocument</doc>
            <type name="Document" c:type="PopplerDocument*"/>
          </parameter>
          <parameter name="rect" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerRectangle</doc>
            <type name="Rectangle" c:type="PopplerRectangle*"/>
          </parameter>
        </parameters>
      </constructor>
      <method name="get_interior_color"
              c:identifier="poppler_annot_circle_get_interior_color"
              version="0.26">
        <doc xml:space="preserve">Retrieves the interior color of @poppler_annot.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new allocated #PopplerColor with the color values of
              @poppler_annot, or %NULL. It must be freed with g_free() when done.</doc>
          <type name="Color" c:type="PopplerColor*"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_annot" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerAnnotCircle</doc>
            <type name="AnnotCircle" c:type="PopplerAnnotCircle*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="set_interior_color"
              c:identifier="poppler_annot_circle_set_interior_color"
              version="0.26">
        <doc xml:space="preserve">Sets the interior color of @poppler_annot.</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_annot" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerAnnotCircle</doc>
            <type name="AnnotCircle" c:type="PopplerAnnotCircle*"/>
          </instance-parameter>
          <parameter name="poppler_color"
                     transfer-ownership="none"
                     nullable="1"
                     allow-none="1">
            <doc xml:space="preserve">a #PopplerColor, or %NULL</doc>
            <type name="Color" c:type="PopplerColor*"/>
          </parameter>
        </parameters>
      </method>
    </class>
    <enumeration name="AnnotExternalDataType"
                 glib:type-name="PopplerAnnotExternalDataType"
                 glib:get-type="poppler_annot_external_data_type_get_type"
                 c:type="PopplerAnnotExternalDataType">
      <member name="3d"
              value="0"
              c:identifier="POPPLER_ANNOT_EXTERNAL_DATA_MARKUP_3D"
              glib:nick="3d">
      </member>
      <member name="unknown"
              value="1"
              c:identifier="POPPLER_ANNOT_EXTERNAL_DATA_MARKUP_UNKNOWN"
              glib:nick="unknown">
      </member>
    </enumeration>
    <class name="AnnotFileAttachment"
           c:symbol-prefix="annot_file_attachment"
           c:type="PopplerAnnotFileAttachment"
           parent="AnnotMarkup"
           glib:type-name="PopplerAnnotFileAttachment"
           glib:get-type="poppler_annot_file_attachment_get_type">
      <method name="get_attachment"
              c:identifier="poppler_annot_file_attachment_get_attachment"
              version="0.14">
        <doc xml:space="preserve">Creates a #PopplerAttachment for the file of the file attachment annotation @annot.
The #PopplerAttachment must be unrefed with g_object_unref by the caller.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">@PopplerAttachment</doc>
          <type name="Attachment" c:type="PopplerAttachment*"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_annot" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerAnnotFileAttachment</doc>
            <type name="AnnotFileAttachment"
                  c:type="PopplerAnnotFileAttachment*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_name"
              c:identifier="poppler_annot_file_attachment_get_name"
              version="0.14">
        <doc xml:space="preserve">Retrieves the name of @poppler_annot.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new allocated string with the name of @poppler_annot. It must
              be freed with g_free() when done.</doc>
          <type name="utf8" c:type="gchar*"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_annot" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerAnnotFileAttachment</doc>
            <type name="AnnotFileAttachment"
                  c:type="PopplerAnnotFileAttachment*"/>
          </instance-parameter>
        </parameters>
      </method>
    </class>
    <bitfield name="AnnotFlag"
              glib:type-name="PopplerAnnotFlag"
              glib:get-type="poppler_annot_flag_get_type"
              c:type="PopplerAnnotFlag">
      <member name="unknown"
              value="0"
              c:identifier="POPPLER_ANNOT_FLAG_UNKNOWN"
              glib:nick="unknown">
      </member>
      <member name="invisible"
              value="1"
              c:identifier="POPPLER_ANNOT_FLAG_INVISIBLE"
              glib:nick="invisible">
      </member>
      <member name="hidden"
              value="2"
              c:identifier="POPPLER_ANNOT_FLAG_HIDDEN"
              glib:nick="hidden">
      </member>
      <member name="print"
              value="4"
              c:identifier="POPPLER_ANNOT_FLAG_PRINT"
              glib:nick="print">
      </member>
      <member name="no_zoom"
              value="8"
              c:identifier="POPPLER_ANNOT_FLAG_NO_ZOOM"
              glib:nick="no-zoom">
      </member>
      <member name="no_rotate"
              value="16"
              c:identifier="POPPLER_ANNOT_FLAG_NO_ROTATE"
              glib:nick="no-rotate">
      </member>
      <member name="no_view"
              value="32"
              c:identifier="POPPLER_ANNOT_FLAG_NO_VIEW"
              glib:nick="no-view">
      </member>
      <member name="read_only"
              value="64"
              c:identifier="POPPLER_ANNOT_FLAG_READ_ONLY"
              glib:nick="read-only">
      </member>
      <member name="locked"
              value="128"
              c:identifier="POPPLER_ANNOT_FLAG_LOCKED"
              glib:nick="locked">
      </member>
      <member name="toggle_no_view"
              value="256"
              c:identifier="POPPLER_ANNOT_FLAG_TOGGLE_NO_VIEW"
              glib:nick="toggle-no-view">
      </member>
      <member name="locked_contents"
              value="512"
              c:identifier="POPPLER_ANNOT_FLAG_LOCKED_CONTENTS"
              glib:nick="locked-contents">
      </member>
    </bitfield>
    <class name="AnnotFreeText"
           c:symbol-prefix="annot_free_text"
           c:type="PopplerAnnotFreeText"
           parent="AnnotMarkup"
           glib:type-name="PopplerAnnotFreeText"
           glib:get-type="poppler_annot_free_text_get_type">
      <method name="get_callout_line"
              c:identifier="poppler_annot_free_text_get_callout_line">
        <doc xml:space="preserve">Retrieves a #PopplerAnnotCalloutLine of four or six numbers specifying a callout
line attached to the @poppler_annot.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new allocated #PopplerAnnotCalloutLine if the annot has a callout
              line, %NULL in other case. It must be freed with g_free() when
              done.</doc>
          <type name="AnnotCalloutLine" c:type="PopplerAnnotCalloutLine*"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_annot" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerAnnotFreeText</doc>
            <type name="AnnotFreeText" c:type="PopplerAnnotFreeText*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_quadding"
              c:identifier="poppler_annot_free_text_get_quadding">
        <doc xml:space="preserve">Retrieves the justification of the text of @poppler_annot.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">#PopplerAnnotFreeTextQuadding of @poppler_annot.</doc>
          <type name="AnnotFreeTextQuadding"
                c:type="PopplerAnnotFreeTextQuadding"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_annot" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerAnnotFreeText</doc>
            <type name="AnnotFreeText" c:type="PopplerAnnotFreeText*"/>
          </instance-parameter>
        </parameters>
      </method>
    </class>
    <enumeration name="AnnotFreeTextQuadding"
                 glib:type-name="PopplerAnnotFreeTextQuadding"
                 glib:get-type="poppler_annot_free_text_quadding_get_type"
                 c:type="PopplerAnnotFreeTextQuadding">
      <member name="left_justified"
              value="0"
              c:identifier="POPPLER_ANNOT_FREE_TEXT_QUADDING_LEFT_JUSTIFIED"
              glib:nick="left-justified">
      </member>
      <member name="centered"
              value="1"
              c:identifier="POPPLER_ANNOT_FREE_TEXT_QUADDING_CENTERED"
              glib:nick="centered">
      </member>
      <member name="right_justified"
              value="2"
              c:identifier="POPPLER_ANNOT_FREE_TEXT_QUADDING_RIGHT_JUSTIFIED"
              glib:nick="right-justified">
      </member>
    </enumeration>
    <class name="AnnotLine"
           c:symbol-prefix="annot_line"
           c:type="PopplerAnnotLine"
           parent="AnnotMarkup"
           glib:type-name="PopplerAnnotLine"
           glib:get-type="poppler_annot_line_get_type">
      <constructor name="new"
                   c:identifier="poppler_annot_line_new"
                   version="0.26">
        <doc xml:space="preserve">Creates a new Line annotation that will be
located on @rect when added to a page. See
poppler_page_add_annot()</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">A newly created #PopplerAnnotLine annotation</doc>
          <type name="Annot" c:type="PopplerAnnot*"/>
        </return-value>
        <parameters>
          <parameter name="doc" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerDocument</doc>
            <type name="Document" c:type="PopplerDocument*"/>
          </parameter>
          <parameter name="rect" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerRectangle</doc>
            <type name="Rectangle" c:type="PopplerRectangle*"/>
          </parameter>
          <parameter name="start" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerPoint of the starting vertice</doc>
            <type name="Point" c:type="PopplerPoint*"/>
          </parameter>
          <parameter name="end" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerPoint of the ending vertice</doc>
            <type name="Point" c:type="PopplerPoint*"/>
          </parameter>
        </parameters>
      </constructor>
      <method name="set_vertices"
              c:identifier="poppler_annot_line_set_vertices"
              version="0.26">
        <doc xml:space="preserve">Set the coordinate points where the @poppler_annot starts and ends.</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_annot" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerAnnotLine</doc>
            <type name="AnnotLine" c:type="PopplerAnnotLine*"/>
          </instance-parameter>
          <parameter name="start" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerPoint of the starting vertice</doc>
            <type name="Point" c:type="PopplerPoint*"/>
          </parameter>
          <parameter name="end" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerPoint of the ending vertice</doc>
            <type name="Point" c:type="PopplerPoint*"/>
          </parameter>
        </parameters>
      </method>
    </class>
    <record name="AnnotMapping"
            c:type="PopplerAnnotMapping"
            glib:type-name="PopplerAnnotMapping"
            glib:get-type="poppler_annot_mapping_get_type"
            c:symbol-prefix="annot_mapping">
      <doc xml:space="preserve">A #PopplerAnnotMapping structure represents the location
of @annot on the page</doc>
      <field name="area" writable="1">
        <doc xml:space="preserve">a #PopplerRectangle representing an area of the page</doc>
        <type name="Rectangle" c:type="PopplerRectangle"/>
      </field>
      <field name="annot" writable="1">
        <doc xml:space="preserve">a #PopplerAnnot</doc>
        <type name="Annot" c:type="PopplerAnnot*"/>
      </field>
      <constructor name="new" c:identifier="poppler_annot_mapping_new">
        <doc xml:space="preserve">Creates a new #PopplerAnnotMapping</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new #PopplerAnnotMapping, use poppler_annot_mapping_free() to free it</doc>
          <type name="AnnotMapping" c:type="PopplerAnnotMapping*"/>
        </return-value>
      </constructor>
      <method name="copy" c:identifier="poppler_annot_mapping_copy">
        <doc xml:space="preserve">Creates a copy of @mapping</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new allocated copy of @mapping</doc>
          <type name="AnnotMapping" c:type="PopplerAnnotMapping*"/>
        </return-value>
        <parameters>
          <instance-parameter name="mapping" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerAnnotMapping to copy</doc>
            <type name="AnnotMapping" c:type="PopplerAnnotMapping*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="free" c:identifier="poppler_annot_mapping_free">
        <doc xml:space="preserve">Frees the given #PopplerAnnotMapping</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="mapping" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerAnnotMapping</doc>
            <type name="AnnotMapping" c:type="PopplerAnnotMapping*"/>
          </instance-parameter>
        </parameters>
      </method>
    </record>
    <class name="AnnotMarkup"
           c:symbol-prefix="annot_markup"
           c:type="PopplerAnnotMarkup"
           parent="Annot"
           glib:type-name="PopplerAnnotMarkup"
           glib:get-type="poppler_annot_markup_get_type">
      <method name="get_date" c:identifier="poppler_annot_markup_get_date">
        <doc xml:space="preserve">Returns the date and time when the annotation was created</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a #GDate representing the date and time
              when the annotation was created, or %NULL</doc>
          <type name="GLib.Date" c:type="GDate*"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_annot" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerAnnotMarkup</doc>
            <type name="AnnotMarkup" c:type="PopplerAnnotMarkup*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_external_data"
              c:identifier="poppler_annot_markup_get_external_data">
        <doc xml:space="preserve">Gets the external data type of @poppler_annot.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">#PopplerAnnotExternalDataType of @poppler_annot.</doc>
          <type name="AnnotExternalDataType"
                c:type="PopplerAnnotExternalDataType"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_annot" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerAnnotMarkup</doc>
            <type name="AnnotMarkup" c:type="PopplerAnnotMarkup*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_label" c:identifier="poppler_annot_markup_get_label">
        <doc xml:space="preserve">Retrieves the label text of @poppler_annot.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">the label text of @poppler_annot.</doc>
          <type name="utf8" c:type="gchar*"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_annot" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerAnnotMarkup</doc>
            <type name="AnnotMarkup" c:type="PopplerAnnotMarkup*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_opacity"
              c:identifier="poppler_annot_markup_get_opacity">
        <doc xml:space="preserve">Retrieves the opacity value of @poppler_annot.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">the opacity value of @poppler_annot,
              between 0 (transparent) and 1 (opaque)</doc>
          <type name="gdouble" c:type="gdouble"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_annot" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerAnnotMarkup</doc>
            <type name="AnnotMarkup" c:type="PopplerAnnotMarkup*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_popup_is_open"
              c:identifier="poppler_annot_markup_get_popup_is_open">
        <doc xml:space="preserve">Retrieves the state of the popup window related to @poppler_annot.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">the state of @poppler_annot. %TRUE if it's open, %FALSE in
              other case.</doc>
          <type name="gboolean" c:type="gboolean"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_annot" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerAnnotMarkup</doc>
            <type name="AnnotMarkup" c:type="PopplerAnnotMarkup*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_popup_rectangle"
              c:identifier="poppler_annot_markup_get_popup_rectangle"
              version="0.12">
        <doc xml:space="preserve">Retrieves the rectangle of the popup window related to @poppler_annot.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">%TRUE if #PopplerRectangle was correctly filled, %FALSE otherwise</doc>
          <type name="gboolean" c:type="gboolean"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_annot" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerAnnotMarkup</doc>
            <type name="AnnotMarkup" c:type="PopplerAnnotMarkup*"/>
          </instance-parameter>
          <parameter name="poppler_rect"
                     direction="out"
                     caller-allocates="1"
                     transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerRectangle to store the popup rectangle</doc>
            <type name="Rectangle" c:type="PopplerRectangle*"/>
          </parameter>
        </parameters>
      </method>
      <method name="get_reply_to"
              c:identifier="poppler_annot_markup_get_reply_to">
        <doc xml:space="preserve">Gets the reply type of @poppler_annot.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">#PopplerAnnotMarkupReplyType of @poppler_annot.</doc>
          <type name="AnnotMarkupReplyType"
                c:type="PopplerAnnotMarkupReplyType"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_annot" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerAnnotMarkup</doc>
            <type name="AnnotMarkup" c:type="PopplerAnnotMarkup*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_subject"
              c:identifier="poppler_annot_markup_get_subject">
        <doc xml:space="preserve">Retrives the subject text of @poppler_annot.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">the subject text of @poppler_annot.</doc>
          <type name="utf8" c:type="gchar*"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_annot" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerAnnotMarkup</doc>
            <type name="AnnotMarkup" c:type="PopplerAnnotMarkup*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="has_popup"
              c:identifier="poppler_annot_markup_has_popup"
              version="0.12">
        <doc xml:space="preserve">Return %TRUE if the markup annotation has a popup window associated</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">%TRUE, if @poppler_annot has popup, %FALSE otherwise</doc>
          <type name="gboolean" c:type="gboolean"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_annot" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerAnnotMarkup</doc>
            <type name="AnnotMarkup" c:type="PopplerAnnotMarkup*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="set_label"
              c:identifier="poppler_annot_markup_set_label"
              version="0.16">
        <doc xml:space="preserve">Sets the label text of @poppler_annot, replacing the current one</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_annot" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerAnnotMarkup</doc>
            <type name="AnnotMarkup" c:type="PopplerAnnotMarkup*"/>
          </instance-parameter>
          <parameter name="label"
                     transfer-ownership="none"
                     nullable="1"
                     allow-none="1">
            <doc xml:space="preserve">a text string containing the new label, or %NULL</doc>
            <type name="utf8" c:type="const gchar*"/>
          </parameter>
        </parameters>
      </method>
      <method name="set_opacity"
              c:identifier="poppler_annot_markup_set_opacity"
              version="0.16">
        <doc xml:space="preserve">Sets the opacity of @poppler_annot. This value applies to
all visible elements of @poppler_annot in its closed state,
but not to the pop-up window that appears when it's openened</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_annot" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerAnnotMarkup</doc>
            <type name="AnnotMarkup" c:type="PopplerAnnotMarkup*"/>
          </instance-parameter>
          <parameter name="opacity" transfer-ownership="none">
            <doc xml:space="preserve">a constant opacity value, between 0 (transparent) and 1 (opaque)</doc>
            <type name="gdouble" c:type="gdouble"/>
          </parameter>
        </parameters>
      </method>
      <method name="set_popup"
              c:identifier="poppler_annot_markup_set_popup"
              version="0.16">
        <doc xml:space="preserve">Associates a new popup window for editing contents of @poppler_annot.
Popup window shall be displayed by viewers at @popup_rect on the page.</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_annot" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerAnnotMarkup</doc>
            <type name="AnnotMarkup" c:type="PopplerAnnotMarkup*"/>
          </instance-parameter>
          <parameter name="popup_rect" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerRectangle</doc>
            <type name="Rectangle" c:type="PopplerRectangle*"/>
          </parameter>
        </parameters>
      </method>
      <method name="set_popup_is_open"
              c:identifier="poppler_annot_markup_set_popup_is_open"
              version="0.16">
        <doc xml:space="preserve">Sets the state of the popup window related to @poppler_annot.</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_annot" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerAnnotMarkup</doc>
            <type name="AnnotMarkup" c:type="PopplerAnnotMarkup*"/>
          </instance-parameter>
          <parameter name="is_open" transfer-ownership="none">
            <doc xml:space="preserve">whether popup window should initially be displayed open</doc>
            <type name="gboolean" c:type="gboolean"/>
          </parameter>
        </parameters>
      </method>
      <method name="set_popup_rectangle"
              c:identifier="poppler_annot_markup_set_popup_rectangle"
              version="0.33">
        <doc xml:space="preserve">Sets the rectangle of the popup window related to @poppler_annot.
This doesn't have any effect if @poppler_annot doesn't have a
popup associated, use poppler_annot_markup_set_popup() to associate
a popup window to a #PopplerAnnotMarkup.</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_annot" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerAnnotMarkup</doc>
            <type name="AnnotMarkup" c:type="PopplerAnnotMarkup*"/>
          </instance-parameter>
          <parameter name="poppler_rect" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerRectangle to set</doc>
            <type name="Rectangle" c:type="PopplerRectangle*"/>
          </parameter>
        </parameters>
      </method>
    </class>
    <enumeration name="AnnotMarkupReplyType"
                 glib:type-name="PopplerAnnotMarkupReplyType"
                 glib:get-type="poppler_annot_markup_reply_type_get_type"
                 c:type="PopplerAnnotMarkupReplyType">
      <member name="r"
              value="0"
              c:identifier="POPPLER_ANNOT_MARKUP_REPLY_TYPE_R"
              glib:nick="r">
      </member>
      <member name="group"
              value="1"
              c:identifier="POPPLER_ANNOT_MARKUP_REPLY_TYPE_GROUP"
              glib:nick="group">
      </member>
    </enumeration>
    <class name="AnnotMovie"
           c:symbol-prefix="annot_movie"
           c:type="PopplerAnnotMovie"
           parent="Annot"
           glib:type-name="PopplerAnnotMovie"
           glib:get-type="poppler_annot_movie_get_type">
      <method name="get_movie"
              c:identifier="poppler_annot_movie_get_movie"
              version="0.14">
        <doc xml:space="preserve">Retrieves the movie object (PopplerMovie) stored in the @poppler_annot.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">the movie object stored in the @poppler_annot. The returned
              object is owned by #PopplerAnnotMovie and should not be freed</doc>
          <type name="Movie" c:type="PopplerMovie*"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_annot" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerAnnotMovie</doc>
            <type name="AnnotMovie" c:type="PopplerAnnotMovie*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_title"
              c:identifier="poppler_annot_movie_get_title"
              version="0.14">
        <doc xml:space="preserve">Retrieves the movie title of @poppler_annot.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">the title text of @poppler_annot.</doc>
          <type name="utf8" c:type="gchar*"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_annot" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerAnnotMovie</doc>
            <type name="AnnotMovie" c:type="PopplerAnnotMovie*"/>
          </instance-parameter>
        </parameters>
      </method>
    </class>
    <class name="AnnotScreen"
           c:symbol-prefix="annot_screen"
           c:type="PopplerAnnotScreen"
           parent="Annot"
           glib:type-name="PopplerAnnotScreen"
           glib:get-type="poppler_annot_screen_get_type">
      <method name="get_action"
              c:identifier="poppler_annot_screen_get_action"
              version="0.14">
        <doc xml:space="preserve">Retrieves the action (#PopplerAction) that shall be performed when @poppler_annot is activated</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">the action to perform. The returned
              object is owned by @poppler_annot and should not be freed</doc>
          <type name="Action" c:type="PopplerAction*"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_annot" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerAnnotScreen</doc>
            <type name="AnnotScreen" c:type="PopplerAnnotScreen*"/>
          </instance-parameter>
        </parameters>
      </method>
    </class>
    <class name="AnnotSquare"
           c:symbol-prefix="annot_square"
           c:type="PopplerAnnotSquare"
           parent="AnnotMarkup"
           glib:type-name="PopplerAnnotSquare"
           glib:get-type="poppler_annot_square_get_type">
      <constructor name="new"
                   c:identifier="poppler_annot_square_new"
                   version="0.26">
        <doc xml:space="preserve">Creates a new Square annotation that will be
located on @rect when added to a page. See
poppler_page_add_annot()</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a newly created #PopplerAnnotSquare annotation</doc>
          <type name="Annot" c:type="PopplerAnnot*"/>
        </return-value>
        <parameters>
          <parameter name="doc" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerDocument</doc>
            <type name="Document" c:type="PopplerDocument*"/>
          </parameter>
          <parameter name="rect" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerRectangle</doc>
            <type name="Rectangle" c:type="PopplerRectangle*"/>
          </parameter>
        </parameters>
      </constructor>
      <method name="get_interior_color"
              c:identifier="poppler_annot_square_get_interior_color"
              version="0.26">
        <doc xml:space="preserve">Retrieves the interior color of @poppler_annot.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new allocated #PopplerColor with the color values of
              @poppler_annot, or %NULL. It must be freed with g_free() when done.</doc>
          <type name="Color" c:type="PopplerColor*"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_annot" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerAnnotSquare</doc>
            <type name="AnnotSquare" c:type="PopplerAnnotSquare*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="set_interior_color"
              c:identifier="poppler_annot_square_set_interior_color"
              version="0.26">
        <doc xml:space="preserve">Sets the interior color of @poppler_annot.</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_annot" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerAnnotSquare</doc>
            <type name="AnnotSquare" c:type="PopplerAnnotSquare*"/>
          </instance-parameter>
          <parameter name="poppler_color"
                     transfer-ownership="none"
                     nullable="1"
                     allow-none="1">
            <doc xml:space="preserve">a #PopplerColor, or %NULL</doc>
            <type name="Color" c:type="PopplerColor*"/>
          </parameter>
        </parameters>
      </method>
    </class>
    <class name="AnnotText"
           c:symbol-prefix="annot_text"
           c:type="PopplerAnnotText"
           parent="AnnotMarkup"
           glib:type-name="PopplerAnnotText"
           glib:get-type="poppler_annot_text_get_type">
      <constructor name="new"
                   c:identifier="poppler_annot_text_new"
                   version="0.16">
        <doc xml:space="preserve">Creates a new Text annotation that will be
located on @rect when added to a page. See
poppler_page_add_annot()</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">A newly created #PopplerAnnotText annotation</doc>
          <type name="Annot" c:type="PopplerAnnot*"/>
        </return-value>
        <parameters>
          <parameter name="doc" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerDocument</doc>
            <type name="Document" c:type="PopplerDocument*"/>
          </parameter>
          <parameter name="rect" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerRectangle</doc>
            <type name="Rectangle" c:type="PopplerRectangle*"/>
          </parameter>
        </parameters>
      </constructor>
      <method name="get_icon" c:identifier="poppler_annot_text_get_icon">
        <doc xml:space="preserve">Gets name of the icon of @poppler_annot.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new allocated string containing the icon name</doc>
          <type name="utf8" c:type="gchar*"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_annot" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerAnnotText</doc>
            <type name="AnnotText" c:type="PopplerAnnotText*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_is_open" c:identifier="poppler_annot_text_get_is_open">
        <doc xml:space="preserve">Retrieves the state of @poppler_annot.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">the state of @poppler_annot. %TRUE if it's open, %FALSE in
              other case.</doc>
          <type name="gboolean" c:type="gboolean"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_annot" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerAnnotText</doc>
            <type name="AnnotText" c:type="PopplerAnnotText*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_state" c:identifier="poppler_annot_text_get_state">
        <doc xml:space="preserve">Retrieves the state of @poppler_annot.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">#PopplerAnnotTextState of @poppler_annot.</doc>
          <type name="AnnotTextState" c:type="PopplerAnnotTextState"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_annot" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerAnnotText</doc>
            <type name="AnnotText" c:type="PopplerAnnotText*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="set_icon"
              c:identifier="poppler_annot_text_set_icon"
              version="0.16">
        <doc xml:space="preserve">Sets the icon of @poppler_annot. The following predefined
icons are currently supported:
&lt;variablelist&gt;
 &lt;varlistentry&gt;
  &lt;term&gt;#POPPLER_ANNOT_TEXT_ICON_NOTE&lt;/term&gt;
 &lt;/varlistentry&gt;
 &lt;varlistentry&gt;
  &lt;term&gt;#POPPLER_ANNOT_TEXT_ICON_COMMENT&lt;/term&gt;
 &lt;/varlistentry&gt;
 &lt;varlistentry&gt;
  &lt;term&gt;#POPPLER_ANNOT_TEXT_ICON_KEY&lt;/term&gt;
 &lt;/varlistentry&gt;
 &lt;varlistentry&gt;
  &lt;term&gt;#POPPLER_ANNOT_TEXT_ICON_HELP&lt;/term&gt;
 &lt;/varlistentry&gt;
 &lt;varlistentry&gt;
  &lt;term&gt;#POPPLER_ANNOT_TEXT_ICON_NEW_PARAGRAPH&lt;/term&gt;
 &lt;/varlistentry&gt;
 &lt;varlistentry&gt;
  &lt;term&gt;#POPPLER_ANNOT_TEXT_ICON_PARAGRAPH&lt;/term&gt;
 &lt;/varlistentry&gt;
 &lt;varlistentry&gt;
  &lt;term&gt;#POPPLER_ANNOT_TEXT_ICON_INSERT&lt;/term&gt;
 &lt;/varlistentry&gt;
 &lt;varlistentry&gt;
  &lt;term&gt;#POPPLER_ANNOT_TEXT_ICON_CROSS&lt;/term&gt;
 &lt;/varlistentry&gt;
 &lt;varlistentry&gt;
  &lt;term&gt;#POPPLER_ANNOT_TEXT_ICON_CIRCLE&lt;/term&gt;
 &lt;/varlistentry&gt;
&lt;/variablelist&gt;</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_annot" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerAnnotText</doc>
            <type name="AnnotText" c:type="PopplerAnnotText*"/>
          </instance-parameter>
          <parameter name="icon" transfer-ownership="none">
            <doc xml:space="preserve">the name of an icon</doc>
            <type name="utf8" c:type="const gchar*"/>
          </parameter>
        </parameters>
      </method>
      <method name="set_is_open"
              c:identifier="poppler_annot_text_set_is_open"
              version="0.16">
        <doc xml:space="preserve">Sets whether @poppler_annot should initially be displayed open</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_annot" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerAnnotText</doc>
            <type name="AnnotText" c:type="PopplerAnnotText*"/>
          </instance-parameter>
          <parameter name="is_open" transfer-ownership="none">
            <doc xml:space="preserve">whether annotation should initially be displayed open</doc>
            <type name="gboolean" c:type="gboolean"/>
          </parameter>
        </parameters>
      </method>
    </class>
    <class name="AnnotTextMarkup"
           c:symbol-prefix="annot_text_markup"
           c:type="PopplerAnnotTextMarkup"
           parent="AnnotMarkup"
           glib:type-name="PopplerAnnotTextMarkup"
           glib:get-type="poppler_annot_text_markup_get_type">
      <constructor name="new_highlight"
                   c:identifier="poppler_annot_text_markup_new_highlight"
                   version="0.26">
        <doc xml:space="preserve">Creates a new Highlight Text annotation that will be
located on @rect when added to a page. See poppler_page_add_annot()</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">A newly created #PopplerAnnotTextMarkup annotation</doc>
          <type name="Annot" c:type="PopplerAnnot*"/>
        </return-value>
        <parameters>
          <parameter name="doc" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerDocument</doc>
            <type name="Document" c:type="PopplerDocument*"/>
          </parameter>
          <parameter name="rect" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerRectangle</doc>
            <type name="Rectangle" c:type="PopplerRectangle*"/>
          </parameter>
          <parameter name="quadrilaterals" transfer-ownership="none">
            <doc xml:space="preserve">A #GArray of
  #PopplerQuadrilateral&lt;!-- --&gt;s</doc>
            <array name="GLib.Array" c:type="GArray*">
              <type name="Quadrilateral"/>
            </array>
          </parameter>
        </parameters>
      </constructor>
      <constructor name="new_squiggly"
                   c:identifier="poppler_annot_text_markup_new_squiggly"
                   version="0.26">
        <doc xml:space="preserve">Creates a new Squiggly Text annotation that will be
located on @rect when added to a page. See poppler_page_add_annot()</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">A newly created #PopplerAnnotTextMarkup annotation</doc>
          <type name="Annot" c:type="PopplerAnnot*"/>
        </return-value>
        <parameters>
          <parameter name="doc" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerDocument</doc>
            <type name="Document" c:type="PopplerDocument*"/>
          </parameter>
          <parameter name="rect" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerRectangle</doc>
            <type name="Rectangle" c:type="PopplerRectangle*"/>
          </parameter>
          <parameter name="quadrilaterals" transfer-ownership="none">
            <doc xml:space="preserve">A #GArray of
  #PopplerQuadrilateral&lt;!-- --&gt;s</doc>
            <array name="GLib.Array" c:type="GArray*">
              <type name="Quadrilateral"/>
            </array>
          </parameter>
        </parameters>
      </constructor>
      <constructor name="new_strikeout"
                   c:identifier="poppler_annot_text_markup_new_strikeout"
                   version="0.26">
        <doc xml:space="preserve">Creates a new Strike Out Text annotation that will be
located on @rect when added to a page. See poppler_page_add_annot()</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">A newly created #PopplerAnnotTextMarkup annotation</doc>
          <type name="Annot" c:type="PopplerAnnot*"/>
        </return-value>
        <parameters>
          <parameter name="doc" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerDocument</doc>
            <type name="Document" c:type="PopplerDocument*"/>
          </parameter>
          <parameter name="rect" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerRectangle</doc>
            <type name="Rectangle" c:type="PopplerRectangle*"/>
          </parameter>
          <parameter name="quadrilaterals" transfer-ownership="none">
            <doc xml:space="preserve">A #GArray of
  #PopplerQuadrilateral&lt;!-- --&gt;s</doc>
            <array name="GLib.Array" c:type="GArray*">
              <type name="Quadrilateral"/>
            </array>
          </parameter>
        </parameters>
      </constructor>
      <constructor name="new_underline"
                   c:identifier="poppler_annot_text_markup_new_underline"
                   version="0.26">
        <doc xml:space="preserve">Creates a new Underline Text annotation that will be
located on @rect when added to a page. See poppler_page_add_annot()</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">A newly created #PopplerAnnotTextMarkup annotation</doc>
          <type name="Annot" c:type="PopplerAnnot*"/>
        </return-value>
        <parameters>
          <parameter name="doc" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerDocument</doc>
            <type name="Document" c:type="PopplerDocument*"/>
          </parameter>
          <parameter name="rect" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerRectangle</doc>
            <type name="Rectangle" c:type="PopplerRectangle*"/>
          </parameter>
          <parameter name="quadrilaterals" transfer-ownership="none">
            <doc xml:space="preserve">A #GArray of
  #PopplerQuadrilateral&lt;!-- --&gt;s</doc>
            <array name="GLib.Array" c:type="GArray*">
              <type name="Quadrilateral"/>
            </array>
          </parameter>
        </parameters>
      </constructor>
      <method name="get_quadrilaterals"
              c:identifier="poppler_annot_text_markup_get_quadrilaterals"
              version="0.26">
        <doc xml:space="preserve">Returns a #GArray of #PopplerQuadrilateral items that map from a
location on @page to a #PopplerAnnotTextMarkup.  This array must be freed
when done.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">A #GArray of #PopplerQuadrilateral</doc>
          <array name="GLib.Array" c:type="GArray*">
            <type name="Quadrilateral"/>
          </array>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_annot" transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerAnnotTextMarkup</doc>
            <type name="AnnotTextMarkup" c:type="PopplerAnnotTextMarkup*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="set_quadrilaterals"
              c:identifier="poppler_annot_text_markup_set_quadrilaterals"
              version="0.26">
        <doc xml:space="preserve">Set the regions (Quadrilaterals) to apply the text markup in @poppler_annot.</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_annot" transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerAnnotTextMarkup</doc>
            <type name="AnnotTextMarkup" c:type="PopplerAnnotTextMarkup*"/>
          </instance-parameter>
          <parameter name="quadrilaterals" transfer-ownership="none">
            <doc xml:space="preserve">A #GArray of
  #PopplerQuadrilateral&lt;!-- --&gt;s</doc>
            <array name="GLib.Array" c:type="GArray*">
              <type name="Quadrilateral"/>
            </array>
          </parameter>
        </parameters>
      </method>
    </class>
    <enumeration name="AnnotTextState"
                 glib:type-name="PopplerAnnotTextState"
                 glib:get-type="poppler_annot_text_state_get_type"
                 c:type="PopplerAnnotTextState">
      <member name="marked"
              value="0"
              c:identifier="POPPLER_ANNOT_TEXT_STATE_MARKED"
              glib:nick="marked">
      </member>
      <member name="unmarked"
              value="1"
              c:identifier="POPPLER_ANNOT_TEXT_STATE_UNMARKED"
              glib:nick="unmarked">
      </member>
      <member name="accepted"
              value="2"
              c:identifier="POPPLER_ANNOT_TEXT_STATE_ACCEPTED"
              glib:nick="accepted">
      </member>
      <member name="rejected"
              value="3"
              c:identifier="POPPLER_ANNOT_TEXT_STATE_REJECTED"
              glib:nick="rejected">
      </member>
      <member name="cancelled"
              value="4"
              c:identifier="POPPLER_ANNOT_TEXT_STATE_CANCELLED"
              glib:nick="cancelled">
      </member>
      <member name="completed"
              value="5"
              c:identifier="POPPLER_ANNOT_TEXT_STATE_COMPLETED"
              glib:nick="completed">
      </member>
      <member name="none"
              value="6"
              c:identifier="POPPLER_ANNOT_TEXT_STATE_NONE"
              glib:nick="none">
      </member>
      <member name="unknown"
              value="7"
              c:identifier="POPPLER_ANNOT_TEXT_STATE_UNKNOWN"
              glib:nick="unknown">
      </member>
    </enumeration>
    <enumeration name="AnnotType"
                 glib:type-name="PopplerAnnotType"
                 glib:get-type="poppler_annot_type_get_type"
                 c:type="PopplerAnnotType">
      <member name="unknown"
              value="0"
              c:identifier="POPPLER_ANNOT_UNKNOWN"
              glib:nick="unknown">
      </member>
      <member name="text"
              value="1"
              c:identifier="POPPLER_ANNOT_TEXT"
              glib:nick="text">
      </member>
      <member name="link"
              value="2"
              c:identifier="POPPLER_ANNOT_LINK"
              glib:nick="link">
      </member>
      <member name="free_text"
              value="3"
              c:identifier="POPPLER_ANNOT_FREE_TEXT"
              glib:nick="free-text">
      </member>
      <member name="line"
              value="4"
              c:identifier="POPPLER_ANNOT_LINE"
              glib:nick="line">
      </member>
      <member name="square"
              value="5"
              c:identifier="POPPLER_ANNOT_SQUARE"
              glib:nick="square">
      </member>
      <member name="circle"
              value="6"
              c:identifier="POPPLER_ANNOT_CIRCLE"
              glib:nick="circle">
      </member>
      <member name="polygon"
              value="7"
              c:identifier="POPPLER_ANNOT_POLYGON"
              glib:nick="polygon">
      </member>
      <member name="poly_line"
              value="8"
              c:identifier="POPPLER_ANNOT_POLY_LINE"
              glib:nick="poly-line">
      </member>
      <member name="highlight"
              value="9"
              c:identifier="POPPLER_ANNOT_HIGHLIGHT"
              glib:nick="highlight">
      </member>
      <member name="underline"
              value="10"
              c:identifier="POPPLER_ANNOT_UNDERLINE"
              glib:nick="underline">
      </member>
      <member name="squiggly"
              value="11"
              c:identifier="POPPLER_ANNOT_SQUIGGLY"
              glib:nick="squiggly">
      </member>
      <member name="strike_out"
              value="12"
              c:identifier="POPPLER_ANNOT_STRIKE_OUT"
              glib:nick="strike-out">
      </member>
      <member name="stamp"
              value="13"
              c:identifier="POPPLER_ANNOT_STAMP"
              glib:nick="stamp">
      </member>
      <member name="caret"
              value="14"
              c:identifier="POPPLER_ANNOT_CARET"
              glib:nick="caret">
      </member>
      <member name="ink"
              value="15"
              c:identifier="POPPLER_ANNOT_INK"
              glib:nick="ink">
      </member>
      <member name="popup"
              value="16"
              c:identifier="POPPLER_ANNOT_POPUP"
              glib:nick="popup">
      </member>
      <member name="file_attachment"
              value="17"
              c:identifier="POPPLER_ANNOT_FILE_ATTACHMENT"
              glib:nick="file-attachment">
      </member>
      <member name="sound"
              value="18"
              c:identifier="POPPLER_ANNOT_SOUND"
              glib:nick="sound">
      </member>
      <member name="movie"
              value="19"
              c:identifier="POPPLER_ANNOT_MOVIE"
              glib:nick="movie">
      </member>
      <member name="widget"
              value="20"
              c:identifier="POPPLER_ANNOT_WIDGET"
              glib:nick="widget">
      </member>
      <member name="screen"
              value="21"
              c:identifier="POPPLER_ANNOT_SCREEN"
              glib:nick="screen">
      </member>
      <member name="printer_mark"
              value="22"
              c:identifier="POPPLER_ANNOT_PRINTER_MARK"
              glib:nick="printer-mark">
      </member>
      <member name="trap_net"
              value="23"
              c:identifier="POPPLER_ANNOT_TRAP_NET"
              glib:nick="trap-net">
      </member>
      <member name="watermark"
              value="24"
              c:identifier="POPPLER_ANNOT_WATERMARK"
              glib:nick="watermark">
      </member>
      <member name="3d"
              value="25"
              c:identifier="POPPLER_ANNOT_3D"
              glib:nick="3d">
      </member>
    </enumeration>
    <class name="Attachment"
           c:symbol-prefix="attachment"
           c:type="PopplerAttachment"
           parent="GObject.Object"
           glib:type-name="PopplerAttachment"
           glib:get-type="poppler_attachment_get_type"
           glib:type-struct="AttachmentClass">
      <method name="save" c:identifier="poppler_attachment_save" throws="1">
        <doc xml:space="preserve">Saves @attachment to a file indicated by @filename.  If @error is set, %FALSE
will be returned. Possible errors include those in the #G_FILE_ERROR domain
and whatever the save function generates.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">%TRUE, if the file successfully saved</doc>
          <type name="gboolean" c:type="gboolean"/>
        </return-value>
        <parameters>
          <instance-parameter name="attachment" transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerAttachment.</doc>
            <type name="Attachment" c:type="PopplerAttachment*"/>
          </instance-parameter>
          <parameter name="filename" transfer-ownership="none">
            <doc xml:space="preserve">name of file to save</doc>
            <type name="utf8" c:type="const char*"/>
          </parameter>
        </parameters>
      </method>
      <method name="save_to_callback"
              c:identifier="poppler_attachment_save_to_callback"
              throws="1">
        <doc xml:space="preserve">Saves @attachment by feeding the produced data to @save_func. Can be used
when you want to store the attachment to something other than a file, such as
an in-memory buffer or a socket. If @error is set, %FALSE will be
returned. Possible errors include those in the #G_FILE_ERROR domain and
whatever the save function generates.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">%TRUE, if the save successfully completed</doc>
          <type name="gboolean" c:type="gboolean"/>
        </return-value>
        <parameters>
          <instance-parameter name="attachment" transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerAttachment.</doc>
            <type name="Attachment" c:type="PopplerAttachment*"/>
          </instance-parameter>
          <parameter name="save_func"
                     transfer-ownership="none"
                     scope="call"
                     closure="1">
            <doc xml:space="preserve">a function that is called to save each block of data that the save routine generates.</doc>
            <type name="AttachmentSaveFunc"
                  c:type="PopplerAttachmentSaveFunc"/>
          </parameter>
          <parameter name="user_data"
                     transfer-ownership="none"
                     nullable="1"
                     allow-none="1">
            <doc xml:space="preserve">user data to pass to the save function.</doc>
            <type name="gpointer" c:type="gpointer"/>
          </parameter>
        </parameters>
      </method>
      <field name="parent">
        <type name="GObject.Object" c:type="GObject"/>
      </field>
      <field name="name">
        <type name="utf8" c:type="gchar*"/>
      </field>
      <field name="description">
        <type name="utf8" c:type="gchar*"/>
      </field>
      <field name="size">
        <type name="gsize" c:type="gsize"/>
      </field>
      <field name="mtime">
        <type name="GLib.Time" c:type="GTime"/>
      </field>
      <field name="ctime">
        <type name="GLib.Time" c:type="GTime"/>
      </field>
      <field name="checksum">
        <type name="GLib.String" c:type="GString*"/>
      </field>
    </class>
    <record name="AttachmentClass"
            c:type="PopplerAttachmentClass"
            glib:is-gtype-struct-for="Attachment">
      <field name="parent_class">
        <type name="GObject.ObjectClass" c:type="GObjectClass"/>
      </field>
    </record>
    <callback name="AttachmentSaveFunc"
              c:type="PopplerAttachmentSaveFunc"
              throws="1">
      <doc xml:space="preserve">Specifies the type of the function passed to
poppler_attachment_save_to_callback().  It is called once for each block of
bytes that is "written" by poppler_attachment_save_to_callback().  If
successful it should return %TRUE.  If an error occurs it should set
@error and return %FALSE, in which case poppler_attachment_save_to_callback()
will fail with the same error.</doc>
      <return-value transfer-ownership="none">
        <doc xml:space="preserve">%TRUE if successful, %FALSE (with @error set) if failed.</doc>
        <type name="gboolean" c:type="gboolean"/>
      </return-value>
      <parameters>
        <parameter name="buf" transfer-ownership="none">
          <doc xml:space="preserve">buffer containing
  bytes to be written.</doc>
          <array length="1" zero-terminated="0" c:type="gchar*">
            <type name="guint8"/>
          </array>
        </parameter>
        <parameter name="count" transfer-ownership="none">
          <doc xml:space="preserve">number of bytes in @buf.</doc>
          <type name="gsize" c:type="gsize"/>
        </parameter>
        <parameter name="data"
                   transfer-ownership="none"
                   nullable="1"
                   allow-none="1"
                   closure="2">
          <doc xml:space="preserve">user data passed to poppler_attachment_save_to_callback()</doc>
          <type name="gpointer" c:type="gpointer"/>
        </parameter>
      </parameters>
    </callback>
    <enumeration name="Backend"
                 glib:type-name="PopplerBackend"
                 glib:get-type="poppler_backend_get_type"
                 c:type="PopplerBackend">
      <doc xml:space="preserve">Backend codes returned by poppler_get_backend().</doc>
      <member name="unknown"
              value="0"
              c:identifier="POPPLER_BACKEND_UNKNOWN"
              glib:nick="unknown">
        <doc xml:space="preserve">Unknown backend</doc>
      </member>
      <member name="splash"
              value="1"
              c:identifier="POPPLER_BACKEND_SPLASH"
              glib:nick="splash">
        <doc xml:space="preserve">Splash backend</doc>
      </member>
      <member name="cairo"
              value="2"
              c:identifier="POPPLER_BACKEND_CAIRO"
              glib:nick="cairo">
        <doc xml:space="preserve">Cairo backend</doc>
      </member>
    </enumeration>
    <record name="Color"
            c:type="PopplerColor"
            glib:type-name="PopplerColor"
            glib:get-type="poppler_color_get_type"
            c:symbol-prefix="color">
      <doc xml:space="preserve">A #PopplerColor describes a RGB color. Color components
are values between 0 and 65535</doc>
      <field name="red" writable="1">
        <doc xml:space="preserve">the red componment of color</doc>
        <type name="guint16" c:type="guint16"/>
      </field>
      <field name="green" writable="1">
        <doc xml:space="preserve">the green component of color</doc>
        <type name="guint16" c:type="guint16"/>
      </field>
      <field name="blue" writable="1">
        <doc xml:space="preserve">the blue component of color</doc>
        <type name="guint16" c:type="guint16"/>
      </field>
      <constructor name="new" c:identifier="poppler_color_new">
        <doc xml:space="preserve">Creates a new #PopplerColor</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new #PopplerColor, use poppler_color_free() to free it</doc>
          <type name="Color" c:type="PopplerColor*"/>
        </return-value>
      </constructor>
      <method name="copy" c:identifier="poppler_color_copy">
        <doc xml:space="preserve">Creates a copy of @color</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new allocated copy of @color</doc>
          <type name="Color" c:type="PopplerColor*"/>
        </return-value>
        <parameters>
          <instance-parameter name="color" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerColor to copy</doc>
            <type name="Color" c:type="PopplerColor*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="free" c:identifier="poppler_color_free">
        <doc xml:space="preserve">Frees the given #PopplerColor</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="color" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerColor</doc>
            <type name="Color" c:type="PopplerColor*"/>
          </instance-parameter>
        </parameters>
      </method>
    </record>
    <record name="Dest"
            c:type="PopplerDest"
            glib:type-name="PopplerDest"
            glib:get-type="poppler_dest_get_type"
            c:symbol-prefix="dest">
      <doc xml:space="preserve">Data structure for holding a destination</doc>
      <field name="type" writable="1">
        <doc xml:space="preserve">type of destination</doc>
        <type name="DestType" c:type="PopplerDestType"/>
      </field>
      <field name="page_num" writable="1">
        <doc xml:space="preserve">page number</doc>
        <type name="gint" c:type="int"/>
      </field>
      <field name="left" writable="1">
        <doc xml:space="preserve">left coordinate</doc>
        <type name="gdouble" c:type="double"/>
      </field>
      <field name="bottom" writable="1">
        <doc xml:space="preserve">bottom coordinate</doc>
        <type name="gdouble" c:type="double"/>
      </field>
      <field name="right" writable="1">
        <doc xml:space="preserve">right coordinate</doc>
        <type name="gdouble" c:type="double"/>
      </field>
      <field name="top" writable="1">
        <doc xml:space="preserve">top coordinate</doc>
        <type name="gdouble" c:type="double"/>
      </field>
      <field name="zoom" writable="1">
        <doc xml:space="preserve">scale factor</doc>
        <type name="gdouble" c:type="double"/>
      </field>
      <field name="named_dest" writable="1">
        <doc xml:space="preserve">name of the destination (#POPPLER_DEST_NAMED only)</doc>
        <type name="utf8" c:type="gchar*"/>
      </field>
      <field name="change_left" writable="1" bits="1">
        <doc xml:space="preserve">whether left coordinate should be changed</doc>
        <type name="guint" c:type="guint"/>
      </field>
      <field name="change_top" writable="1" bits="1">
        <doc xml:space="preserve">whether top coordinate should be changed</doc>
        <type name="guint" c:type="guint"/>
      </field>
      <field name="change_zoom" writable="1" bits="1">
        <doc xml:space="preserve">whether scale factor should be changed</doc>
        <type name="guint" c:type="guint"/>
      </field>
      <method name="copy" c:identifier="poppler_dest_copy">
        <doc xml:space="preserve">Copies @dest, creating an identical #PopplerDest.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new destination identical to @dest</doc>
          <type name="Dest" c:type="PopplerDest*"/>
        </return-value>
        <parameters>
          <instance-parameter name="dest" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerDest</doc>
            <type name="Dest" c:type="PopplerDest*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="free" c:identifier="poppler_dest_free">
        <doc xml:space="preserve">Frees @dest</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="dest" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerDest</doc>
            <type name="Dest" c:type="PopplerDest*"/>
          </instance-parameter>
        </parameters>
      </method>
    </record>
    <enumeration name="DestType"
                 glib:type-name="PopplerDestType"
                 glib:get-type="poppler_dest_type_get_type"
                 c:type="PopplerDestType">
      <doc xml:space="preserve">Destination types</doc>
      <member name="unknown"
              value="0"
              c:identifier="POPPLER_DEST_UNKNOWN"
              glib:nick="unknown">
        <doc xml:space="preserve">unknown destination</doc>
      </member>
      <member name="xyz"
              value="1"
              c:identifier="POPPLER_DEST_XYZ"
              glib:nick="xyz">
        <doc xml:space="preserve">go to page with coordinates (left, top)
positioned at the upper-left corner of the window and the contents of
the page magnified by the factor zoom</doc>
      </member>
      <member name="fit"
              value="2"
              c:identifier="POPPLER_DEST_FIT"
              glib:nick="fit">
        <doc xml:space="preserve">go to page with its contents magnified just
enough to fit the entire page within the window both horizontally and
vertically</doc>
      </member>
      <member name="fith"
              value="3"
              c:identifier="POPPLER_DEST_FITH"
              glib:nick="fith">
        <doc xml:space="preserve">go to page with the vertical coordinate top
positioned at the top edge of the window and the contents of the page
magnified just enough to fit the entire width of the page within the window</doc>
      </member>
      <member name="fitv"
              value="4"
              c:identifier="POPPLER_DEST_FITV"
              glib:nick="fitv">
        <doc xml:space="preserve">go to page with the horizontal coordinate
left positioned at the left edge of the window and the contents of the
page magnified just enough to fit the entire height of the page within the window</doc>
      </member>
      <member name="fitr"
              value="5"
              c:identifier="POPPLER_DEST_FITR"
              glib:nick="fitr">
        <doc xml:space="preserve">go to page with its contents magnified just
enough to fit the rectangle specified by the coordinates left, bottom,
right, and top entirely within the window both horizontally and vertically</doc>
      </member>
      <member name="fitb"
              value="6"
              c:identifier="POPPLER_DEST_FITB"
              glib:nick="fitb">
        <doc xml:space="preserve">go to page with its contents magnified just enough to fit
its bounding box entirely within the window both horizontally and vertically</doc>
      </member>
      <member name="fitbh"
              value="7"
              c:identifier="POPPLER_DEST_FITBH"
              glib:nick="fitbh">
        <doc xml:space="preserve">go to page with the vertical
coordinate top positioned at the top edge of the window and the
contents of the page magnified just enough to fit the entire width of its
bounding box within the window</doc>
      </member>
      <member name="fitbv"
              value="8"
              c:identifier="POPPLER_DEST_FITBV"
              glib:nick="fitbv">
        <doc xml:space="preserve">go to page with the horizontal
coordinate left positioned at the left edge of the window and the
contents of the page magnified just enough to fit the entire height of its
bounding box within the window</doc>
      </member>
      <member name="named"
              value="9"
              c:identifier="POPPLER_DEST_NAMED"
              glib:nick="named">
        <doc xml:space="preserve">got to page specified by a name. See poppler_document_find_dest()</doc>
      </member>
    </enumeration>
    <class name="Document"
           c:symbol-prefix="document"
           c:type="PopplerDocument"
           parent="GObject.Object"
           glib:type-name="PopplerDocument"
           glib:get-type="poppler_document_get_type">
      <constructor name="new_from_data"
                   c:identifier="poppler_document_new_from_data"
                   throws="1">
        <doc xml:space="preserve">Creates a new #PopplerDocument.  If %NULL is returned, then @error will be
set. Possible errors include those in the #POPPLER_ERROR and #G_FILE_ERROR
domains.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">A newly created #PopplerDocument, or %NULL</doc>
          <type name="Document" c:type="PopplerDocument*"/>
        </return-value>
        <parameters>
          <parameter name="data" transfer-ownership="none">
            <doc xml:space="preserve">the pdf data contained in a char array</doc>
            <type name="utf8" c:type="char*"/>
          </parameter>
          <parameter name="length" transfer-ownership="none">
            <doc xml:space="preserve">the length of #data</doc>
            <type name="gint" c:type="int"/>
          </parameter>
          <parameter name="password"
                     transfer-ownership="none"
                     nullable="1"
                     allow-none="1">
            <doc xml:space="preserve">password to unlock the file with, or %NULL</doc>
            <type name="utf8" c:type="const char*"/>
          </parameter>
        </parameters>
      </constructor>
      <constructor name="new_from_file"
                   c:identifier="poppler_document_new_from_file"
                   throws="1">
        <doc xml:space="preserve">Creates a new #PopplerDocument.  If %NULL is returned, then @error will be
set. Possible errors include those in the #POPPLER_ERROR and #G_FILE_ERROR
domains.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">A newly created #PopplerDocument, or %NULL</doc>
          <type name="Document" c:type="PopplerDocument*"/>
        </return-value>
        <parameters>
          <parameter name="uri" transfer-ownership="none">
            <doc xml:space="preserve">uri of the file to load</doc>
            <type name="utf8" c:type="const char*"/>
          </parameter>
          <parameter name="password"
                     transfer-ownership="none"
                     nullable="1"
                     allow-none="1">
            <doc xml:space="preserve">password to unlock the file with, or %NULL</doc>
            <type name="utf8" c:type="const char*"/>
          </parameter>
        </parameters>
      </constructor>
      <constructor name="new_from_gfile"
                   c:identifier="poppler_document_new_from_gfile"
                   version="0.22"
                   throws="1">
        <doc xml:space="preserve">Creates a new #PopplerDocument reading the PDF contents from @file.
Possible errors include those in the #POPPLER_ERROR and #G_FILE_ERROR
domains.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new #PopplerDocument, or %NULL</doc>
          <type name="Document" c:type="PopplerDocument*"/>
        </return-value>
        <parameters>
          <parameter name="file" transfer-ownership="none">
            <doc xml:space="preserve">a #GFile to load</doc>
            <type name="Gio.File" c:type="GFile*"/>
          </parameter>
          <parameter name="password"
                     transfer-ownership="none"
                     nullable="1"
                     allow-none="1">
            <doc xml:space="preserve">password to unlock the file with, or %NULL</doc>
            <type name="utf8" c:type="const char*"/>
          </parameter>
          <parameter name="cancellable"
                     transfer-ownership="none"
                     nullable="1"
                     allow-none="1">
            <doc xml:space="preserve">a #GCancellable, or %NULL</doc>
            <type name="Gio.Cancellable" c:type="GCancellable*"/>
          </parameter>
        </parameters>
      </constructor>
      <constructor name="new_from_stream"
                   c:identifier="poppler_document_new_from_stream"
                   version="0.22"
                   throws="1">
        <doc xml:space="preserve">Creates a new #PopplerDocument reading the PDF contents from @stream.
Note that the given #GInputStream must be seekable or %G_IO_ERROR_NOT_SUPPORTED
will be returned.
Possible errors include those in the #POPPLER_ERROR and #G_FILE_ERROR
domains.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new #PopplerDocument, or %NULL</doc>
          <type name="Document" c:type="PopplerDocument*"/>
        </return-value>
        <parameters>
          <parameter name="stream" transfer-ownership="none">
            <doc xml:space="preserve">a #GInputStream to read from</doc>
            <type name="Gio.InputStream" c:type="GInputStream*"/>
          </parameter>
          <parameter name="length" transfer-ownership="none">
            <doc xml:space="preserve">the stream length, or -1 if not known</doc>
            <type name="gint64" c:type="goffset"/>
          </parameter>
          <parameter name="password"
                     transfer-ownership="none"
                     nullable="1"
                     allow-none="1">
            <doc xml:space="preserve">password to unlock the file with, or %NULL</doc>
            <type name="utf8" c:type="const char*"/>
          </parameter>
          <parameter name="cancellable"
                     transfer-ownership="none"
                     nullable="1"
                     allow-none="1">
            <doc xml:space="preserve">a #GCancellable, or %NULL</doc>
            <type name="Gio.Cancellable" c:type="GCancellable*"/>
          </parameter>
        </parameters>
      </constructor>
      <method name="find_dest" c:identifier="poppler_document_find_dest">
        <doc xml:space="preserve">Finds named destination @link_name in @document</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">The #PopplerDest destination or %NULL if
@link_name is not a destination. Returned value must
be freed with #poppler_dest_free</doc>
          <type name="Dest" c:type="PopplerDest*"/>
        </return-value>
        <parameters>
          <instance-parameter name="document" transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerDocument</doc>
            <type name="Document" c:type="PopplerDocument*"/>
          </instance-parameter>
          <parameter name="link_name" transfer-ownership="none">
            <doc xml:space="preserve">a named destination</doc>
            <type name="utf8" c:type="const gchar*"/>
          </parameter>
        </parameters>
      </method>
      <method name="get_attachments"
              c:identifier="poppler_document_get_attachments">
        <doc xml:space="preserve">Returns a #GList containing #PopplerAttachment&lt;!-- --&gt;s.  These attachments
are unowned, and must be unreffed, and the list must be freed with
g_list_free().</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a list of available attachments.</doc>
          <type name="GLib.List" c:type="GList*">
            <type name="Attachment"/>
          </type>
        </return-value>
        <parameters>
          <instance-parameter name="document" transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerDocument</doc>
            <type name="Document" c:type="PopplerDocument*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_author"
              c:identifier="poppler_document_get_author"
              version="0.16">
        <doc xml:space="preserve">Returns the author of the document</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new allocated string containing the author
              of @document, or %NULL</doc>
          <type name="utf8" c:type="gchar*"/>
        </return-value>
        <parameters>
          <instance-parameter name="document" transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerDocument</doc>
            <type name="Document" c:type="PopplerDocument*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_creation_date"
              c:identifier="poppler_document_get_creation_date"
              version="0.16">
        <doc xml:space="preserve">Returns the date the document was created as seconds since the Epoch</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">the date the document was created, or -1</doc>
          <type name="glong" c:type="time_t"/>
        </return-value>
        <parameters>
          <instance-parameter name="document" transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerDocument</doc>
            <type name="Document" c:type="PopplerDocument*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_creator"
              c:identifier="poppler_document_get_creator"
              version="0.16">
        <doc xml:space="preserve">Returns the creator of the document. If the document was converted
from another format, the creator is the name of the product
that created the original document from which it was converted.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new allocated string containing the creator
              of @document, or %NULL</doc>
          <type name="utf8" c:type="gchar*"/>
        </return-value>
        <parameters>
          <instance-parameter name="document" transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerDocument</doc>
            <type name="Document" c:type="PopplerDocument*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_form_field"
              c:identifier="poppler_document_get_form_field">
        <doc xml:space="preserve">Returns the #PopplerFormField for the given @id. It must be freed with
g_object_unref()</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new #PopplerFormField or %NULL if
not found</doc>
          <type name="FormField" c:type="PopplerFormField*"/>
        </return-value>
        <parameters>
          <instance-parameter name="document" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerDocument</doc>
            <type name="Document" c:type="PopplerDocument*"/>
          </instance-parameter>
          <parameter name="id" transfer-ownership="none">
            <doc xml:space="preserve">an id of a #PopplerFormField</doc>
            <type name="gint" c:type="gint"/>
          </parameter>
        </parameters>
      </method>
      <method name="get_id"
              c:identifier="poppler_document_get_id"
              version="0.16">
        <doc xml:space="preserve">Returns the PDF file identifier represented as two byte string arrays of size 32.
@permanent_id is the permanent identifier that is built based on the file
contents at the time it was originally created, so that this identifer
never changes. @update_id is the update identifier that is built based on
the file contents at the time it was last updated.

Note that returned strings are not null-terminated, they have a fixed
size of 32 bytes.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">%TRUE if the @document contains an id, %FALSE otherwise</doc>
          <type name="gboolean" c:type="gboolean"/>
        </return-value>
        <parameters>
          <instance-parameter name="document" transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerDocument</doc>
            <type name="Document" c:type="PopplerDocument*"/>
          </instance-parameter>
          <parameter name="permanent_id"
                     direction="out"
                     caller-allocates="0"
                     transfer-ownership="full"
                     optional="1"
                     allow-none="1">
            <doc xml:space="preserve">location to store an allocated string, use g_free() to free the returned string</doc>
            <type name="utf8" c:type="gchar**"/>
          </parameter>
          <parameter name="update_id"
                     direction="out"
                     caller-allocates="0"
                     transfer-ownership="full"
                     optional="1"
                     allow-none="1">
            <doc xml:space="preserve">location to store an allocated string, use g_free() to free the returned string</doc>
            <type name="utf8" c:type="gchar**"/>
          </parameter>
        </parameters>
      </method>
      <method name="get_keywords"
              c:identifier="poppler_document_get_keywords"
              version="0.16">
        <doc xml:space="preserve">Returns the keywords associated to the document</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new allocated string containing keywords associated
              to @document, or %NULL</doc>
          <type name="utf8" c:type="gchar*"/>
        </return-value>
        <parameters>
          <instance-parameter name="document" transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerDocument</doc>
            <type name="Document" c:type="PopplerDocument*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_metadata"
              c:identifier="poppler_document_get_metadata"
              version="0.16">
        <doc xml:space="preserve">Returns the XML metadata string of the document</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new allocated string containing the XML
              metadata, or %NULL</doc>
          <type name="utf8" c:type="gchar*"/>
        </return-value>
        <parameters>
          <instance-parameter name="document" transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerDocument</doc>
            <type name="Document" c:type="PopplerDocument*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_modification_date"
              c:identifier="poppler_document_get_modification_date"
              version="0.16">
        <doc xml:space="preserve">Returns the date the document was most recently modified as seconds since the Epoch</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">the date the document was most recently modified, or -1</doc>
          <type name="glong" c:type="time_t"/>
        </return-value>
        <parameters>
          <instance-parameter name="document" transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerDocument</doc>
            <type name="Document" c:type="PopplerDocument*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_n_attachments"
              c:identifier="poppler_document_get_n_attachments"
              version="0.18">
        <doc xml:space="preserve">Returns the number of attachments in a loaded document.
See also poppler_document_get_attachments()</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">Number of attachments</doc>
          <type name="guint" c:type="guint"/>
        </return-value>
        <parameters>
          <instance-parameter name="document" transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerDocument</doc>
            <type name="Document" c:type="PopplerDocument*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_n_pages" c:identifier="poppler_document_get_n_pages">
        <doc xml:space="preserve">Returns the number of pages in a loaded document.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">Number of pages</doc>
          <type name="gint" c:type="int"/>
        </return-value>
        <parameters>
          <instance-parameter name="document" transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerDocument</doc>
            <type name="Document" c:type="PopplerDocument*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_page" c:identifier="poppler_document_get_page">
        <doc xml:space="preserve">Returns the #PopplerPage indexed at @index.  This object is owned by the
caller.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">The #PopplerPage at @index</doc>
          <type name="Page" c:type="PopplerPage*"/>
        </return-value>
        <parameters>
          <instance-parameter name="document" transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerDocument</doc>
            <type name="Document" c:type="PopplerDocument*"/>
          </instance-parameter>
          <parameter name="index" transfer-ownership="none">
            <doc xml:space="preserve">a page index</doc>
            <type name="gint" c:type="int"/>
          </parameter>
        </parameters>
      </method>
      <method name="get_page_by_label"
              c:identifier="poppler_document_get_page_by_label">
        <doc xml:space="preserve">Returns the #PopplerPage reference by @label.  This object is owned by the
caller.  @label is a human-readable string representation of the page number,
and can be document specific.  Typically, it is a value such as "iii" or "3".

By default, "1" refers to the first page.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">The #PopplerPage referenced by @label</doc>
          <type name="Page" c:type="PopplerPage*"/>
        </return-value>
        <parameters>
          <instance-parameter name="document" transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerDocument</doc>
            <type name="Document" c:type="PopplerDocument*"/>
          </instance-parameter>
          <parameter name="label" transfer-ownership="none">
            <doc xml:space="preserve">a page label</doc>
            <type name="utf8" c:type="const char*"/>
          </parameter>
        </parameters>
      </method>
      <method name="get_page_layout"
              c:identifier="poppler_document_get_page_layout"
              version="0.16">
        <doc xml:space="preserve">Returns the page layout that should be used when the document is opened</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">a #PopplerPageLayout that should be used when the document is opened</doc>
          <type name="PageLayout" c:type="PopplerPageLayout"/>
        </return-value>
        <parameters>
          <instance-parameter name="document" transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerDocument</doc>
            <type name="Document" c:type="PopplerDocument*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_page_mode"
              c:identifier="poppler_document_get_page_mode"
              version="0.16">
        <doc xml:space="preserve">Returns a #PopplerPageMode representing how the document should
be initially displayed when opened.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">a #PopplerPageMode that should be used when document is opened</doc>
          <type name="PageMode" c:type="PopplerPageMode"/>
        </return-value>
        <parameters>
          <instance-parameter name="document" transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerDocument</doc>
            <type name="Document" c:type="PopplerDocument*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_pdf_version"
              c:identifier="poppler_document_get_pdf_version"
              version="0.16">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="document" transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerDocument</doc>
            <type name="Document" c:type="PopplerDocument*"/>
          </instance-parameter>
          <parameter name="major_version"
                     direction="out"
                     caller-allocates="0"
                     transfer-ownership="full"
                     optional="1"
                     allow-none="1">
            <doc xml:space="preserve">return location for the PDF major version number</doc>
            <type name="guint" c:type="guint*"/>
          </parameter>
          <parameter name="minor_version"
                     direction="out"
                     caller-allocates="0"
                     transfer-ownership="full"
                     optional="1"
                     allow-none="1">
            <doc xml:space="preserve">return location for the PDF minor version number</doc>
            <type name="guint" c:type="guint*"/>
          </parameter>
        </parameters>
      </method>
      <method name="get_pdf_version_string"
              c:identifier="poppler_document_get_pdf_version_string"
              version="0.16">
        <doc xml:space="preserve">Returns the PDF version of @document as a string (e.g. PDF-1.6)</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new allocated string containing the PDF version
              of @document, or %NULL</doc>
          <type name="utf8" c:type="gchar*"/>
        </return-value>
        <parameters>
          <instance-parameter name="document" transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerDocument</doc>
            <type name="Document" c:type="PopplerDocument*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_permissions"
              c:identifier="poppler_document_get_permissions"
              version="0.16">
        <doc xml:space="preserve">Returns the flags specifying which operations are permitted when the document is opened.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">a set of falgs from  #PopplerPermissions enumeration</doc>
          <type name="Permissions" c:type="PopplerPermissions"/>
        </return-value>
        <parameters>
          <instance-parameter name="document" transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerDocument</doc>
            <type name="Document" c:type="PopplerDocument*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_producer"
              c:identifier="poppler_document_get_producer"
              version="0.16">
        <doc xml:space="preserve">Returns the producer of the document. If the document was converted
from another format, the producer is the name of the product
that converted it to PDF</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new allocated string containing the producer
              of @document, or %NULL</doc>
          <type name="utf8" c:type="gchar*"/>
        </return-value>
        <parameters>
          <instance-parameter name="document" transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerDocument</doc>
            <type name="Document" c:type="PopplerDocument*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_subject"
              c:identifier="poppler_document_get_subject"
              version="0.16">
        <doc xml:space="preserve">Returns the subject of the document</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new allocated string containing the subject
              of @document, or %NULL</doc>
          <type name="utf8" c:type="gchar*"/>
        </return-value>
        <parameters>
          <instance-parameter name="document" transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerDocument</doc>
            <type name="Document" c:type="PopplerDocument*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_title"
              c:identifier="poppler_document_get_title"
              version="0.16">
        <doc xml:space="preserve">Returns the document's title</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new allocated string containing the title
              of @document, or %NULL</doc>
          <type name="utf8" c:type="gchar*"/>
        </return-value>
        <parameters>
          <instance-parameter name="document" transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerDocument</doc>
            <type name="Document" c:type="PopplerDocument*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="has_attachments"
              c:identifier="poppler_document_has_attachments">
        <doc xml:space="preserve">Returns %TRUE of @document has any attachments.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">%TRUE, if @document has attachments.</doc>
          <type name="gboolean" c:type="gboolean"/>
        </return-value>
        <parameters>
          <instance-parameter name="document" transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerDocument</doc>
            <type name="Document" c:type="PopplerDocument*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="is_linearized"
              c:identifier="poppler_document_is_linearized"
              version="0.16">
        <doc xml:space="preserve">Returns whether @document is linearized or not. Linearization of PDF
enables efficient incremental access of the PDF file in a network environment.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">%TRUE if @document is linearized, %FALSE otherwhise</doc>
          <type name="gboolean" c:type="gboolean"/>
        </return-value>
        <parameters>
          <instance-parameter name="document" transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerDocument</doc>
            <type name="Document" c:type="PopplerDocument*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="save" c:identifier="poppler_document_save" throws="1">
        <doc xml:space="preserve">Saves @document. Any change made in the document such as
form fields filled, annotations added or modified
will be saved.
If @error is set, %FALSE will be returned. Possible errors
include those in the #G_FILE_ERROR domain.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">%TRUE, if the document was successfully saved</doc>
          <type name="gboolean" c:type="gboolean"/>
        </return-value>
        <parameters>
          <instance-parameter name="document" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerDocument</doc>
            <type name="Document" c:type="PopplerDocument*"/>
          </instance-parameter>
          <parameter name="uri" transfer-ownership="none">
            <doc xml:space="preserve">uri of file to save</doc>
            <type name="utf8" c:type="const char*"/>
          </parameter>
        </parameters>
      </method>
      <method name="save_a_copy"
              c:identifier="poppler_document_save_a_copy"
              throws="1">
        <doc xml:space="preserve">Saves a copy of the original @document.
Any change made in the document such as
form fields filled by the user will not be saved.
If @error is set, %FALSE will be returned. Possible errors
include those in the #G_FILE_ERROR domain.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">%TRUE, if the document was successfully saved</doc>
          <type name="gboolean" c:type="gboolean"/>
        </return-value>
        <parameters>
          <instance-parameter name="document" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerDocument</doc>
            <type name="Document" c:type="PopplerDocument*"/>
          </instance-parameter>
          <parameter name="uri" transfer-ownership="none">
            <doc xml:space="preserve">uri of file to save</doc>
            <type name="utf8" c:type="const char*"/>
          </parameter>
        </parameters>
      </method>
      <method name="set_author"
              c:identifier="poppler_document_set_author"
              version="0.46">
        <doc xml:space="preserve">Sets the document's author. If <AUTHOR> %NULL, Author
entry is removed from the document's Info dictionary.</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="document" transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerDocument</doc>
            <type name="Document" c:type="PopplerDocument*"/>
          </instance-parameter>
          <parameter name="author" transfer-ownership="none">
            <doc xml:space="preserve">A new author</doc>
            <type name="utf8" c:type="const gchar*"/>
          </parameter>
        </parameters>
      </method>
      <method name="set_creation_date"
              c:identifier="poppler_document_set_creation_date"
              version="0.46">
        <doc xml:space="preserve">Sets the document's creation date. If @creation_date is -1, CreationDate
entry is removed from the document's Info dictionary.</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="document" transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerDocument</doc>
            <type name="Document" c:type="PopplerDocument*"/>
          </instance-parameter>
          <parameter name="creation_date" transfer-ownership="none">
            <doc xml:space="preserve">A new creation date</doc>
            <type name="glong" c:type="time_t"/>
          </parameter>
        </parameters>
      </method>
      <method name="set_creator"
              c:identifier="poppler_document_set_creator"
              version="0.46">
        <doc xml:space="preserve">Sets the document's creator. If @creator is %NULL, Creator
entry is removed from the document's Info dictionary.</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="document" transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerDocument</doc>
            <type name="Document" c:type="PopplerDocument*"/>
          </instance-parameter>
          <parameter name="creator" transfer-ownership="none">
            <doc xml:space="preserve">A new creator</doc>
            <type name="utf8" c:type="const gchar*"/>
          </parameter>
        </parameters>
      </method>
      <method name="set_keywords"
              c:identifier="poppler_document_set_keywords"
              version="0.46">
        <doc xml:space="preserve">Sets the document's keywords. If @keywords is %NULL,
Keywords entry is removed from the document's Info dictionary.</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="document" transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerDocument</doc>
            <type name="Document" c:type="PopplerDocument*"/>
          </instance-parameter>
          <parameter name="keywords" transfer-ownership="none">
            <doc xml:space="preserve">New keywords</doc>
            <type name="utf8" c:type="const gchar*"/>
          </parameter>
        </parameters>
      </method>
      <method name="set_modification_date"
              c:identifier="poppler_document_set_modification_date"
              version="0.46">
        <doc xml:space="preserve">Sets the document's modification date. If @modification_date is -1, ModDate
entry is removed from the document's Info dictionary.</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="document" transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerDocument</doc>
            <type name="Document" c:type="PopplerDocument*"/>
          </instance-parameter>
          <parameter name="modification_date" transfer-ownership="none">
            <doc xml:space="preserve">A new modification date</doc>
            <type name="glong" c:type="time_t"/>
          </parameter>
        </parameters>
      </method>
      <method name="set_producer"
              c:identifier="poppler_document_set_producer"
              version="0.46">
        <doc xml:space="preserve">Sets the document's producer. If @producer is %NULL,
Producer entry is removed from the document's Info dictionary.</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="document" transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerDocument</doc>
            <type name="Document" c:type="PopplerDocument*"/>
          </instance-parameter>
          <parameter name="producer" transfer-ownership="none">
            <doc xml:space="preserve">A new producer</doc>
            <type name="utf8" c:type="const gchar*"/>
          </parameter>
        </parameters>
      </method>
      <method name="set_subject"
              c:identifier="poppler_document_set_subject"
              version="0.46">
        <doc xml:space="preserve">Sets the document's subject. If @subject is %NULL, Subject
entry is removed from the document's Info dictionary.</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="document" transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerDocument</doc>
            <type name="Document" c:type="PopplerDocument*"/>
          </instance-parameter>
          <parameter name="subject" transfer-ownership="none">
            <doc xml:space="preserve">A new subject</doc>
            <type name="utf8" c:type="const gchar*"/>
          </parameter>
        </parameters>
      </method>
      <method name="set_title"
              c:identifier="poppler_document_set_title"
              version="0.46">
        <doc xml:space="preserve">Sets the document's title. If @title is %NULL, Title entry
is removed from the document's Info dictionary.</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="document" transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerDocument</doc>
            <type name="Document" c:type="PopplerDocument*"/>
          </instance-parameter>
          <parameter name="title" transfer-ownership="none">
            <doc xml:space="preserve">A new title</doc>
            <type name="utf8" c:type="const gchar*"/>
          </parameter>
        </parameters>
      </method>
      <property name="author" writable="1" transfer-ownership="none">
        <doc xml:space="preserve">The author of the document</doc>
        <type name="utf8" c:type="gchar*"/>
      </property>
      <property name="creation-date" writable="1" transfer-ownership="none">
        <doc xml:space="preserve">The date the document was created as seconds since the Epoch, or -1</doc>
        <type name="gint" c:type="gint"/>
      </property>
      <property name="creator" writable="1" transfer-ownership="none">
        <doc xml:space="preserve">The creator of the document. See also poppler_document_get_creator()</doc>
        <type name="utf8" c:type="gchar*"/>
      </property>
      <property name="format" transfer-ownership="none">
        <doc xml:space="preserve">The PDF version as string. See also poppler_document_get_pdf_version_string()</doc>
        <type name="utf8" c:type="gchar*"/>
      </property>
      <property name="format-major" transfer-ownership="none">
        <doc xml:space="preserve">The PDF major version number. See also poppler_document_get_pdf_version()</doc>
        <type name="guint" c:type="guint"/>
      </property>
      <property name="format-minor" transfer-ownership="none">
        <doc xml:space="preserve">The PDF minor version number. See also poppler_document_get_pdf_version()</doc>
        <type name="guint" c:type="guint"/>
      </property>
      <property name="keywords" writable="1" transfer-ownership="none">
        <doc xml:space="preserve">The keywords associated to the document</doc>
        <type name="utf8" c:type="gchar*"/>
      </property>
      <property name="linearized" transfer-ownership="none">
        <doc xml:space="preserve">Whether document is linearized. See also poppler_document_is_linearized()</doc>
        <type name="gboolean" c:type="gboolean"/>
      </property>
      <property name="metadata" transfer-ownership="none">
        <doc xml:space="preserve">Document metadata in XML format, or %NULL</doc>
        <type name="utf8" c:type="gchar*"/>
      </property>
      <property name="mod-date" writable="1" transfer-ownership="none">
        <doc xml:space="preserve">The date the document was most recently modified as seconds since the Epoch, or -1</doc>
        <type name="gint" c:type="gint"/>
      </property>
      <property name="page-layout" transfer-ownership="none">
        <doc xml:space="preserve">The page layout that should be used when the document is opened</doc>
        <type name="PageLayout"/>
      </property>
      <property name="page-mode" transfer-ownership="none">
        <doc xml:space="preserve">The mode that should be used when the document is opened</doc>
        <type name="PageMode"/>
      </property>
      <property name="permissions" transfer-ownership="none">
        <doc xml:space="preserve">Flags specifying which operations are permitted when the document is opened</doc>
        <type name="Permissions"/>
      </property>
      <property name="producer" writable="1" transfer-ownership="none">
        <doc xml:space="preserve">The producer of the document. See also poppler_document_get_producer()</doc>
        <type name="utf8" c:type="gchar*"/>
      </property>
      <property name="subject" writable="1" transfer-ownership="none">
        <doc xml:space="preserve">The subject of the document</doc>
        <type name="utf8" c:type="gchar*"/>
      </property>
      <property name="title" writable="1" transfer-ownership="none">
        <doc xml:space="preserve">The document's title or %NULL</doc>
        <type name="utf8" c:type="gchar*"/>
      </property>
      <property name="viewer-preferences" transfer-ownership="none">
        <type name="ViewerPreferences"/>
      </property>
    </class>
    <enumeration name="Error"
                 glib:type-name="PopplerError"
                 glib:get-type="poppler_error_get_type"
                 c:type="PopplerError"
                 glib:error-domain="poppler-quark">
      <doc xml:space="preserve">Error codes returned by #PopplerDocument</doc>
      <member name="invalid"
              value="0"
              c:identifier="POPPLER_ERROR_INVALID"
              glib:nick="invalid">
        <doc xml:space="preserve">Generic error when a document operation fails</doc>
      </member>
      <member name="encrypted"
              value="1"
              c:identifier="POPPLER_ERROR_ENCRYPTED"
              glib:nick="encrypted">
        <doc xml:space="preserve">Document is encrypted</doc>
      </member>
      <member name="open_file"
              value="2"
              c:identifier="POPPLER_ERROR_OPEN_FILE"
              glib:nick="open-file">
        <doc xml:space="preserve">File could not be opened for writing when saving document</doc>
      </member>
      <member name="bad_catalog"
              value="3"
              c:identifier="POPPLER_ERROR_BAD_CATALOG"
              glib:nick="bad-catalog">
        <doc xml:space="preserve">Failed to read the document catalog</doc>
      </member>
      <member name="damaged"
              value="4"
              c:identifier="POPPLER_ERROR_DAMAGED"
              glib:nick="damaged">
        <doc xml:space="preserve">Document is damaged</doc>
      </member>
      <function name="quark" c:identifier="poppler_error_quark">
        <return-value transfer-ownership="none">
          <type name="GLib.Quark" c:type="GQuark"/>
        </return-value>
      </function>
    </enumeration>
    <bitfield name="FindFlags"
              version="0.22"
              glib:type-name="PopplerFindFlags"
              glib:get-type="poppler_find_flags_get_type"
              c:type="PopplerFindFlags">
      <doc xml:space="preserve">Flags using while searching text in a page</doc>
      <member name="default"
              value="0"
              c:identifier="POPPLER_FIND_DEFAULT"
              glib:nick="default">
        <doc xml:space="preserve">use default search settings</doc>
      </member>
      <member name="case_sensitive"
              value="1"
              c:identifier="POPPLER_FIND_CASE_SENSITIVE"
              glib:nick="case-sensitive">
        <doc xml:space="preserve">do case sensitive search</doc>
      </member>
      <member name="backwards"
              value="2"
              c:identifier="POPPLER_FIND_BACKWARDS"
              glib:nick="backwards">
        <doc xml:space="preserve">search backwards</doc>
      </member>
      <member name="whole_words_only"
              value="4"
              c:identifier="POPPLER_FIND_WHOLE_WORDS_ONLY"
              glib:nick="whole-words-only">
        <doc xml:space="preserve">search only whole words</doc>
      </member>
    </bitfield>
    <class name="FontInfo"
           c:symbol-prefix="font_info"
           c:type="PopplerFontInfo"
           parent="GObject.Object"
           glib:type-name="PopplerFontInfo"
           glib:get-type="poppler_font_info_get_type">
      <constructor name="new" c:identifier="poppler_font_info_new">
        <doc xml:space="preserve">Creates a new #PopplerFontInfo object</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new #PopplerFontInfo instance</doc>
          <type name="FontInfo" c:type="PopplerFontInfo*"/>
        </return-value>
        <parameters>
          <parameter name="document" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerDocument</doc>
            <type name="Document" c:type="PopplerDocument*"/>
          </parameter>
        </parameters>
      </constructor>
      <method name="free" c:identifier="poppler_font_info_free">
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="font_info" transfer-ownership="none">
            <type name="FontInfo" c:type="PopplerFontInfo*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="scan" c:identifier="poppler_font_info_scan">
        <doc xml:space="preserve">Scans the document associated with @font_info for fonts. At most
@n_pages will be scanned starting from the current iterator. @iter will
point to the first font scanned.

Here is a simple example of code to scan fonts in a document

&lt;informalexample&gt;&lt;programlisting&gt;
font_info = poppler_font_info_new (document);
while (poppler_font_info_scan (font_info, 20, &amp;fonts_iter)) {
        if (!fonts_iter)
                continue; /&lt;!-- --&gt;* No fonts found in these 20 pages *&lt;!-- --&gt;/
        do {
                /&lt;!-- --&gt;* Do something with font iter *&lt;!-- --&gt;/
                g_print ("Font Name: %s\n", poppler_fonts_iter_get_name (fonts_iter));
        } while (poppler_fonts_iter_next (fonts_iter));
        poppler_fonts_iter_free (fonts_iter);
}
&lt;/programlisting&gt;&lt;/informalexample&gt;</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">%TRUE, if there are more fonts left to scan</doc>
          <type name="gboolean" c:type="gboolean"/>
        </return-value>
        <parameters>
          <instance-parameter name="font_info" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerFontInfo</doc>
            <type name="FontInfo" c:type="PopplerFontInfo*"/>
          </instance-parameter>
          <parameter name="n_pages" transfer-ownership="none">
            <doc xml:space="preserve">number of pages to scan</doc>
            <type name="gint" c:type="int"/>
          </parameter>
          <parameter name="iter"
                     direction="out"
                     caller-allocates="0"
                     transfer-ownership="full">
            <doc xml:space="preserve">return location for a #PopplerFontsIter</doc>
            <type name="FontsIter" c:type="PopplerFontsIter**"/>
          </parameter>
        </parameters>
      </method>
    </class>
    <enumeration name="FontType"
                 glib:type-name="PopplerFontType"
                 glib:get-type="poppler_font_type_get_type"
                 c:type="PopplerFontType">
      <doc xml:space="preserve">Font types</doc>
      <member name="unknown"
              value="0"
              c:identifier="POPPLER_FONT_TYPE_UNKNOWN"
              glib:nick="unknown">
        <doc xml:space="preserve">unknown font type</doc>
      </member>
      <member name="type1"
              value="1"
              c:identifier="POPPLER_FONT_TYPE_TYPE1"
              glib:nick="type1">
        <doc xml:space="preserve">Type 1 font type</doc>
      </member>
      <member name="type1c"
              value="2"
              c:identifier="POPPLER_FONT_TYPE_TYPE1C"
              glib:nick="type1c">
        <doc xml:space="preserve">Type 1 font type embedded in Compact Font Format (CFF) font program</doc>
      </member>
      <member name="type1cot"
              value="3"
              c:identifier="POPPLER_FONT_TYPE_TYPE1COT"
              glib:nick="type1cot">
        <doc xml:space="preserve">Type 1 font type embedded in OpenType font program</doc>
      </member>
      <member name="type3"
              value="4"
              c:identifier="POPPLER_FONT_TYPE_TYPE3"
              glib:nick="type3">
        <doc xml:space="preserve">A font type that is defined with PDF graphics operators</doc>
      </member>
      <member name="truetype"
              value="5"
              c:identifier="POPPLER_FONT_TYPE_TRUETYPE"
              glib:nick="truetype">
        <doc xml:space="preserve">TrueType font type</doc>
      </member>
      <member name="truetypeot"
              value="6"
              c:identifier="POPPLER_FONT_TYPE_TRUETYPEOT"
              glib:nick="truetypeot">
        <doc xml:space="preserve">TrueType font type embedded in OpenType font program</doc>
      </member>
      <member name="cid_type0"
              value="7"
              c:identifier="POPPLER_FONT_TYPE_CID_TYPE0"
              glib:nick="cid-type0">
        <doc xml:space="preserve">CIDFont type based on Type 1 font technology</doc>
      </member>
      <member name="cid_type0c"
              value="8"
              c:identifier="POPPLER_FONT_TYPE_CID_TYPE0C"
              glib:nick="cid-type0c">
        <doc xml:space="preserve">CIDFont type based on Type 1 font technology embedded in CFF font program</doc>
      </member>
      <member name="cid_type0cot"
              value="9"
              c:identifier="POPPLER_FONT_TYPE_CID_TYPE0COT"
              glib:nick="cid-type0cot">
        <doc xml:space="preserve">CIDFont type based on Type 1 font technology embedded in OpenType font program</doc>
      </member>
      <member name="cid_type2"
              value="10"
              c:identifier="POPPLER_FONT_TYPE_CID_TYPE2"
              glib:nick="cid-type2">
        <doc xml:space="preserve">CIDFont type based on TrueType font technology</doc>
      </member>
      <member name="cid_type2ot"
              value="11"
              c:identifier="POPPLER_FONT_TYPE_CID_TYPE2OT"
              glib:nick="cid-type2ot">
        <doc xml:space="preserve">CIDFont type based on TrueType font technology embedded in OpenType font program</doc>
      </member>
    </enumeration>
    <record name="FontsIter"
            c:type="PopplerFontsIter"
            glib:type-name="PopplerFontsIter"
            glib:get-type="poppler_fonts_iter_get_type"
            c:symbol-prefix="fonts_iter">
      <method name="copy" c:identifier="poppler_fonts_iter_copy">
        <doc xml:space="preserve">Creates a copy of @iter</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new allocated copy of @iter</doc>
          <type name="FontsIter" c:type="PopplerFontsIter*"/>
        </return-value>
        <parameters>
          <instance-parameter name="iter" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerFontsIter to copy</doc>
            <type name="FontsIter" c:type="PopplerFontsIter*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="free" c:identifier="poppler_fonts_iter_free">
        <doc xml:space="preserve">Frees the given #PopplerFontsIter</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="iter" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerFontsIter</doc>
            <type name="FontsIter" c:type="PopplerFontsIter*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_encoding"
              c:identifier="poppler_fonts_iter_get_encoding"
              version="0.20">
        <doc xml:space="preserve">Returns the encoding of the font associated with @iter</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">the font encoding</doc>
          <type name="utf8" c:type="const char*"/>
        </return-value>
        <parameters>
          <instance-parameter name="iter" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerFontsIter</doc>
            <type name="FontsIter" c:type="PopplerFontsIter*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_file_name"
              c:identifier="poppler_fonts_iter_get_file_name">
        <doc xml:space="preserve">The filename of the font associated with @iter or %NULL if
the font is embedded</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">the filename of the font or %NULL if font is embedded</doc>
          <type name="utf8" c:type="const char*"/>
        </return-value>
        <parameters>
          <instance-parameter name="iter" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerFontsIter</doc>
            <type name="FontsIter" c:type="PopplerFontsIter*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_font_type"
              c:identifier="poppler_fonts_iter_get_font_type">
        <doc xml:space="preserve">Returns the type of the font associated with @iter</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">the font type</doc>
          <type name="FontType" c:type="PopplerFontType"/>
        </return-value>
        <parameters>
          <instance-parameter name="iter" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerFontsIter</doc>
            <type name="FontsIter" c:type="PopplerFontsIter*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_full_name"
              c:identifier="poppler_fonts_iter_get_full_name">
        <doc xml:space="preserve">Returns the full name of the font associated with @iter</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">the font full name</doc>
          <type name="utf8" c:type="const char*"/>
        </return-value>
        <parameters>
          <instance-parameter name="iter" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerFontsIter</doc>
            <type name="FontsIter" c:type="PopplerFontsIter*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_name" c:identifier="poppler_fonts_iter_get_name">
        <doc xml:space="preserve">Returns the name of the font associated with @iter</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">the font name</doc>
          <type name="utf8" c:type="const char*"/>
        </return-value>
        <parameters>
          <instance-parameter name="iter" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerFontsIter</doc>
            <type name="FontsIter" c:type="PopplerFontsIter*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_substitute_name"
              c:identifier="poppler_fonts_iter_get_substitute_name"
              version="0.20">
        <doc xml:space="preserve">The name of the substitute font of the font associated with @iter or %NULL if
the font is embedded</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">the name of the substitute font or %NULL if font is embedded</doc>
          <type name="utf8" c:type="const char*"/>
        </return-value>
        <parameters>
          <instance-parameter name="iter" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerFontsIter</doc>
            <type name="FontsIter" c:type="PopplerFontsIter*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="is_embedded" c:identifier="poppler_fonts_iter_is_embedded">
        <doc xml:space="preserve">Returns whether the font associated with @iter is embedded in the document</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">%TRUE if font is emebdded, %FALSE otherwise</doc>
          <type name="gboolean" c:type="gboolean"/>
        </return-value>
        <parameters>
          <instance-parameter name="iter" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerFontsIter</doc>
            <type name="FontsIter" c:type="PopplerFontsIter*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="is_subset" c:identifier="poppler_fonts_iter_is_subset">
        <doc xml:space="preserve">Returns whether the font associated with @iter is a subset of another font</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">%TRUE if font is a subset, %FALSE otherwise</doc>
          <type name="gboolean" c:type="gboolean"/>
        </return-value>
        <parameters>
          <instance-parameter name="iter" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerFontsIter</doc>
            <type name="FontsIter" c:type="PopplerFontsIter*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="next" c:identifier="poppler_fonts_iter_next">
        <doc xml:space="preserve">Sets @iter to point to the next font</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">%TRUE, if @iter was set to the next font</doc>
          <type name="gboolean" c:type="gboolean"/>
        </return-value>
        <parameters>
          <instance-parameter name="iter" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerFontsIter</doc>
            <type name="FontsIter" c:type="PopplerFontsIter*"/>
          </instance-parameter>
        </parameters>
      </method>
    </record>
    <enumeration name="FormButtonType"
                 glib:type-name="PopplerFormButtonType"
                 glib:get-type="poppler_form_button_type_get_type"
                 c:type="PopplerFormButtonType">
      <member name="push"
              value="0"
              c:identifier="POPPLER_FORM_BUTTON_PUSH"
              glib:nick="push">
      </member>
      <member name="check"
              value="1"
              c:identifier="POPPLER_FORM_BUTTON_CHECK"
              glib:nick="check">
      </member>
      <member name="radio"
              value="2"
              c:identifier="POPPLER_FORM_BUTTON_RADIO"
              glib:nick="radio">
      </member>
    </enumeration>
    <enumeration name="FormChoiceType"
                 glib:type-name="PopplerFormChoiceType"
                 glib:get-type="poppler_form_choice_type_get_type"
                 c:type="PopplerFormChoiceType">
      <member name="combo"
              value="0"
              c:identifier="POPPLER_FORM_CHOICE_COMBO"
              glib:nick="combo">
      </member>
      <member name="list"
              value="1"
              c:identifier="POPPLER_FORM_CHOICE_LIST"
              glib:nick="list">
      </member>
    </enumeration>
    <class name="FormField"
           c:symbol-prefix="form_field"
           c:type="PopplerFormField"
           parent="GObject.Object"
           glib:type-name="PopplerFormField"
           glib:get-type="poppler_form_field_get_type">
      <method name="button_get_button_type"
              c:identifier="poppler_form_field_button_get_button_type">
        <doc xml:space="preserve">Gets the button type of @field</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">#PopplerFormButtonType of @field</doc>
          <type name="FormButtonType" c:type="PopplerFormButtonType"/>
        </return-value>
        <parameters>
          <instance-parameter name="field" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerFormField</doc>
            <type name="FormField" c:type="PopplerFormField*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="button_get_state"
              c:identifier="poppler_form_field_button_get_state">
        <doc xml:space="preserve">Queries a #PopplerFormField and returns its current state. Returns %TRUE if
@field is pressed in and %FALSE if it is raised.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">current state of @field</doc>
          <type name="gboolean" c:type="gboolean"/>
        </return-value>
        <parameters>
          <instance-parameter name="field" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerFormField</doc>
            <type name="FormField" c:type="PopplerFormField*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="button_set_state"
              c:identifier="poppler_form_field_button_set_state">
        <doc xml:space="preserve">Sets the status of @field. Set to %TRUE if you want the #PopplerFormField
to be 'pressed in', and %FALSE to raise it.</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="field" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerFormField</doc>
            <type name="FormField" c:type="PopplerFormField*"/>
          </instance-parameter>
          <parameter name="state" transfer-ownership="none">
            <doc xml:space="preserve">%TRUE or %FALSE</doc>
            <type name="gboolean" c:type="gboolean"/>
          </parameter>
        </parameters>
      </method>
      <method name="choice_can_select_multiple"
              c:identifier="poppler_form_field_choice_can_select_multiple">
        <doc xml:space="preserve">Checks whether @field allows multiple choices to be selected</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">%TRUE if @field allows multiple choices to be selected</doc>
          <type name="gboolean" c:type="gboolean"/>
        </return-value>
        <parameters>
          <instance-parameter name="field" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerFormField</doc>
            <type name="FormField" c:type="PopplerFormField*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="choice_commit_on_change"
              c:identifier="poppler_form_field_choice_commit_on_change">
        <return-value transfer-ownership="none">
          <type name="gboolean" c:type="gboolean"/>
        </return-value>
        <parameters>
          <instance-parameter name="field" transfer-ownership="none">
            <type name="FormField" c:type="PopplerFormField*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="choice_do_spell_check"
              c:identifier="poppler_form_field_choice_do_spell_check">
        <doc xml:space="preserve">Checks whether spell checking should be done for the contents of @field</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">%TRUE if spell checking should be done for @field</doc>
          <type name="gboolean" c:type="gboolean"/>
        </return-value>
        <parameters>
          <instance-parameter name="field" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerFormField</doc>
            <type name="FormField" c:type="PopplerFormField*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="choice_get_choice_type"
              c:identifier="poppler_form_field_choice_get_choice_type">
        <doc xml:space="preserve">Gets the choice type of @field</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">#PopplerFormChoiceType of @field</doc>
          <type name="FormChoiceType" c:type="PopplerFormChoiceType"/>
        </return-value>
        <parameters>
          <instance-parameter name="field" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerFormField</doc>
            <type name="FormField" c:type="PopplerFormField*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="choice_get_item"
              c:identifier="poppler_form_field_choice_get_item">
        <doc xml:space="preserve">Returns the contents of the item on @field at the given index</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new allocated string. It must be freed with g_free() when done.</doc>
          <type name="utf8" c:type="gchar*"/>
        </return-value>
        <parameters>
          <instance-parameter name="field" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerFormField</doc>
            <type name="FormField" c:type="PopplerFormField*"/>
          </instance-parameter>
          <parameter name="index" transfer-ownership="none">
            <doc xml:space="preserve">the index of the item</doc>
            <type name="gint" c:type="gint"/>
          </parameter>
        </parameters>
      </method>
      <method name="choice_get_n_items"
              c:identifier="poppler_form_field_choice_get_n_items">
        <doc xml:space="preserve">Returns the number of items on @field</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">the number of items on @field</doc>
          <type name="gint" c:type="gint"/>
        </return-value>
        <parameters>
          <instance-parameter name="field" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerFormField</doc>
            <type name="FormField" c:type="PopplerFormField*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="choice_get_text"
              c:identifier="poppler_form_field_choice_get_text">
        <doc xml:space="preserve">Retrieves the contents of @field.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new allocated string. It must be freed with g_free() when done.</doc>
          <type name="utf8" c:type="gchar*"/>
        </return-value>
        <parameters>
          <instance-parameter name="field" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerFormField</doc>
            <type name="FormField" c:type="PopplerFormField*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="choice_is_editable"
              c:identifier="poppler_form_field_choice_is_editable">
        <doc xml:space="preserve">Checks whether @field is editable</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">%TRUE if @field is editable</doc>
          <type name="gboolean" c:type="gboolean"/>
        </return-value>
        <parameters>
          <instance-parameter name="field" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerFormField</doc>
            <type name="FormField" c:type="PopplerFormField*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="choice_is_item_selected"
              c:identifier="poppler_form_field_choice_is_item_selected">
        <doc xml:space="preserve">Checks whether the item at the given index on @field is currently selected</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">%TRUE if item at @index is currently selected</doc>
          <type name="gboolean" c:type="gboolean"/>
        </return-value>
        <parameters>
          <instance-parameter name="field" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerFormField</doc>
            <type name="FormField" c:type="PopplerFormField*"/>
          </instance-parameter>
          <parameter name="index" transfer-ownership="none">
            <doc xml:space="preserve">the index of the item</doc>
            <type name="gint" c:type="gint"/>
          </parameter>
        </parameters>
      </method>
      <method name="choice_select_item"
              c:identifier="poppler_form_field_choice_select_item">
        <doc xml:space="preserve">Selects the item at the given index on @field</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="field" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerFormField</doc>
            <type name="FormField" c:type="PopplerFormField*"/>
          </instance-parameter>
          <parameter name="index" transfer-ownership="none">
            <doc xml:space="preserve">the index of the item</doc>
            <type name="gint" c:type="gint"/>
          </parameter>
        </parameters>
      </method>
      <method name="choice_set_text"
              c:identifier="poppler_form_field_choice_set_text">
        <doc xml:space="preserve">Sets the text in @field to the given value, replacing the current contents</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="field" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerFormField</doc>
            <type name="FormField" c:type="PopplerFormField*"/>
          </instance-parameter>
          <parameter name="text" transfer-ownership="none">
            <doc xml:space="preserve">the new text</doc>
            <type name="utf8" c:type="const gchar*"/>
          </parameter>
        </parameters>
      </method>
      <method name="choice_toggle_item"
              c:identifier="poppler_form_field_choice_toggle_item">
        <doc xml:space="preserve">Changes the state of the item at the given index</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="field" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerFormField</doc>
            <type name="FormField" c:type="PopplerFormField*"/>
          </instance-parameter>
          <parameter name="index" transfer-ownership="none">
            <doc xml:space="preserve">the index of the item</doc>
            <type name="gint" c:type="gint"/>
          </parameter>
        </parameters>
      </method>
      <method name="choice_unselect_all"
              c:identifier="poppler_form_field_choice_unselect_all">
        <doc xml:space="preserve">Unselects all the items on @field</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="field" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerFormField</doc>
            <type name="FormField" c:type="PopplerFormField*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_action"
              c:identifier="poppler_form_field_get_action"
              version="0.18">
        <doc xml:space="preserve">Retrieves the action (#PopplerAction) that shall be
performed when @field is activated, or %NULL</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">the action to perform. The returned
              object is owned by @field and should not be freed</doc>
          <type name="Action" c:type="PopplerAction*"/>
        </return-value>
        <parameters>
          <instance-parameter name="field" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerFormField</doc>
            <type name="FormField" c:type="PopplerFormField*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_field_type"
              c:identifier="poppler_form_field_get_field_type">
        <doc xml:space="preserve">Gets the type of @field</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">#PopplerFormFieldType of @field</doc>
          <type name="FormFieldType" c:type="PopplerFormFieldType"/>
        </return-value>
        <parameters>
          <instance-parameter name="field" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerFormField</doc>
            <type name="FormField" c:type="PopplerFormField*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_font_size"
              c:identifier="poppler_form_field_get_font_size">
        <doc xml:space="preserve">Gets the font size of @field

WARNING: This function always returns 0. Contact the poppler
mailing list if you're interested in implementing it properly</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">the font size of @field</doc>
          <type name="gdouble" c:type="gdouble"/>
        </return-value>
        <parameters>
          <instance-parameter name="field" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerFormField</doc>
            <type name="FormField" c:type="PopplerFormField*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_id" c:identifier="poppler_form_field_get_id">
        <doc xml:space="preserve">Gets the id of @field</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">the id of @field</doc>
          <type name="gint" c:type="gint"/>
        </return-value>
        <parameters>
          <instance-parameter name="field" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerFormField</doc>
            <type name="FormField" c:type="PopplerFormField*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_mapping_name"
              c:identifier="poppler_form_field_get_mapping_name"
              version="0.16">
        <doc xml:space="preserve">Gets the mapping name of @field that is used when
exporting interactive form field data from the document</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new allocated string. It must be freed with g_free() when done.</doc>
          <type name="utf8" c:type="gchar*"/>
        </return-value>
        <parameters>
          <instance-parameter name="field" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerFormField</doc>
            <type name="FormField" c:type="PopplerFormField*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_name"
              c:identifier="poppler_form_field_get_name"
              version="0.16">
        <doc xml:space="preserve">Gets the fully qualified name of @field. It's constructed by concatenating
the partial field names of the field and all of its ancestors.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new allocated string. It must be freed with g_free() when done.</doc>
          <type name="utf8" c:type="gchar*"/>
        </return-value>
        <parameters>
          <instance-parameter name="field" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerFormField</doc>
            <type name="FormField" c:type="PopplerFormField*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_partial_name"
              c:identifier="poppler_form_field_get_partial_name"
              version="0.16">
        <doc xml:space="preserve">Gets the partial name of @field.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new allocated string. It must be freed with g_free() when done.</doc>
          <type name="utf8" c:type="gchar*"/>
        </return-value>
        <parameters>
          <instance-parameter name="field" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerFormField</doc>
            <type name="FormField" c:type="PopplerFormField*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="is_read_only"
              c:identifier="poppler_form_field_is_read_only">
        <doc xml:space="preserve">Checks whether @field is read only</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">%TRUE if @field is read only</doc>
          <type name="gboolean" c:type="gboolean"/>
        </return-value>
        <parameters>
          <instance-parameter name="field" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerFormField</doc>
            <type name="FormField" c:type="PopplerFormField*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="text_do_scroll"
              c:identifier="poppler_form_field_text_do_scroll">
        <return-value transfer-ownership="none">
          <type name="gboolean" c:type="gboolean"/>
        </return-value>
        <parameters>
          <instance-parameter name="field" transfer-ownership="none">
            <type name="FormField" c:type="PopplerFormField*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="text_do_spell_check"
              c:identifier="poppler_form_field_text_do_spell_check">
        <doc xml:space="preserve">Checks whether spell checking should be done for the contents of @field</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">%TRUE if spell checking should be done for @field</doc>
          <type name="gboolean" c:type="gboolean"/>
        </return-value>
        <parameters>
          <instance-parameter name="field" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerFormField</doc>
            <type name="FormField" c:type="PopplerFormField*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="text_get_max_len"
              c:identifier="poppler_form_field_text_get_max_len">
        <doc xml:space="preserve">Retrieves the maximum allowed length of the text in @field</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">the maximum allowed number of characters in @field, or -1 if there is no maximum.</doc>
          <type name="gint" c:type="gint"/>
        </return-value>
        <parameters>
          <instance-parameter name="field" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerFormField</doc>
            <type name="FormField" c:type="PopplerFormField*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="text_get_text"
              c:identifier="poppler_form_field_text_get_text">
        <doc xml:space="preserve">Retrieves the contents of @field.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new allocated string. It must be freed with g_free() when done.</doc>
          <type name="utf8" c:type="gchar*"/>
        </return-value>
        <parameters>
          <instance-parameter name="field" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerFormField</doc>
            <type name="FormField" c:type="PopplerFormField*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="text_get_text_type"
              c:identifier="poppler_form_field_text_get_text_type">
        <doc xml:space="preserve">Gets the text type of @field.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">#PopplerFormTextType of @field</doc>
          <type name="FormTextType" c:type="PopplerFormTextType"/>
        </return-value>
        <parameters>
          <instance-parameter name="field" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerFormField</doc>
            <type name="FormField" c:type="PopplerFormField*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="text_is_password"
              c:identifier="poppler_form_field_text_is_password">
        <doc xml:space="preserve">Checks whether content of @field is a password and it must be hidden</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">%TRUE if the content of @field is a password</doc>
          <type name="gboolean" c:type="gboolean"/>
        </return-value>
        <parameters>
          <instance-parameter name="field" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerFormField</doc>
            <type name="FormField" c:type="PopplerFormField*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="text_is_rich_text"
              c:identifier="poppler_form_field_text_is_rich_text">
        <doc xml:space="preserve">Checks whether the contents of @field are rich text</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">%TRUE if the contents of @field are rich text</doc>
          <type name="gboolean" c:type="gboolean"/>
        </return-value>
        <parameters>
          <instance-parameter name="field" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerFormField</doc>
            <type name="FormField" c:type="PopplerFormField*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="text_set_text"
              c:identifier="poppler_form_field_text_set_text">
        <doc xml:space="preserve">Sets the text in @field to the given value, replacing the current contents.</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="field" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerFormField</doc>
            <type name="FormField" c:type="PopplerFormField*"/>
          </instance-parameter>
          <parameter name="text" transfer-ownership="none">
            <doc xml:space="preserve">the new text</doc>
            <type name="utf8" c:type="const gchar*"/>
          </parameter>
        </parameters>
      </method>
    </class>
    <record name="FormFieldMapping"
            c:type="PopplerFormFieldMapping"
            glib:type-name="PopplerFormFieldMapping"
            glib:get-type="poppler_form_field_mapping_get_type"
            c:symbol-prefix="form_field_mapping">
      <doc xml:space="preserve">A #PopplerFormFieldMapping structure represents the location
of @field on the page</doc>
      <field name="area" writable="1">
        <doc xml:space="preserve">a #PopplerRectangle representing an area of the page</doc>
        <type name="Rectangle" c:type="PopplerRectangle"/>
      </field>
      <field name="field" writable="1">
        <doc xml:space="preserve">a #PopplerFormField</doc>
        <type name="FormField" c:type="PopplerFormField*"/>
      </field>
      <constructor name="new" c:identifier="poppler_form_field_mapping_new">
        <doc xml:space="preserve">Creates a new #PopplerFormFieldMapping</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new #PopplerFormFieldMapping, use poppler_form_field_mapping_free() to free it</doc>
          <type name="FormFieldMapping" c:type="PopplerFormFieldMapping*"/>
        </return-value>
      </constructor>
      <method name="copy" c:identifier="poppler_form_field_mapping_copy">
        <doc xml:space="preserve">Creates a copy of @mapping</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new allocated copy of @mapping</doc>
          <type name="FormFieldMapping" c:type="PopplerFormFieldMapping*"/>
        </return-value>
        <parameters>
          <instance-parameter name="mapping" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerFormFieldMapping to copy</doc>
            <type name="FormFieldMapping" c:type="PopplerFormFieldMapping*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="free" c:identifier="poppler_form_field_mapping_free">
        <doc xml:space="preserve">Frees the given #PopplerFormFieldMapping</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="mapping" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerFormFieldMapping</doc>
            <type name="FormFieldMapping" c:type="PopplerFormFieldMapping*"/>
          </instance-parameter>
        </parameters>
      </method>
    </record>
    <enumeration name="FormFieldType"
                 glib:type-name="PopplerFormFieldType"
                 glib:get-type="poppler_form_field_type_get_type"
                 c:type="PopplerFormFieldType">
      <member name="unknown"
              value="0"
              c:identifier="POPPLER_FORM_FIELD_UNKNOWN"
              glib:nick="unknown">
      </member>
      <member name="button"
              value="1"
              c:identifier="POPPLER_FORM_FIELD_BUTTON"
              glib:nick="button">
      </member>
      <member name="text"
              value="2"
              c:identifier="POPPLER_FORM_FIELD_TEXT"
              glib:nick="text">
      </member>
      <member name="choice"
              value="3"
              c:identifier="POPPLER_FORM_FIELD_CHOICE"
              glib:nick="choice">
      </member>
      <member name="signature"
              value="4"
              c:identifier="POPPLER_FORM_FIELD_SIGNATURE"
              glib:nick="signature">
      </member>
    </enumeration>
    <enumeration name="FormTextType"
                 glib:type-name="PopplerFormTextType"
                 glib:get-type="poppler_form_text_type_get_type"
                 c:type="PopplerFormTextType">
      <member name="normal"
              value="0"
              c:identifier="POPPLER_FORM_TEXT_NORMAL"
              glib:nick="normal">
      </member>
      <member name="multiline"
              value="1"
              c:identifier="POPPLER_FORM_TEXT_MULTILINE"
              glib:nick="multiline">
      </member>
      <member name="file_select"
              value="2"
              c:identifier="POPPLER_FORM_TEXT_FILE_SELECT"
              glib:nick="file-select">
      </member>
    </enumeration>
    <constant name="HAS_CAIRO" value="1" c:type="POPPLER_HAS_CAIRO">
      <doc xml:space="preserve">Defined if poppler was compiled with cairo support.</doc>
      <type name="gint" c:type="gint"/>
    </constant>
    <record name="ImageMapping"
            c:type="PopplerImageMapping"
            glib:type-name="PopplerImageMapping"
            glib:get-type="poppler_image_mapping_get_type"
            c:symbol-prefix="image_mapping">
      <doc xml:space="preserve">A #PopplerImageMapping structure represents the location
of an image on the page</doc>
      <field name="area" writable="1">
        <doc xml:space="preserve">a #PopplerRectangle representing an area of the page</doc>
        <type name="Rectangle" c:type="PopplerRectangle"/>
      </field>
      <field name="image_id" writable="1">
        <doc xml:space="preserve">an image identifier</doc>
        <type name="gint" c:type="gint"/>
      </field>
      <constructor name="new" c:identifier="poppler_image_mapping_new">
        <doc xml:space="preserve">Creates a new #PopplerImageMapping</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new #PopplerImageMapping, use poppler_image_mapping_free() to free it</doc>
          <type name="ImageMapping" c:type="PopplerImageMapping*"/>
        </return-value>
      </constructor>
      <method name="copy" c:identifier="poppler_image_mapping_copy">
        <doc xml:space="preserve">Creates a copy of @mapping</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new allocated copy of @mapping</doc>
          <type name="ImageMapping" c:type="PopplerImageMapping*"/>
        </return-value>
        <parameters>
          <instance-parameter name="mapping" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerImageMapping to copy</doc>
            <type name="ImageMapping" c:type="PopplerImageMapping*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="free" c:identifier="poppler_image_mapping_free">
        <doc xml:space="preserve">Frees the given #PopplerImageMapping</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="mapping" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerImageMapping</doc>
            <type name="ImageMapping" c:type="PopplerImageMapping*"/>
          </instance-parameter>
        </parameters>
      </method>
    </record>
    <record name="IndexIter"
            c:type="PopplerIndexIter"
            glib:type-name="PopplerIndexIter"
            glib:get-type="poppler_index_iter_get_type"
            c:symbol-prefix="index_iter">
      <constructor name="new" c:identifier="poppler_index_iter_new">
        <doc xml:space="preserve">Returns the root #PopplerIndexIter for @document, or %NULL.  This must be
freed with poppler_index_iter_free().

Certain documents have an index associated with them.  This index can be used
to help the user navigate the document, and is similar to a table of
contents.  Each node in the index will contain a #PopplerAction that can be
displayed to the user &amp;mdash; typically a #POPPLER_ACTION_GOTO_DEST or a
#POPPLER_ACTION_URI&lt;!-- --&gt;.

Here is a simple example of some code that walks the full index:

&lt;informalexample&gt;&lt;programlisting&gt;
static void
walk_index (PopplerIndexIter *iter)
{
  do
    {
      /&lt;!-- --&gt;* Get the the action and do something with it *&lt;!-- --&gt;/
      PopplerIndexIter *child = poppler_index_iter_get_child (iter);
      if (child)
        walk_index (child);
      poppler_index_iter_free (child);
    }
  while (poppler_index_iter_next (iter));
}
...
{
  iter = poppler_index_iter_new (document);
  walk_index (iter);
  poppler_index_iter_free (iter);
}
&lt;/programlisting&gt;&lt;/informalexample&gt;</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new #PopplerIndexIter</doc>
          <type name="IndexIter" c:type="PopplerIndexIter*"/>
        </return-value>
        <parameters>
          <parameter name="document" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerDocument</doc>
            <type name="Document" c:type="PopplerDocument*"/>
          </parameter>
        </parameters>
      </constructor>
      <method name="copy" c:identifier="poppler_index_iter_copy">
        <doc xml:space="preserve">Creates a new #PopplerIndexIter as a copy of @iter.  This must be freed with
poppler_index_iter_free().</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new #PopplerIndexIter</doc>
          <type name="IndexIter" c:type="PopplerIndexIter*"/>
        </return-value>
        <parameters>
          <instance-parameter name="iter" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerIndexIter</doc>
            <type name="IndexIter" c:type="PopplerIndexIter*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="free" c:identifier="poppler_index_iter_free">
        <doc xml:space="preserve">Frees @iter.</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="iter" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerIndexIter</doc>
            <type name="IndexIter" c:type="PopplerIndexIter*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_action" c:identifier="poppler_index_iter_get_action">
        <doc xml:space="preserve">Returns the #PopplerAction associated with @iter.  It must be freed with
poppler_action_free().</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new #PopplerAction</doc>
          <type name="Action" c:type="PopplerAction*"/>
        </return-value>
        <parameters>
          <instance-parameter name="iter" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerIndexIter</doc>
            <type name="IndexIter" c:type="PopplerIndexIter*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_child" c:identifier="poppler_index_iter_get_child">
        <doc xml:space="preserve">Returns a newly created child of @parent, or %NULL if the iter has no child.
See poppler_index_iter_new() for more information on this function.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new #PopplerIndexIter</doc>
          <type name="IndexIter" c:type="PopplerIndexIter*"/>
        </return-value>
        <parameters>
          <instance-parameter name="parent" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerIndexIter</doc>
            <type name="IndexIter" c:type="PopplerIndexIter*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="is_open" c:identifier="poppler_index_iter_is_open">
        <doc xml:space="preserve">Returns whether this node should be expanded by default to the user.  The
document can provide a hint as to how the document's index should be expanded
initially.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">%TRUE, if the document wants @iter to be expanded</doc>
          <type name="gboolean" c:type="gboolean"/>
        </return-value>
        <parameters>
          <instance-parameter name="iter" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerIndexIter</doc>
            <type name="IndexIter" c:type="PopplerIndexIter*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="next" c:identifier="poppler_index_iter_next">
        <doc xml:space="preserve">Sets @iter to point to the next action at the current level, if valid.  See
poppler_index_iter_new() for more information.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">%TRUE, if @iter was set to the next action</doc>
          <type name="gboolean" c:type="gboolean"/>
        </return-value>
        <parameters>
          <instance-parameter name="iter" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerIndexIter</doc>
            <type name="IndexIter" c:type="PopplerIndexIter*"/>
          </instance-parameter>
        </parameters>
      </method>
    </record>
    <class name="Layer"
           c:symbol-prefix="layer"
           c:type="PopplerLayer"
           parent="GObject.Object"
           glib:type-name="PopplerLayer"
           glib:get-type="poppler_layer_get_type">
      <method name="get_radio_button_group_id"
              c:identifier="poppler_layer_get_radio_button_group_id"
              version="0.12">
        <doc xml:space="preserve">Returns the numeric ID the radio button group associated with @layer.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">the ID of the radio button group associated with @layer,
or 0 if the layer is not associated to any radio button group</doc>
          <type name="gint" c:type="gint"/>
        </return-value>
        <parameters>
          <instance-parameter name="layer" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerLayer</doc>
            <type name="Layer" c:type="PopplerLayer*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_title"
              c:identifier="poppler_layer_get_title"
              version="0.12">
        <doc xml:space="preserve">Returns the name of the layer suitable for
presentation as a title in a viewer's GUI</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">a string containing the title of the layer</doc>
          <type name="utf8" c:type="const gchar*"/>
        </return-value>
        <parameters>
          <instance-parameter name="layer" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerLayer</doc>
            <type name="Layer" c:type="PopplerLayer*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="hide" c:identifier="poppler_layer_hide" version="0.12">
        <doc xml:space="preserve">Hides @layer. If @layer is the parent of other nested layers,
such layers will be also hidden and will be blocked until @layer
is shown again</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="layer" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerLayer</doc>
            <type name="Layer" c:type="PopplerLayer*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="is_parent"
              c:identifier="poppler_layer_is_parent"
              version="0.12">
        <doc xml:space="preserve">Returns whether @layer is parent of other nested layers.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">%TRUE if @layer is a parent layer</doc>
          <type name="gboolean" c:type="gboolean"/>
        </return-value>
        <parameters>
          <instance-parameter name="layer" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerLayer</doc>
            <type name="Layer" c:type="PopplerLayer*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="is_visible"
              c:identifier="poppler_layer_is_visible"
              version="0.12">
        <doc xml:space="preserve">Returns whether @layer is visible</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">%TRUE if @layer is visible</doc>
          <type name="gboolean" c:type="gboolean"/>
        </return-value>
        <parameters>
          <instance-parameter name="layer" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerLayer</doc>
            <type name="Layer" c:type="PopplerLayer*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="show" c:identifier="poppler_layer_show" version="0.12">
        <doc xml:space="preserve">Shows @layer</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="layer" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerLayer</doc>
            <type name="Layer" c:type="PopplerLayer*"/>
          </instance-parameter>
        </parameters>
      </method>
    </class>
    <record name="LayersIter"
            c:type="PopplerLayersIter"
            glib:type-name="PopplerLayersIter"
            glib:get-type="poppler_layers_iter_get_type"
            c:symbol-prefix="layers_iter">
      <constructor name="new"
                   c:identifier="poppler_layers_iter_new"
                   version="0.12">
        <return-value transfer-ownership="full">
          <type name="LayersIter" c:type="PopplerLayersIter*"/>
        </return-value>
        <parameters>
          <parameter name="document" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerDocument</doc>
            <type name="Document" c:type="PopplerDocument*"/>
          </parameter>
        </parameters>
      </constructor>
      <method name="copy" c:identifier="poppler_layers_iter_copy">
        <doc xml:space="preserve">Creates a new #PopplerLayersIter as a copy of @iter.  This must be freed with
poppler_layers_iter_free().</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new #PopplerLayersIter

Since 0.12</doc>
          <type name="LayersIter" c:type="PopplerLayersIter*"/>
        </return-value>
        <parameters>
          <instance-parameter name="iter" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerLayersIter</doc>
            <type name="LayersIter" c:type="PopplerLayersIter*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="free"
              c:identifier="poppler_layers_iter_free"
              version="0.12">
        <doc xml:space="preserve">Frees @iter.</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="iter" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerLayersIter</doc>
            <type name="LayersIter" c:type="PopplerLayersIter*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_child"
              c:identifier="poppler_layers_iter_get_child"
              version="0.12">
        <doc xml:space="preserve">Returns a newly created child of @parent, or %NULL if the iter has no child.
See poppler_layers_iter_new() for more information on this function.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new #PopplerLayersIter, or %NULL</doc>
          <type name="LayersIter" c:type="PopplerLayersIter*"/>
        </return-value>
        <parameters>
          <instance-parameter name="parent" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerLayersIter</doc>
            <type name="LayersIter" c:type="PopplerLayersIter*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_layer"
              c:identifier="poppler_layers_iter_get_layer"
              version="0.12">
        <doc xml:space="preserve">Returns the #PopplerLayer associated with @iter.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new #PopplerLayer, or %NULL if
there isn't any layer associated with @iter</doc>
          <type name="Layer" c:type="PopplerLayer*"/>
        </return-value>
        <parameters>
          <instance-parameter name="iter" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerLayersIter</doc>
            <type name="LayersIter" c:type="PopplerLayersIter*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_title"
              c:identifier="poppler_layers_iter_get_title"
              version="0.12">
        <doc xml:space="preserve">Returns the title associated with @iter.  It must be freed with
g_free().</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new string containing the @iter's title or %NULL if @iter doesn't have a title.
The returned string should be freed with g_free() when no longer needed.</doc>
          <type name="utf8" c:type="gchar*"/>
        </return-value>
        <parameters>
          <instance-parameter name="iter" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerLayersIter</doc>
            <type name="LayersIter" c:type="PopplerLayersIter*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="next"
              c:identifier="poppler_layers_iter_next"
              version="0.12">
        <doc xml:space="preserve">Sets @iter to point to the next action at the current level, if valid.  See
poppler_layers_iter_new() for more information.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">%TRUE, if @iter was set to the next action</doc>
          <type name="gboolean" c:type="gboolean"/>
        </return-value>
        <parameters>
          <instance-parameter name="iter" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerLayersIter</doc>
            <type name="LayersIter" c:type="PopplerLayersIter*"/>
          </instance-parameter>
        </parameters>
      </method>
    </record>
    <record name="LinkMapping"
            c:type="PopplerLinkMapping"
            glib:type-name="PopplerLinkMapping"
            glib:get-type="poppler_link_mapping_get_type"
            c:symbol-prefix="link_mapping">
      <doc xml:space="preserve">A #PopplerLinkMapping structure represents the location
of @action on the page</doc>
      <field name="area" writable="1">
        <doc xml:space="preserve">a #PopplerRectangle representing an area of the page</doc>
        <type name="Rectangle" c:type="PopplerRectangle"/>
      </field>
      <field name="action" writable="1">
        <doc xml:space="preserve">a #PopplerAction</doc>
        <type name="Action" c:type="PopplerAction*"/>
      </field>
      <constructor name="new" c:identifier="poppler_link_mapping_new">
        <doc xml:space="preserve">Creates a new #PopplerLinkMapping</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new #PopplerLinkMapping, use poppler_link_mapping_free() to free it</doc>
          <type name="LinkMapping" c:type="PopplerLinkMapping*"/>
        </return-value>
      </constructor>
      <method name="copy" c:identifier="poppler_link_mapping_copy">
        <doc xml:space="preserve">Creates a copy of @mapping</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new allocated copy of @mapping</doc>
          <type name="LinkMapping" c:type="PopplerLinkMapping*"/>
        </return-value>
        <parameters>
          <instance-parameter name="mapping" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerLinkMapping to copy</doc>
            <type name="LinkMapping" c:type="PopplerLinkMapping*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="free" c:identifier="poppler_link_mapping_free">
        <doc xml:space="preserve">Frees the given #PopplerLinkMapping</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="mapping" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerLinkMapping</doc>
            <type name="LinkMapping" c:type="PopplerLinkMapping*"/>
          </instance-parameter>
        </parameters>
      </method>
    </record>
    <constant name="MAJOR_VERSION"
              value="0"
              c:type="POPPLER_MAJOR_VERSION"
              version="0.12">
      <doc xml:space="preserve">The major version number of the poppler header files (e.g. in poppler version
0.1.2 this is 0.)</doc>
      <type name="gint" c:type="gint"/>
    </constant>
    <constant name="MICRO_VERSION"
              value="0"
              c:type="POPPLER_MICRO_VERSION"
              version="0.12">
      <doc xml:space="preserve">The micro version number of the poppler header files (e.g. in poppler version
0.1.2 this is 2.)</doc>
      <type name="gint" c:type="gint"/>
    </constant>
    <constant name="MINOR_VERSION"
              value="66"
              c:type="POPPLER_MINOR_VERSION"
              version="0.12">
      <doc xml:space="preserve">The major version number of the poppler header files (e.g. in poppler version
0.1.2 this is 1.)</doc>
      <type name="gint" c:type="gint"/>
    </constant>
    <class name="Media"
           c:symbol-prefix="media"
           c:type="PopplerMedia"
           parent="GObject.Object"
           glib:type-name="PopplerMedia"
           glib:get-type="poppler_media_get_type">
      <method name="get_filename"
              c:identifier="poppler_media_get_filename"
              version="0.14">
        <doc xml:space="preserve">Returns the media clip filename, in case of non-embedded media. filename might be
a local relative or absolute path or a URI</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">a filename, return value is owned by #PopplerMedia and should not be freed</doc>
          <type name="utf8" c:type="const gchar*"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_media" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerMedia</doc>
            <type name="Media" c:type="PopplerMedia*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_mime_type"
              c:identifier="poppler_media_get_mime_type"
              version="0.14">
        <doc xml:space="preserve">Returns the media clip mime-type</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">the mime-type, return value is owned by #PopplerMedia and should not be freed</doc>
          <type name="utf8" c:type="const gchar*"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_media" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerMedia</doc>
            <type name="Media" c:type="PopplerMedia*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="is_embedded"
              c:identifier="poppler_media_is_embedded"
              version="0.14">
        <doc xml:space="preserve">Whether the media clip is embedded in the PDF. If the result is %TRUE, the embedded stream
can be saved with poppler_media_save() or poppler_media_save_to_callback() function.
If the result is %FALSE, the media clip filename can be retrieved with
poppler_media_get_filename() function.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">%TRUE if media clip is embedded, %FALSE otherwise</doc>
          <type name="gboolean" c:type="gboolean"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_media" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerMedia</doc>
            <type name="Media" c:type="PopplerMedia*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="save"
              c:identifier="poppler_media_save"
              version="0.14"
              throws="1">
        <doc xml:space="preserve">Saves embedded stream of @poppler_media to a file indicated by @filename.
If @error is set, %FALSE will be returned.
Possible errors include those in the #G_FILE_ERROR domain
and whatever the save function generates.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">%TRUE, if the file successfully saved</doc>
          <type name="gboolean" c:type="gboolean"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_media" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerMedia</doc>
            <type name="Media" c:type="PopplerMedia*"/>
          </instance-parameter>
          <parameter name="filename" transfer-ownership="none">
            <doc xml:space="preserve">name of file to save</doc>
            <type name="utf8" c:type="const char*"/>
          </parameter>
        </parameters>
      </method>
      <method name="save_to_callback"
              c:identifier="poppler_media_save_to_callback"
              version="0.14"
              throws="1">
        <doc xml:space="preserve">Saves embedded stream of @poppler_media by feeding the produced data to @save_func. Can be used
when you want to store the media clip stream to something other than a file, such as
an in-memory buffer or a socket. If @error is set, %FALSE will be
returned. Possible errors include those in the #G_FILE_ERROR domain and
whatever the save function generates.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">%TRUE, if the save successfully completed</doc>
          <type name="gboolean" c:type="gboolean"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_media" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerMedia</doc>
            <type name="Media" c:type="PopplerMedia*"/>
          </instance-parameter>
          <parameter name="save_func"
                     transfer-ownership="none"
                     scope="call"
                     closure="1">
            <doc xml:space="preserve">a function that is called to save each block of data that the save routine generates.</doc>
            <type name="MediaSaveFunc" c:type="PopplerMediaSaveFunc"/>
          </parameter>
          <parameter name="user_data"
                     transfer-ownership="none"
                     nullable="1"
                     allow-none="1">
            <doc xml:space="preserve">user data to pass to the save function.</doc>
            <type name="gpointer" c:type="gpointer"/>
          </parameter>
        </parameters>
      </method>
    </class>
    <callback name="MediaSaveFunc"
              c:type="PopplerMediaSaveFunc"
              version="0.14"
              throws="1">
      <doc xml:space="preserve">Specifies the type of the function passed to
poppler_media_save_to_callback().  It is called once for each block of
bytes that is "written" by poppler_media_save_to_callback().  If
successful it should return %TRUE.  If an error occurs it should set
@error and return %FALSE, in which case poppler_media_save_to_callback()
will fail with the same error.</doc>
      <return-value transfer-ownership="none">
        <doc xml:space="preserve">%TRUE if successful, %FALSE (with @error set) if failed.</doc>
        <type name="gboolean" c:type="gboolean"/>
      </return-value>
      <parameters>
        <parameter name="buf" transfer-ownership="none">
          <doc xml:space="preserve">buffer containing
  bytes to be written.</doc>
          <array length="1" zero-terminated="0" c:type="gchar*">
            <type name="guint8"/>
          </array>
        </parameter>
        <parameter name="count" transfer-ownership="none">
          <doc xml:space="preserve">number of bytes in @buf.</doc>
          <type name="gsize" c:type="gsize"/>
        </parameter>
        <parameter name="data"
                   transfer-ownership="none"
                   nullable="1"
                   allow-none="1"
                   closure="2">
          <doc xml:space="preserve">user data passed to poppler_media_save_to_callback()</doc>
          <type name="gpointer" c:type="gpointer"/>
        </parameter>
      </parameters>
    </callback>
    <class name="Movie"
           c:symbol-prefix="movie"
           c:type="PopplerMovie"
           parent="GObject.Object"
           glib:type-name="PopplerMovie"
           glib:get-type="poppler_movie_get_type">
      <method name="get_filename"
              c:identifier="poppler_movie_get_filename"
              version="0.14">
        <doc xml:space="preserve">Returns the local filename identifying a self-describing movie file</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">a local filename, return value is owned by #PopplerMovie and
              should not be freed</doc>
          <type name="utf8" c:type="const gchar*"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_movie" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerMovie</doc>
            <type name="Movie" c:type="PopplerMovie*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_play_mode"
              c:identifier="poppler_movie_get_play_mode"
              version="0.54">
        <doc xml:space="preserve">Returns the play mode of @poppler_movie.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">a #PopplerMoviePlayMode.</doc>
          <type name="MoviePlayMode" c:type="PopplerMoviePlayMode"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_movie" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerMovie</doc>
            <type name="Movie" c:type="PopplerMovie*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="need_poster"
              c:identifier="poppler_movie_need_poster"
              version="0.14">
        <doc xml:space="preserve">Returns whether a poster image representing the Movie
shall be displayed. The poster image must be retrieved
from the movie file.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">%TRUE if move needs a poster image, %FALSE otherwise</doc>
          <type name="gboolean" c:type="gboolean"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_movie" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerMovie</doc>
            <type name="Movie" c:type="PopplerMovie*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="show_controls"
              c:identifier="poppler_movie_show_controls"
              version="0.14">
        <doc xml:space="preserve">Returns whether to display a movie controller bar while playing the movie</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">%TRUE if controller bar should be displayed, %FALSE otherwise</doc>
          <type name="gboolean" c:type="gboolean"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_movie" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerMovie</doc>
            <type name="Movie" c:type="PopplerMovie*"/>
          </instance-parameter>
        </parameters>
      </method>
    </class>
    <enumeration name="MoviePlayMode"
                 version="0.54"
                 glib:type-name="PopplerMoviePlayMode"
                 glib:get-type="poppler_movie_play_mode_get_type"
                 c:type="PopplerMoviePlayMode">
      <doc xml:space="preserve">Play mode enum values.</doc>
      <member name="once"
              value="0"
              c:identifier="POPPLER_MOVIE_PLAY_MODE_ONCE"
              glib:nick="once">
        <doc xml:space="preserve">the movie should be played once and controls should be closed at the end.</doc>
      </member>
      <member name="open"
              value="1"
              c:identifier="POPPLER_MOVIE_PLAY_MODE_OPEN"
              glib:nick="open">
        <doc xml:space="preserve">the movie should be played once, but controls should be left open.</doc>
      </member>
      <member name="repeat"
              value="2"
              c:identifier="POPPLER_MOVIE_PLAY_MODE_REPEAT"
              glib:nick="repeat">
        <doc xml:space="preserve">the movie should be played in loop, until manually stopped.</doc>
      </member>
      <member name="palindrome"
              value="3"
              c:identifier="POPPLER_MOVIE_PLAY_MODE_PALINDROME"
              glib:nick="palindrome">
        <doc xml:space="preserve">the movie should be played forward and backward, forward and backward,
  and so forth, until manually stopped.</doc>
      </member>
    </enumeration>
    <class name="PSFile"
           c:symbol-prefix="ps_file"
           c:type="PopplerPSFile"
           parent="GObject.Object"
           glib:type-name="PopplerPSFile"
           glib:get-type="poppler_ps_file_get_type">
      <constructor name="new" c:identifier="poppler_ps_file_new">
        <doc xml:space="preserve">Create a new postscript file to render to</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a PopplerPSFile</doc>
          <type name="PSFile" c:type="PopplerPSFile*"/>
        </return-value>
        <parameters>
          <parameter name="document" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerDocument</doc>
            <type name="Document" c:type="PopplerDocument*"/>
          </parameter>
          <parameter name="filename" transfer-ownership="none">
            <doc xml:space="preserve">the path of the output filename</doc>
            <type name="utf8" c:type="const char*"/>
          </parameter>
          <parameter name="first_page" transfer-ownership="none">
            <doc xml:space="preserve">the first page to print</doc>
            <type name="gint" c:type="int"/>
          </parameter>
          <parameter name="n_pages" transfer-ownership="none">
            <doc xml:space="preserve">the number of pages to print</doc>
            <type name="gint" c:type="int"/>
          </parameter>
        </parameters>
      </constructor>
      <method name="free" c:identifier="poppler_ps_file_free">
        <doc xml:space="preserve">Frees @ps_file</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="ps_file" transfer-ownership="none">
            <doc xml:space="preserve">a PopplerPSFile</doc>
            <type name="PSFile" c:type="PopplerPSFile*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="set_duplex" c:identifier="poppler_ps_file_set_duplex">
        <doc xml:space="preserve">Enable or disable Duplex printing.</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="ps_file" transfer-ownership="none">
            <doc xml:space="preserve">a PopplerPSFile which was not yet printed to</doc>
            <type name="PSFile" c:type="PopplerPSFile*"/>
          </instance-parameter>
          <parameter name="duplex" transfer-ownership="none">
            <doc xml:space="preserve">whether to force duplex printing (on printers which support this)</doc>
            <type name="gboolean" c:type="gboolean"/>
          </parameter>
        </parameters>
      </method>
      <method name="set_paper_size"
              c:identifier="poppler_ps_file_set_paper_size">
        <doc xml:space="preserve">Set the output paper size. These values will end up in the
DocumentMedia, the BoundingBox DSC comments and other places in the
generated PostScript.</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="ps_file" transfer-ownership="none">
            <doc xml:space="preserve">a PopplerPSFile which was not yet printed to.</doc>
            <type name="PSFile" c:type="PopplerPSFile*"/>
          </instance-parameter>
          <parameter name="width" transfer-ownership="none">
            <doc xml:space="preserve">the paper width in 1/72 inch</doc>
            <type name="gdouble" c:type="double"/>
          </parameter>
          <parameter name="height" transfer-ownership="none">
            <doc xml:space="preserve">the paper height in 1/72 inch</doc>
            <type name="gdouble" c:type="double"/>
          </parameter>
        </parameters>
      </method>
    </class>
    <class name="Page"
           c:symbol-prefix="page"
           c:type="PopplerPage"
           parent="GObject.Object"
           glib:type-name="PopplerPage"
           glib:get-type="poppler_page_get_type">
      <function name="free_annot_mapping"
                c:identifier="poppler_page_free_annot_mapping">
        <doc xml:space="preserve">Frees a list of #PopplerAnnotMapping&lt;!-- --&gt;s allocated by
poppler_page_get_annot_mapping().  It also unreferences the #PopplerAnnot&lt;!-- --&gt;s
that each mapping contains, so if you want to keep them around, you need to
reference them with g_object_ref().</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <parameter name="list" transfer-ownership="none">
            <doc xml:space="preserve">A list of
  #PopplerAnnotMapping&lt;!-- --&gt;s</doc>
            <type name="GLib.List" c:type="GList*">
              <type name="AnnotMapping"/>
            </type>
          </parameter>
        </parameters>
      </function>
      <function name="free_form_field_mapping"
                c:identifier="poppler_page_free_form_field_mapping">
        <doc xml:space="preserve">Frees a list of #PopplerFormFieldMapping&lt;!-- --&gt;s allocated by
poppler_page_get_form_field_mapping().</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <parameter name="list" transfer-ownership="none">
            <doc xml:space="preserve">A list of
  #PopplerFormFieldMapping&lt;!-- --&gt;s</doc>
            <type name="GLib.List" c:type="GList*">
              <type name="FormFieldMapping"/>
            </type>
          </parameter>
        </parameters>
      </function>
      <function name="free_image_mapping"
                c:identifier="poppler_page_free_image_mapping">
        <doc xml:space="preserve">Frees a list of #PopplerImageMapping&lt;!-- --&gt;s allocated by
poppler_page_get_image_mapping().</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <parameter name="list" transfer-ownership="none">
            <doc xml:space="preserve">A list of
  #PopplerImageMapping&lt;!-- --&gt;s</doc>
            <type name="GLib.List" c:type="GList*">
              <type name="ImageMapping"/>
            </type>
          </parameter>
        </parameters>
      </function>
      <function name="free_link_mapping"
                c:identifier="poppler_page_free_link_mapping">
        <doc xml:space="preserve">Frees a list of #PopplerLinkMapping&lt;!-- --&gt;s allocated by
poppler_page_get_link_mapping().  It also frees the #PopplerAction&lt;!-- --&gt;s
that each mapping contains, so if you want to keep them around, you need to
copy them with poppler_action_copy().</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <parameter name="list" transfer-ownership="none">
            <doc xml:space="preserve">A list of
  #PopplerLinkMapping&lt;!-- --&gt;s</doc>
            <type name="GLib.List" c:type="GList*">
              <type name="LinkMapping"/>
            </type>
          </parameter>
        </parameters>
      </function>
      <function name="free_text_attributes"
                c:identifier="poppler_page_free_text_attributes"
                version="0.18">
        <doc xml:space="preserve">Frees a list of #PopplerTextAttributes&lt;!-- --&gt;s allocated by
poppler_page_get_text_attributes().</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <parameter name="list" transfer-ownership="none">
            <doc xml:space="preserve">A list of
  #PopplerTextAttributes&lt;!-- --&gt;s</doc>
            <type name="GLib.List" c:type="GList*">
              <type name="TextAttributes"/>
            </type>
          </parameter>
        </parameters>
      </function>
      <function name="selection_region_free"
                c:identifier="poppler_page_selection_region_free"
                deprecated="1"
                deprecated-version="0.16">
        <doc xml:space="preserve">Frees @region</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <parameter name="region" transfer-ownership="none">
            <doc xml:space="preserve">a #GList of
  #PopplerRectangle</doc>
            <type name="GLib.List" c:type="GList*">
              <type name="Rectangle"/>
            </type>
          </parameter>
        </parameters>
      </function>
      <method name="add_annot"
              c:identifier="poppler_page_add_annot"
              version="0.16">
        <doc xml:space="preserve">Adds annotation @annot to @page.</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="page" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerPage</doc>
            <type name="Page" c:type="PopplerPage*"/>
          </instance-parameter>
          <parameter name="annot" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerAnnot to add</doc>
            <type name="Annot" c:type="PopplerAnnot*"/>
          </parameter>
        </parameters>
      </method>
      <method name="find_text" c:identifier="poppler_page_find_text">
        <doc xml:space="preserve">Finds @text in @page with the default options (%POPPLER_FIND_DEFAULT) and
returns a #GList of rectangles for each occurance of the text on the page.
The coordinates are in PDF points.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a #GList of #PopplerRectangle,</doc>
          <type name="GLib.List" c:type="GList*">
            <type name="Rectangle"/>
          </type>
        </return-value>
        <parameters>
          <instance-parameter name="page" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerPage</doc>
            <type name="Page" c:type="PopplerPage*"/>
          </instance-parameter>
          <parameter name="text" transfer-ownership="none">
            <doc xml:space="preserve">the text to search for (UTF-8 encoded)</doc>
            <type name="utf8" c:type="const char*"/>
          </parameter>
        </parameters>
      </method>
      <method name="find_text_with_options"
              c:identifier="poppler_page_find_text_with_options"
              version="0.22">
        <doc xml:space="preserve">Finds @text in @page with the given #PopplerFindFlags options and
returns a #GList of rectangles for each occurance of the text on the page.
The coordinates are in PDF points.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a #GList of #PopplerRectangle,</doc>
          <type name="GLib.List" c:type="GList*">
            <type name="Rectangle"/>
          </type>
        </return-value>
        <parameters>
          <instance-parameter name="page" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerPage</doc>
            <type name="Page" c:type="PopplerPage*"/>
          </instance-parameter>
          <parameter name="text" transfer-ownership="none">
            <doc xml:space="preserve">the text to search for (UTF-8 encoded)</doc>
            <type name="utf8" c:type="const char*"/>
          </parameter>
          <parameter name="options" transfer-ownership="none">
            <doc xml:space="preserve">find options</doc>
            <type name="FindFlags" c:type="PopplerFindFlags"/>
          </parameter>
        </parameters>
      </method>
      <method name="get_annot_mapping"
              c:identifier="poppler_page_get_annot_mapping">
        <doc xml:space="preserve">Returns a list of #PopplerAnnotMapping items that map from a location on
@page to a #PopplerAnnot.  This list must be freed with
poppler_page_free_annot_mapping() when done.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">A #GList of #PopplerAnnotMapping</doc>
          <type name="GLib.List" c:type="GList*">
            <type name="AnnotMapping"/>
          </type>
        </return-value>
        <parameters>
          <instance-parameter name="page" transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerPage</doc>
            <type name="Page" c:type="PopplerPage*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_crop_box" c:identifier="poppler_page_get_crop_box">
        <doc xml:space="preserve">Retrurns the crop box of @page</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="page" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerPage</doc>
            <type name="Page" c:type="PopplerPage*"/>
          </instance-parameter>
          <parameter name="rect"
                     direction="out"
                     caller-allocates="1"
                     transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerRectangle to fill</doc>
            <type name="Rectangle" c:type="PopplerRectangle*"/>
          </parameter>
        </parameters>
      </method>
      <method name="get_duration" c:identifier="poppler_page_get_duration">
        <doc xml:space="preserve">Returns the duration of @page</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">duration in seconds of @page or -1.</doc>
          <type name="gdouble" c:type="double"/>
        </return-value>
        <parameters>
          <instance-parameter name="page" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerPage</doc>
            <type name="Page" c:type="PopplerPage*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_form_field_mapping"
              c:identifier="poppler_page_get_form_field_mapping">
        <doc xml:space="preserve">Returns a list of #PopplerFormFieldMapping items that map from a
location on @page to a form field.  This list must be freed
with poppler_page_free_form_field_mapping() when done.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">A #GList of #PopplerFormFieldMapping</doc>
          <type name="GLib.List" c:type="GList*">
            <type name="FormFieldMapping"/>
          </type>
        </return-value>
        <parameters>
          <instance-parameter name="page" transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerPage</doc>
            <type name="Page" c:type="PopplerPage*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_image" c:identifier="poppler_page_get_image">
        <doc xml:space="preserve">Returns a cairo surface for the image of the @page</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">A cairo surface for the image</doc>
          <type name="cairo.Surface" c:type="cairo_surface_t*"/>
        </return-value>
        <parameters>
          <instance-parameter name="page" transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerPage</doc>
            <type name="Page" c:type="PopplerPage*"/>
          </instance-parameter>
          <parameter name="image_id" transfer-ownership="none">
            <doc xml:space="preserve">The image identifier</doc>
            <type name="gint" c:type="gint"/>
          </parameter>
        </parameters>
      </method>
      <method name="get_image_mapping"
              c:identifier="poppler_page_get_image_mapping">
        <doc xml:space="preserve">Returns a list of #PopplerImageMapping items that map from a
location on @page to an image of the page. This list must be freed
with poppler_page_free_image_mapping() when done.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">A #GList of #PopplerImageMapping</doc>
          <type name="GLib.List" c:type="GList*">
            <type name="ImageMapping"/>
          </type>
        </return-value>
        <parameters>
          <instance-parameter name="page" transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerPage</doc>
            <type name="Page" c:type="PopplerPage*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_index" c:identifier="poppler_page_get_index">
        <doc xml:space="preserve">Returns the index of @page</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">index value of @page</doc>
          <type name="gint" c:type="int"/>
        </return-value>
        <parameters>
          <instance-parameter name="page" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerPage</doc>
            <type name="Page" c:type="PopplerPage*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_label"
              c:identifier="poppler_page_get_label"
              version="0.16">
        <doc xml:space="preserve">Returns the label of @page. Note that page labels
and page indices might not coincide.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new allocated string containing the label of @page,
              or %NULL if @page doesn't have a label</doc>
          <type name="utf8" c:type="gchar*"/>
        </return-value>
        <parameters>
          <instance-parameter name="page" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerPage</doc>
            <type name="Page" c:type="PopplerPage*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_link_mapping"
              c:identifier="poppler_page_get_link_mapping">
        <doc xml:space="preserve">Returns a list of #PopplerLinkMapping items that map from a
location on @page to a #PopplerAction.  This list must be freed
with poppler_page_free_link_mapping() when done.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">A #GList of #PopplerLinkMapping</doc>
          <type name="GLib.List" c:type="GList*">
            <type name="LinkMapping"/>
          </type>
        </return-value>
        <parameters>
          <instance-parameter name="page" transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerPage</doc>
            <type name="Page" c:type="PopplerPage*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_selected_region"
              c:identifier="poppler_page_get_selected_region"
              version="0.16">
        <doc xml:space="preserve">Returns a region containing the area that would be rendered by
poppler_page_render_selection().
The returned region must be freed with cairo_region_destroy()</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a cairo_region_t</doc>
          <type name="cairo.Region" c:type="cairo_region_t*"/>
        </return-value>
        <parameters>
          <instance-parameter name="page" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerPage</doc>
            <type name="Page" c:type="PopplerPage*"/>
          </instance-parameter>
          <parameter name="scale" transfer-ownership="none">
            <doc xml:space="preserve">scale specified as pixels per point</doc>
            <type name="gdouble" c:type="gdouble"/>
          </parameter>
          <parameter name="style" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerSelectionStyle</doc>
            <type name="SelectionStyle" c:type="PopplerSelectionStyle"/>
          </parameter>
          <parameter name="selection" transfer-ownership="none">
            <doc xml:space="preserve">start and end point of selection as a rectangle</doc>
            <type name="Rectangle" c:type="PopplerRectangle*"/>
          </parameter>
        </parameters>
      </method>
      <method name="get_selected_text"
              c:identifier="poppler_page_get_selected_text"
              version="0.16">
        <doc xml:space="preserve">Retrieves the contents of the specified @selection as text.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a pointer to the contents of the @selection
              as a string</doc>
          <type name="utf8" c:type="char*"/>
        </return-value>
        <parameters>
          <instance-parameter name="page" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerPage</doc>
            <type name="Page" c:type="PopplerPage*"/>
          </instance-parameter>
          <parameter name="style" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerSelectionStyle</doc>
            <type name="SelectionStyle" c:type="PopplerSelectionStyle"/>
          </parameter>
          <parameter name="selection" transfer-ownership="none">
            <doc xml:space="preserve">the #PopplerRectangle including the text</doc>
            <type name="Rectangle" c:type="PopplerRectangle*"/>
          </parameter>
        </parameters>
      </method>
      <method name="get_selection_region"
              c:identifier="poppler_page_get_selection_region"
              deprecated="1"
              deprecated-version="0.16">
        <doc xml:space="preserve">Returns a region containing the area that would be rendered by
poppler_page_render_selection() as a #GList of
#PopplerRectangle. The returned list must be freed with
poppler_page_selection_region_free().</doc>
        <doc-deprecated xml:space="preserve">Use poppler_page_get_selected_region() instead.</doc-deprecated>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a #GList of #PopplerRectangle</doc>
          <type name="GLib.List" c:type="GList*">
            <type name="Rectangle"/>
          </type>
        </return-value>
        <parameters>
          <instance-parameter name="page" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerPage</doc>
            <type name="Page" c:type="PopplerPage*"/>
          </instance-parameter>
          <parameter name="scale" transfer-ownership="none">
            <doc xml:space="preserve">scale specified as pixels per point</doc>
            <type name="gdouble" c:type="gdouble"/>
          </parameter>
          <parameter name="style" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerSelectionStyle</doc>
            <type name="SelectionStyle" c:type="PopplerSelectionStyle"/>
          </parameter>
          <parameter name="selection" transfer-ownership="none">
            <doc xml:space="preserve">start and end point of selection as a rectangle</doc>
            <type name="Rectangle" c:type="PopplerRectangle*"/>
          </parameter>
        </parameters>
      </method>
      <method name="get_size" c:identifier="poppler_page_get_size">
        <doc xml:space="preserve">Gets the size of @page at the current scale and rotation.</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="page" transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerPage</doc>
            <type name="Page" c:type="PopplerPage*"/>
          </instance-parameter>
          <parameter name="width"
                     direction="out"
                     caller-allocates="0"
                     transfer-ownership="full"
                     optional="1"
                     allow-none="1">
            <doc xml:space="preserve">return location for the width of @page</doc>
            <type name="gdouble" c:type="double*"/>
          </parameter>
          <parameter name="height"
                     direction="out"
                     caller-allocates="0"
                     transfer-ownership="full"
                     optional="1"
                     allow-none="1">
            <doc xml:space="preserve">return location for the height of @page</doc>
            <type name="gdouble" c:type="double*"/>
          </parameter>
        </parameters>
      </method>
      <method name="get_text"
              c:identifier="poppler_page_get_text"
              version="0.16">
        <doc xml:space="preserve">Retrieves the text of @page.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a pointer to the text of the @page
              as a string</doc>
          <type name="utf8" c:type="char*"/>
        </return-value>
        <parameters>
          <instance-parameter name="page" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerPage</doc>
            <type name="Page" c:type="PopplerPage*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_text_attributes"
              c:identifier="poppler_page_get_text_attributes"
              version="0.18">
        <doc xml:space="preserve">Obtains the attributes of the text as a #GList of #PopplerTextAttributes.
This list must be freed with poppler_page_free_text_attributes() when done.

Each list element is a #PopplerTextAttributes struct where start_index and
end_index indicates the range of text (as returned by poppler_page_get_text())
to which text attributes apply.

See also poppler_page_get_text_attributes_for_area()</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">A #GList of #PopplerTextAttributes</doc>
          <type name="GLib.List" c:type="GList*">
            <type name="TextAttributes"/>
          </type>
        </return-value>
        <parameters>
          <instance-parameter name="page" transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerPage</doc>
            <type name="Page" c:type="PopplerPage*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_text_attributes_for_area"
              c:identifier="poppler_page_get_text_attributes_for_area"
              version="0.26">
        <doc xml:space="preserve">Obtains the attributes of the text in @area as a #GList of #PopplerTextAttributes.
This list must be freed with poppler_page_free_text_attributes() when done.

Each list element is a #PopplerTextAttributes struct where start_index and
end_index indicates the range of text (as returned by poppler_page_get_text_for_area())
to which text attributes apply.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">A #GList of #PopplerTextAttributes</doc>
          <type name="GLib.List" c:type="GList*">
            <type name="TextAttributes"/>
          </type>
        </return-value>
        <parameters>
          <instance-parameter name="page" transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerPage</doc>
            <type name="Page" c:type="PopplerPage*"/>
          </instance-parameter>
          <parameter name="area" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerRectangle</doc>
            <type name="Rectangle" c:type="PopplerRectangle*"/>
          </parameter>
        </parameters>
      </method>
      <method name="get_text_for_area"
              c:identifier="poppler_page_get_text_for_area"
              version="0.26">
        <doc xml:space="preserve">Retrieves the text of @page contained in @area.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a pointer to the text as a string</doc>
          <type name="utf8" c:type="char*"/>
        </return-value>
        <parameters>
          <instance-parameter name="page" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerPage</doc>
            <type name="Page" c:type="PopplerPage*"/>
          </instance-parameter>
          <parameter name="area" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerRectangle</doc>
            <type name="Rectangle" c:type="PopplerRectangle*"/>
          </parameter>
        </parameters>
      </method>
      <method name="get_text_layout"
              c:identifier="poppler_page_get_text_layout"
              version="0.16">
        <doc xml:space="preserve">Obtains the layout of the text as a list of #PopplerRectangle
This array must be freed with g_free() when done.

The position in the array represents an offset in the text returned by
poppler_page_get_text()

See also poppler_page_get_text_layout_for_area().</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">%TRUE if the page contains text, %FALSE otherwise</doc>
          <type name="gboolean" c:type="gboolean"/>
        </return-value>
        <parameters>
          <instance-parameter name="page" transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerPage</doc>
            <type name="Page" c:type="PopplerPage*"/>
          </instance-parameter>
          <parameter name="rectangles"
                     direction="out"
                     caller-allocates="0"
                     transfer-ownership="container">
            <doc xml:space="preserve">return location for an array of #PopplerRectangle</doc>
            <array length="1" zero-terminated="0" c:type="PopplerRectangle**">
              <type name="Rectangle" c:type="PopplerRectangle*"/>
            </array>
          </parameter>
          <parameter name="n_rectangles"
                     direction="out"
                     caller-allocates="0"
                     transfer-ownership="full">
            <doc xml:space="preserve">length of returned array</doc>
            <type name="guint" c:type="guint*"/>
          </parameter>
        </parameters>
      </method>
      <method name="get_text_layout_for_area"
              c:identifier="poppler_page_get_text_layout_for_area"
              version="0.26">
        <doc xml:space="preserve">Obtains the layout of the text contained in @area as a list of #PopplerRectangle
This array must be freed with g_free() when done.

The position in the array represents an offset in the text returned by
poppler_page_get_text_for_area()</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">%TRUE if the page contains text, %FALSE otherwise</doc>
          <type name="gboolean" c:type="gboolean"/>
        </return-value>
        <parameters>
          <instance-parameter name="page" transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerPage</doc>
            <type name="Page" c:type="PopplerPage*"/>
          </instance-parameter>
          <parameter name="area" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerRectangle</doc>
            <type name="Rectangle" c:type="PopplerRectangle*"/>
          </parameter>
          <parameter name="rectangles"
                     direction="out"
                     caller-allocates="0"
                     transfer-ownership="container">
            <doc xml:space="preserve">return location for an array of #PopplerRectangle</doc>
            <array length="2" zero-terminated="0" c:type="PopplerRectangle**">
              <type name="Rectangle" c:type="PopplerRectangle*"/>
            </array>
          </parameter>
          <parameter name="n_rectangles"
                     direction="out"
                     caller-allocates="0"
                     transfer-ownership="full">
            <doc xml:space="preserve">length of returned array</doc>
            <type name="guint" c:type="guint*"/>
          </parameter>
        </parameters>
      </method>
      <method name="get_thumbnail" c:identifier="poppler_page_get_thumbnail">
        <doc xml:space="preserve">Get the embedded thumbnail for the specified page.  If the document
doesn't have an embedded thumbnail for the page, this function
returns %NULL.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">the tumbnail as a cairo_surface_t or %NULL if the document
doesn't have a thumbnail for this page.</doc>
          <type name="cairo.Surface" c:type="cairo_surface_t*"/>
        </return-value>
        <parameters>
          <instance-parameter name="page" transfer-ownership="none">
            <doc xml:space="preserve">the #PopplerPage to get the thumbnail for</doc>
            <type name="Page" c:type="PopplerPage*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_thumbnail_size"
              c:identifier="poppler_page_get_thumbnail_size">
        <doc xml:space="preserve">Returns %TRUE if @page has a thumbnail associated with it.  It also
fills in @width and @height with the width and height of the
thumbnail.  The values of width and height are not changed if no
appropriate thumbnail exists.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">%TRUE, if @page has a thumbnail associated with it.</doc>
          <type name="gboolean" c:type="gboolean"/>
        </return-value>
        <parameters>
          <instance-parameter name="page" transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerPage</doc>
            <type name="Page" c:type="PopplerPage*"/>
          </instance-parameter>
          <parameter name="width"
                     direction="out"
                     caller-allocates="0"
                     transfer-ownership="full">
            <doc xml:space="preserve">return location for width</doc>
            <type name="gint" c:type="int*"/>
          </parameter>
          <parameter name="height"
                     direction="out"
                     caller-allocates="0"
                     transfer-ownership="full">
            <doc xml:space="preserve">return location for height</doc>
            <type name="gint" c:type="int*"/>
          </parameter>
        </parameters>
      </method>
      <method name="get_transition" c:identifier="poppler_page_get_transition">
        <doc xml:space="preserve">Returns the transition effect of @page</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a #PopplerPageTransition or %NULL.</doc>
          <type name="PageTransition" c:type="PopplerPageTransition*"/>
        </return-value>
        <parameters>
          <instance-parameter name="page" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerPage</doc>
            <type name="Page" c:type="PopplerPage*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="remove_annot"
              c:identifier="poppler_page_remove_annot"
              version="0.22">
        <doc xml:space="preserve">Removes annotation @annot from @page</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="page" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerPage</doc>
            <type name="Page" c:type="PopplerPage*"/>
          </instance-parameter>
          <parameter name="annot" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerAnnot to remove</doc>
            <type name="Annot" c:type="PopplerAnnot*"/>
          </parameter>
        </parameters>
      </method>
      <method name="render" c:identifier="poppler_page_render">
        <doc xml:space="preserve">Render the page to the given cairo context. This function
is for rendering a page that will be displayed. If you want
to render a page that will be printed use
poppler_page_render_for_printing() instead</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="page" transfer-ownership="none">
            <doc xml:space="preserve">the page to render from</doc>
            <type name="Page" c:type="PopplerPage*"/>
          </instance-parameter>
          <parameter name="cairo" transfer-ownership="none">
            <doc xml:space="preserve">cairo context to render to</doc>
            <type name="cairo.Context" c:type="cairo_t*"/>
          </parameter>
        </parameters>
      </method>
      <method name="render_for_printing"
              c:identifier="poppler_page_render_for_printing">
        <doc xml:space="preserve">Render the page to the given cairo context for printing.</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="page" transfer-ownership="none">
            <doc xml:space="preserve">the page to render from</doc>
            <type name="Page" c:type="PopplerPage*"/>
          </instance-parameter>
          <parameter name="cairo" transfer-ownership="none">
            <doc xml:space="preserve">cairo context to render to</doc>
            <type name="cairo.Context" c:type="cairo_t*"/>
          </parameter>
        </parameters>
      </method>
      <method name="render_for_printing_with_options"
              c:identifier="poppler_page_render_for_printing_with_options"
              version="0.16">
        <doc xml:space="preserve">Render the page to the given cairo context for printing
with the specified options</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="page" transfer-ownership="none">
            <doc xml:space="preserve">the page to render from</doc>
            <type name="Page" c:type="PopplerPage*"/>
          </instance-parameter>
          <parameter name="cairo" transfer-ownership="none">
            <doc xml:space="preserve">cairo context to render to</doc>
            <type name="cairo.Context" c:type="cairo_t*"/>
          </parameter>
          <parameter name="options" transfer-ownership="none">
            <doc xml:space="preserve">print options</doc>
            <type name="PrintFlags" c:type="PopplerPrintFlags"/>
          </parameter>
        </parameters>
      </method>
      <method name="render_selection"
              c:identifier="poppler_page_render_selection">
        <doc xml:space="preserve">Render the selection specified by @selection for @page to
the given cairo context.  The selection will be rendered, using
@glyph_color for the glyphs and @background_color for the selection
background.

If non-NULL, @old_selection specifies the selection that is already
rendered to @cairo, in which case this function will (some day)
only render the changed part of the selection.</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="page" transfer-ownership="none">
            <doc xml:space="preserve">the #PopplerPage for which to render selection</doc>
            <type name="Page" c:type="PopplerPage*"/>
          </instance-parameter>
          <parameter name="cairo" transfer-ownership="none">
            <doc xml:space="preserve">cairo context to render to</doc>
            <type name="cairo.Context" c:type="cairo_t*"/>
          </parameter>
          <parameter name="selection" transfer-ownership="none">
            <doc xml:space="preserve">start and end point of selection as a rectangle</doc>
            <type name="Rectangle" c:type="PopplerRectangle*"/>
          </parameter>
          <parameter name="old_selection" transfer-ownership="none">
            <doc xml:space="preserve">previous selection</doc>
            <type name="Rectangle" c:type="PopplerRectangle*"/>
          </parameter>
          <parameter name="style" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerSelectionStyle</doc>
            <type name="SelectionStyle" c:type="PopplerSelectionStyle"/>
          </parameter>
          <parameter name="glyph_color" transfer-ownership="none">
            <doc xml:space="preserve">color to use for drawing glyphs</doc>
            <type name="Color" c:type="PopplerColor*"/>
          </parameter>
          <parameter name="background_color" transfer-ownership="none">
            <doc xml:space="preserve">color to use for the selection background</doc>
            <type name="Color" c:type="PopplerColor*"/>
          </parameter>
        </parameters>
      </method>
      <method name="render_to_ps" c:identifier="poppler_page_render_to_ps">
        <doc xml:space="preserve">Render the page on a postscript file</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="page" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerPage</doc>
            <type name="Page" c:type="PopplerPage*"/>
          </instance-parameter>
          <parameter name="ps_file" transfer-ownership="none">
            <doc xml:space="preserve">the PopplerPSFile to render to</doc>
            <type name="PSFile" c:type="PopplerPSFile*"/>
          </parameter>
        </parameters>
      </method>
      <property name="label" transfer-ownership="none">
        <doc xml:space="preserve">The label of the page or %NULL. See also poppler_page_get_label()</doc>
        <type name="utf8" c:type="gchar*"/>
      </property>
    </class>
    <enumeration name="PageLayout"
                 glib:type-name="PopplerPageLayout"
                 glib:get-type="poppler_page_layout_get_type"
                 c:type="PopplerPageLayout">
      <doc xml:space="preserve">Page layout types</doc>
      <member name="unset"
              value="0"
              c:identifier="POPPLER_PAGE_LAYOUT_UNSET"
              glib:nick="unset">
        <doc xml:space="preserve">no specific layout set</doc>
      </member>
      <member name="single_page"
              value="1"
              c:identifier="POPPLER_PAGE_LAYOUT_SINGLE_PAGE"
              glib:nick="single-page">
        <doc xml:space="preserve">one page at a time</doc>
      </member>
      <member name="one_column"
              value="2"
              c:identifier="POPPLER_PAGE_LAYOUT_ONE_COLUMN"
              glib:nick="one-column">
        <doc xml:space="preserve">pages in one column</doc>
      </member>
      <member name="two_column_left"
              value="3"
              c:identifier="POPPLER_PAGE_LAYOUT_TWO_COLUMN_LEFT"
              glib:nick="two-column-left">
        <doc xml:space="preserve">pages in two columns with odd numbered pages on the left</doc>
      </member>
      <member name="two_column_right"
              value="4"
              c:identifier="POPPLER_PAGE_LAYOUT_TWO_COLUMN_RIGHT"
              glib:nick="two-column-right">
        <doc xml:space="preserve">pages in two columns with odd numbered pages on the right</doc>
      </member>
      <member name="two_page_left"
              value="5"
              c:identifier="POPPLER_PAGE_LAYOUT_TWO_PAGE_LEFT"
              glib:nick="two-page-left">
        <doc xml:space="preserve">two pages at a time with odd numbered pages on the left</doc>
      </member>
      <member name="two_page_right"
              value="6"
              c:identifier="POPPLER_PAGE_LAYOUT_TWO_PAGE_RIGHT"
              glib:nick="two-page-right">
        <doc xml:space="preserve">two pages at a time with odd numbered pages on the right</doc>
      </member>
    </enumeration>
    <enumeration name="PageMode"
                 glib:type-name="PopplerPageMode"
                 glib:get-type="poppler_page_mode_get_type"
                 c:type="PopplerPageMode">
      <doc xml:space="preserve">Page modes</doc>
      <member name="unset"
              value="0"
              c:identifier="POPPLER_PAGE_MODE_UNSET"
              glib:nick="unset">
        <doc xml:space="preserve">no specific mode set</doc>
      </member>
      <member name="none"
              value="1"
              c:identifier="POPPLER_PAGE_MODE_NONE"
              glib:nick="none">
        <doc xml:space="preserve">neither document outline nor thumbnails visible</doc>
      </member>
      <member name="use_outlines"
              value="2"
              c:identifier="POPPLER_PAGE_MODE_USE_OUTLINES"
              glib:nick="use-outlines">
        <doc xml:space="preserve">document outline visible</doc>
      </member>
      <member name="use_thumbs"
              value="3"
              c:identifier="POPPLER_PAGE_MODE_USE_THUMBS"
              glib:nick="use-thumbs">
        <doc xml:space="preserve">thumbnails visible</doc>
      </member>
      <member name="full_screen"
              value="4"
              c:identifier="POPPLER_PAGE_MODE_FULL_SCREEN"
              glib:nick="full-screen">
        <doc xml:space="preserve">full-screen mode</doc>
      </member>
      <member name="use_oc"
              value="5"
              c:identifier="POPPLER_PAGE_MODE_USE_OC"
              glib:nick="use-oc">
        <doc xml:space="preserve">layers panel visible</doc>
      </member>
      <member name="use_attachments"
              value="6"
              c:identifier="POPPLER_PAGE_MODE_USE_ATTACHMENTS"
              glib:nick="use-attachments">
        <doc xml:space="preserve">attachments panel visible</doc>
      </member>
    </enumeration>
    <record name="PageTransition"
            c:type="PopplerPageTransition"
            glib:type-name="PopplerPageTransition"
            glib:get-type="poppler_page_transition_get_type"
            c:symbol-prefix="page_transition">
      <doc xml:space="preserve">A #PopplerPageTransition structures describes a visual transition
to use when moving between pages during a presentation</doc>
      <field name="type" writable="1">
        <doc xml:space="preserve">the type of transtition</doc>
        <type name="PageTransitionType" c:type="PopplerPageTransitionType"/>
      </field>
      <field name="alignment" writable="1">
        <doc xml:space="preserve">the dimension in which the transition effect shall occur.
Only for #POPPLER_PAGE_TRANSITION_SPLIT and #POPPLER_PAGE_TRANSITION_BLINDS transition types</doc>
        <type name="PageTransitionAlignment"
              c:type="PopplerPageTransitionAlignment"/>
      </field>
      <field name="direction" writable="1">
        <doc xml:space="preserve">the direccion of motion for the transition effect.
Only for #POPPLER_PAGE_TRANSITION_SPLIT, #POPPLER_PAGE_TRANSITION_BOX and #POPPLER_PAGE_TRANSITION_FLY
transition types</doc>
        <type name="PageTransitionDirection"
              c:type="PopplerPageTransitionDirection"/>
      </field>
      <field name="duration" writable="1">
        <doc xml:space="preserve">the duration of the transition effect</doc>
        <type name="gint" c:type="gint"/>
      </field>
      <field name="angle" writable="1">
        <doc xml:space="preserve">the direction in which the specified transition effect shall moves,
expressed in degrees counterclockwise starting from a left-to-right direction.
Only for #POPPLER_PAGE_TRANSITION_WIPE, #POPPLER_PAGE_TRANSITION_GLITTER, #POPPLER_PAGE_TRANSITION_FLY,
#POPPLER_PAGE_TRANSITION_COVER, #POPPLER_PAGE_TRANSITION_UNCOVER and #POPPLER_PAGE_TRANSITION_PUSH
transition types</doc>
        <type name="gint" c:type="gint"/>
      </field>
      <field name="scale" writable="1">
        <doc xml:space="preserve">the starting or ending scale at which the changes shall be drawn.
Only for #POPPLER_PAGE_TRANSITION_FLY transition type</doc>
        <type name="gdouble" c:type="gdouble"/>
      </field>
      <field name="rectangular" writable="1">
        <doc xml:space="preserve">whether the area that will be flown is rectangular and opaque.
Only for #POPPLER_PAGE_TRANSITION_FLY transition type</doc>
        <type name="gboolean" c:type="gboolean"/>
      </field>
      <field name="duration_real" writable="1">
        <type name="gdouble" c:type="gdouble"/>
      </field>
      <constructor name="new" c:identifier="poppler_page_transition_new">
        <doc xml:space="preserve">Creates a new #PopplerPageTransition</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new #PopplerPageTransition, use poppler_page_transition_free() to free it</doc>
          <type name="PageTransition" c:type="PopplerPageTransition*"/>
        </return-value>
      </constructor>
      <method name="copy" c:identifier="poppler_page_transition_copy">
        <doc xml:space="preserve">Creates a copy of @transition</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new allocated copy of @transition</doc>
          <type name="PageTransition" c:type="PopplerPageTransition*"/>
        </return-value>
        <parameters>
          <instance-parameter name="transition" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerPageTransition to copy</doc>
            <type name="PageTransition" c:type="PopplerPageTransition*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="free" c:identifier="poppler_page_transition_free">
        <doc xml:space="preserve">Frees the given #PopplerPageTransition</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="transition" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerPageTransition</doc>
            <type name="PageTransition" c:type="PopplerPageTransition*"/>
          </instance-parameter>
        </parameters>
      </method>
    </record>
    <enumeration name="PageTransitionAlignment"
                 glib:type-name="PopplerPageTransitionAlignment"
                 glib:get-type="poppler_page_transition_alignment_get_type"
                 c:type="PopplerPageTransitionAlignment">
      <doc xml:space="preserve">Page transition alignment types for #POPPLER_PAGE_TRANSITION_SPLIT
and #POPPLER_PAGE_TRANSITION_BLINDS transition types</doc>
      <member name="horizontal"
              value="0"
              c:identifier="POPPLER_PAGE_TRANSITION_HORIZONTAL"
              glib:nick="horizontal">
        <doc xml:space="preserve">horizontal dimension</doc>
      </member>
      <member name="vertical"
              value="1"
              c:identifier="POPPLER_PAGE_TRANSITION_VERTICAL"
              glib:nick="vertical">
        <doc xml:space="preserve">vertical dimension</doc>
      </member>
    </enumeration>
    <enumeration name="PageTransitionDirection"
                 glib:type-name="PopplerPageTransitionDirection"
                 glib:get-type="poppler_page_transition_direction_get_type"
                 c:type="PopplerPageTransitionDirection">
      <doc xml:space="preserve">Page transition direction types for #POPPLER_PAGE_TRANSITION_SPLIT,
#POPPLER_PAGE_TRANSITION_BOX and #POPPLER_PAGE_TRANSITION_FLY transition types</doc>
      <member name="inward"
              value="0"
              c:identifier="POPPLER_PAGE_TRANSITION_INWARD"
              glib:nick="inward">
        <doc xml:space="preserve">inward from the edges of the page</doc>
      </member>
      <member name="outward"
              value="1"
              c:identifier="POPPLER_PAGE_TRANSITION_OUTWARD"
              glib:nick="outward">
        <doc xml:space="preserve">outward from the center of the page</doc>
      </member>
    </enumeration>
    <enumeration name="PageTransitionType"
                 glib:type-name="PopplerPageTransitionType"
                 glib:get-type="poppler_page_transition_type_get_type"
                 c:type="PopplerPageTransitionType">
      <doc xml:space="preserve">Page transition types</doc>
      <member name="replace"
              value="0"
              c:identifier="POPPLER_PAGE_TRANSITION_REPLACE"
              glib:nick="replace">
        <doc xml:space="preserve">the new page replace the old one</doc>
      </member>
      <member name="split"
              value="1"
              c:identifier="POPPLER_PAGE_TRANSITION_SPLIT"
              glib:nick="split">
        <doc xml:space="preserve">two lines sweep across the screen, revealing the new page</doc>
      </member>
      <member name="blinds"
              value="2"
              c:identifier="POPPLER_PAGE_TRANSITION_BLINDS"
              glib:nick="blinds">
        <doc xml:space="preserve">multiple lines, evenly spaced across the screen, synchronously
sweep in the same direction to reveal the new page</doc>
      </member>
      <member name="box"
              value="3"
              c:identifier="POPPLER_PAGE_TRANSITION_BOX"
              glib:nick="box">
        <doc xml:space="preserve">a rectangular box sweeps inward from the edges of the page or
outward from the center revealing the new page</doc>
      </member>
      <member name="wipe"
              value="4"
              c:identifier="POPPLER_PAGE_TRANSITION_WIPE"
              glib:nick="wipe">
        <doc xml:space="preserve">a single line sweeps across the screen from one edge to the other
revealing the new page</doc>
      </member>
      <member name="dissolve"
              value="5"
              c:identifier="POPPLER_PAGE_TRANSITION_DISSOLVE"
              glib:nick="dissolve">
        <doc xml:space="preserve">the old page dissolves gradually to reveal the new one</doc>
      </member>
      <member name="glitter"
              value="6"
              c:identifier="POPPLER_PAGE_TRANSITION_GLITTER"
              glib:nick="glitter">
        <doc xml:space="preserve">similar to #POPPLER_PAGE_TRANSITION_DISSOLVE, except that the effect
sweeps across the page in a wide band moving from one side of the screen to the other</doc>
      </member>
      <member name="fly"
              value="7"
              c:identifier="POPPLER_PAGE_TRANSITION_FLY"
              glib:nick="fly">
        <doc xml:space="preserve">changes are flown out or in to or from a location that is offscreen</doc>
      </member>
      <member name="push"
              value="8"
              c:identifier="POPPLER_PAGE_TRANSITION_PUSH"
              glib:nick="push">
        <doc xml:space="preserve">the old page slides off the screen while the new page slides in</doc>
      </member>
      <member name="cover"
              value="9"
              c:identifier="POPPLER_PAGE_TRANSITION_COVER"
              glib:nick="cover">
        <doc xml:space="preserve">the new page slides on to the screen covering the old page</doc>
      </member>
      <member name="uncover"
              value="10"
              c:identifier="POPPLER_PAGE_TRANSITION_UNCOVER"
              glib:nick="uncover">
        <doc xml:space="preserve">the old page slides off the screen uncovering the new page</doc>
      </member>
      <member name="fade"
              value="11"
              c:identifier="POPPLER_PAGE_TRANSITION_FADE"
              glib:nick="fade">
        <doc xml:space="preserve">the new page gradually becomes visible through the old one</doc>
      </member>
    </enumeration>
    <bitfield name="Permissions"
              glib:type-name="PopplerPermissions"
              glib:get-type="poppler_permissions_get_type"
              c:type="PopplerPermissions">
      <doc xml:space="preserve">Permissions</doc>
      <member name="ok_to_print"
              value="1"
              c:identifier="POPPLER_PERMISSIONS_OK_TO_PRINT"
              glib:nick="ok-to-print">
        <doc xml:space="preserve">document can be printer</doc>
      </member>
      <member name="ok_to_modify"
              value="2"
              c:identifier="POPPLER_PERMISSIONS_OK_TO_MODIFY"
              glib:nick="ok-to-modify">
        <doc xml:space="preserve">document contents can be modified</doc>
      </member>
      <member name="ok_to_copy"
              value="4"
              c:identifier="POPPLER_PERMISSIONS_OK_TO_COPY"
              glib:nick="ok-to-copy">
        <doc xml:space="preserve">document can be copied</doc>
      </member>
      <member name="ok_to_add_notes"
              value="8"
              c:identifier="POPPLER_PERMISSIONS_OK_TO_ADD_NOTES"
              glib:nick="ok-to-add-notes">
        <doc xml:space="preserve">annotations can added to the document</doc>
      </member>
      <member name="ok_to_fill_form"
              value="16"
              c:identifier="POPPLER_PERMISSIONS_OK_TO_FILL_FORM"
              glib:nick="ok-to-fill-form">
        <doc xml:space="preserve">interactive form fields can be filled in</doc>
      </member>
      <member name="ok_to_extract_contents"
              value="32"
              c:identifier="POPPLER_PERMISSIONS_OK_TO_EXTRACT_CONTENTS"
              glib:nick="ok-to-extract-contents">
        <doc xml:space="preserve">extract text and graphics
(in support of accessibility to users with disabilities or for other purposes). Since 0.18</doc>
      </member>
      <member name="ok_to_assemble"
              value="64"
              c:identifier="POPPLER_PERMISSIONS_OK_TO_ASSEMBLE"
              glib:nick="ok-to-assemble">
        <doc xml:space="preserve">assemble the document (insert, rotate, or delete pages and create
bookmarks or thumbnail images). Since 0.18</doc>
      </member>
      <member name="ok_to_print_high_resolution"
              value="128"
              c:identifier="POPPLER_PERMISSIONS_OK_TO_PRINT_HIGH_RESOLUTION"
              glib:nick="ok-to-print-high-resolution">
        <doc xml:space="preserve">document can be printer at high resolution. Since 0.18</doc>
      </member>
      <member name="full"
              value="255"
              c:identifier="POPPLER_PERMISSIONS_FULL"
              glib:nick="full">
        <doc xml:space="preserve">document permits all operations</doc>
      </member>
    </bitfield>
    <record name="Point"
            c:type="PopplerPoint"
            glib:type-name="PopplerPoint"
            glib:get-type="poppler_point_get_type"
            c:symbol-prefix="point">
      <doc xml:space="preserve">A #PopplerPoint is used to describe a location point on a page</doc>
      <field name="x" writable="1">
        <doc xml:space="preserve">x coordinate</doc>
        <type name="gdouble" c:type="gdouble"/>
      </field>
      <field name="y" writable="1">
        <doc xml:space="preserve">y coordinate</doc>
        <type name="gdouble" c:type="gdouble"/>
      </field>
      <constructor name="new" c:identifier="poppler_point_new" version="0.26">
        <doc xml:space="preserve">Creates a new #PopplerPoint. It must be freed with poppler_point_free() after use.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new #PopplerPoint</doc>
          <type name="Point" c:type="PopplerPoint*"/>
        </return-value>
      </constructor>
      <method name="copy" c:identifier="poppler_point_copy" version="0.26">
        <doc xml:space="preserve">Creates a copy of @point. The copy must be freed with poppler_point_free()
after use.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new allocated copy of @point</doc>
          <type name="Point" c:type="PopplerPoint*"/>
        </return-value>
        <parameters>
          <instance-parameter name="point" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerPoint to copy</doc>
            <type name="Point" c:type="PopplerPoint*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="free" c:identifier="poppler_point_free" version="0.26">
        <doc xml:space="preserve">Frees the memory used by @point</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="point" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerPoint</doc>
            <type name="Point" c:type="PopplerPoint*"/>
          </instance-parameter>
        </parameters>
      </method>
    </record>
    <bitfield name="PrintFlags"
              version="0.16"
              glib:type-name="PopplerPrintFlags"
              glib:get-type="poppler_print_flags_get_type"
              c:type="PopplerPrintFlags">
      <doc xml:space="preserve">Printing flags</doc>
      <member name="document"
              value="0"
              c:identifier="POPPLER_PRINT_DOCUMENT"
              glib:nick="document">
        <doc xml:space="preserve">print main document contents</doc>
      </member>
      <member name="markup_annots"
              value="1"
              c:identifier="POPPLER_PRINT_MARKUP_ANNOTS"
              glib:nick="markup-annots">
        <doc xml:space="preserve">print document and markup annotations</doc>
      </member>
      <member name="stamp_annots_only"
              value="2"
              c:identifier="POPPLER_PRINT_STAMP_ANNOTS_ONLY"
              glib:nick="stamp-annots-only">
        <doc xml:space="preserve">print document and only stamp annotations</doc>
      </member>
      <member name="all"
              value="1"
              c:identifier="POPPLER_PRINT_ALL"
              glib:nick="all">
        <doc xml:space="preserve">print main document contents and all markup annotations</doc>
      </member>
    </bitfield>
    <record name="Quadrilateral"
            c:type="PopplerQuadrilateral"
            glib:type-name="PopplerQuadrilateral"
            glib:get-type="poppler_quadrilateral_get_type"
            c:symbol-prefix="quadrilateral">
      <doc xml:space="preserve">A #PopplerQuadrilateral is used to describe rectangle-like polygon
 with arbitrary inclination on a page.

 Since: 0.26</doc>
      <field name="p1" writable="1">
        <doc xml:space="preserve">a #PopplerPoint with the first vertex coordinates</doc>
        <type name="Point" c:type="PopplerPoint"/>
      </field>
      <field name="p2" writable="1">
        <doc xml:space="preserve">a #PopplerPoint with the second vertex coordinates</doc>
        <type name="Point" c:type="PopplerPoint"/>
      </field>
      <field name="p3" writable="1">
        <doc xml:space="preserve">a #PopplerPoint with the third vertex coordinates</doc>
        <type name="Point" c:type="PopplerPoint"/>
      </field>
      <field name="p4" writable="1">
        <doc xml:space="preserve">a #PopplerPoint with the fourth vertex coordinates</doc>
        <type name="Point" c:type="PopplerPoint"/>
      </field>
      <constructor name="new"
                   c:identifier="poppler_quadrilateral_new"
                   version="0.26">
        <doc xml:space="preserve">Creates a new #PopplerQuadrilateral. It must be freed with poppler_quadrilateral_free() after use.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new #PopplerQuadrilateral.</doc>
          <type name="Quadrilateral" c:type="PopplerQuadrilateral*"/>
        </return-value>
      </constructor>
      <method name="copy"
              c:identifier="poppler_quadrilateral_copy"
              version="0.26">
        <doc xml:space="preserve">Creates a copy of @quad. The copy must be freed with poppler_quadrilateral_free() after use.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new allocated copy of @quad</doc>
          <type name="Quadrilateral" c:type="PopplerQuadrilateral*"/>
        </return-value>
        <parameters>
          <instance-parameter name="quad" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerQuadrilateral to copy</doc>
            <type name="Quadrilateral" c:type="PopplerQuadrilateral*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="free"
              c:identifier="poppler_quadrilateral_free"
              version="0.26">
        <doc xml:space="preserve">Frees the memory used by @quad</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="quad" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerQuadrilateral</doc>
            <type name="Quadrilateral" c:type="PopplerQuadrilateral*"/>
          </instance-parameter>
        </parameters>
      </method>
    </record>
    <record name="Rectangle"
            c:type="PopplerRectangle"
            glib:type-name="PopplerRectangle"
            glib:get-type="poppler_rectangle_get_type"
            c:symbol-prefix="rectangle">
      <doc xml:space="preserve">A #PopplerRectangle is used to describe
locations on a page and bounding boxes</doc>
      <field name="x1" writable="1">
        <doc xml:space="preserve">x coordinate of lower left corner</doc>
        <type name="gdouble" c:type="gdouble"/>
      </field>
      <field name="y1" writable="1">
        <doc xml:space="preserve">y coordinate of lower left corner</doc>
        <type name="gdouble" c:type="gdouble"/>
      </field>
      <field name="x2" writable="1">
        <doc xml:space="preserve">x coordinate of upper right corner</doc>
        <type name="gdouble" c:type="gdouble"/>
      </field>
      <field name="y2" writable="1">
        <doc xml:space="preserve">y coordinate of upper right corner</doc>
        <type name="gdouble" c:type="gdouble"/>
      </field>
      <constructor name="new" c:identifier="poppler_rectangle_new">
        <doc xml:space="preserve">Creates a new #PopplerRectangle</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new #PopplerRectangle, use poppler_rectangle_free() to free it</doc>
          <type name="Rectangle" c:type="PopplerRectangle*"/>
        </return-value>
      </constructor>
      <method name="copy" c:identifier="poppler_rectangle_copy">
        <doc xml:space="preserve">Creates a copy of @rectangle</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new allocated copy of @rectangle</doc>
          <type name="Rectangle" c:type="PopplerRectangle*"/>
        </return-value>
        <parameters>
          <instance-parameter name="rectangle" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerRectangle to copy</doc>
            <type name="Rectangle" c:type="PopplerRectangle*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="free" c:identifier="poppler_rectangle_free">
        <doc xml:space="preserve">Frees the given #PopplerRectangle</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="rectangle" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerRectangle</doc>
            <type name="Rectangle" c:type="PopplerRectangle*"/>
          </instance-parameter>
        </parameters>
      </method>
    </record>
    <enumeration name="SelectionStyle"
                 glib:type-name="PopplerSelectionStyle"
                 glib:get-type="poppler_selection_style_get_type"
                 c:type="PopplerSelectionStyle">
      <doc xml:space="preserve">Selection styles</doc>
      <member name="glyph"
              value="0"
              c:identifier="POPPLER_SELECTION_GLYPH"
              glib:nick="glyph">
        <doc xml:space="preserve">glyph is the minimum unit for selection</doc>
      </member>
      <member name="word"
              value="1"
              c:identifier="POPPLER_SELECTION_WORD"
              glib:nick="word">
        <doc xml:space="preserve">word is the minimum unit for selection</doc>
      </member>
      <member name="line"
              value="2"
              c:identifier="POPPLER_SELECTION_LINE"
              glib:nick="line">
        <doc xml:space="preserve">line is the minimum unit for selection</doc>
      </member>
    </enumeration>
    <enumeration name="StructureBlockAlign"
                 glib:type-name="PopplerStructureBlockAlign"
                 glib:get-type="poppler_structure_block_align_get_type"
                 c:type="PopplerStructureBlockAlign">
      <member name="before"
              value="0"
              c:identifier="POPPLER_STRUCTURE_BLOCK_ALIGN_BEFORE"
              glib:nick="before">
      </member>
      <member name="middle"
              value="1"
              c:identifier="POPPLER_STRUCTURE_BLOCK_ALIGN_MIDDLE"
              glib:nick="middle">
      </member>
      <member name="after"
              value="2"
              c:identifier="POPPLER_STRUCTURE_BLOCK_ALIGN_AFTER"
              glib:nick="after">
      </member>
      <member name="justify"
              value="3"
              c:identifier="POPPLER_STRUCTURE_BLOCK_ALIGN_JUSTIFY"
              glib:nick="justify">
      </member>
    </enumeration>
    <enumeration name="StructureBorderStyle"
                 glib:type-name="PopplerStructureBorderStyle"
                 glib:get-type="poppler_structure_border_style_get_type"
                 c:type="PopplerStructureBorderStyle">
      <member name="none"
              value="0"
              c:identifier="POPPLER_STRUCTURE_BORDER_STYLE_NONE"
              glib:nick="none">
      </member>
      <member name="hidden"
              value="1"
              c:identifier="POPPLER_STRUCTURE_BORDER_STYLE_HIDDEN"
              glib:nick="hidden">
      </member>
      <member name="dotted"
              value="2"
              c:identifier="POPPLER_STRUCTURE_BORDER_STYLE_DOTTED"
              glib:nick="dotted">
      </member>
      <member name="dashed"
              value="3"
              c:identifier="POPPLER_STRUCTURE_BORDER_STYLE_DASHED"
              glib:nick="dashed">
      </member>
      <member name="solid"
              value="4"
              c:identifier="POPPLER_STRUCTURE_BORDER_STYLE_SOLID"
              glib:nick="solid">
      </member>
      <member name="double"
              value="5"
              c:identifier="POPPLER_STRUCTURE_BORDER_STYLE_DOUBLE"
              glib:nick="double">
      </member>
      <member name="groove"
              value="6"
              c:identifier="POPPLER_STRUCTURE_BORDER_STYLE_GROOVE"
              glib:nick="groove">
      </member>
      <member name="inset"
              value="7"
              c:identifier="POPPLER_STRUCTURE_BORDER_STYLE_INSET"
              glib:nick="inset">
      </member>
      <member name="outset"
              value="8"
              c:identifier="POPPLER_STRUCTURE_BORDER_STYLE_OUTSET"
              glib:nick="outset">
      </member>
    </enumeration>
    <class name="StructureElement"
           c:symbol-prefix="structure_element"
           c:type="PopplerStructureElement"
           parent="GObject.Object"
           glib:type-name="PopplerStructureElement"
           glib:get-type="poppler_structure_element_get_type">
      <method name="get_abbreviation"
              c:identifier="poppler_structure_element_get_abbreviation">
        <return-value transfer-ownership="full">
          <type name="utf8" c:type="gchar*"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_actual_text"
              c:identifier="poppler_structure_element_get_actual_text"
              version="0.26">
        <doc xml:space="preserve">Obtains the actual text enclosed by the element (and its child elements).
The actual text is mostly used for non-text elements like images and
figures which &lt;emphasis&gt;do&lt;/emphasis&gt; have the graphical appearance of text, like
a logo. For those the actual text is the equivalent text to those
graphical elements which look like text when rendered.

Note that for elements containing proper text, the function
poppler_structure_element_get_text() must be used instead.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">The actual text for the element, or %NULL
   if not defined.</doc>
          <type name="utf8" c:type="gchar*"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureElement</doc>
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_alt_text"
              c:identifier="poppler_structure_element_get_alt_text"
              version="0.26">
        <doc xml:space="preserve">Obtains the “alternate” text representation of the element (and its child
elements). This is mostly used for non-text elements like images and
figures, to specify a textual description of the element.

Note that for elements containing proper text, the function
poppler_structure_element_get_text() must be used instead.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">The alternate text representation for the
   element, or %NULL if not defined.</doc>
          <type name="utf8" c:type="gchar*"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureElement</doc>
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_background_color"
              c:identifier="poppler_structure_element_get_background_color"
              version="0.26">
        <doc xml:space="preserve">Obtains the background color of the element. If this attribute is
not specified, the element shall be treated as if it were transparent.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">%TRUE if a color is defined for the element,
   %FALSE otherwise.</doc>
          <type name="gboolean" c:type="gboolean"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureElement</doc>
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
          <parameter name="color"
                     direction="out"
                     caller-allocates="1"
                     transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerColor.</doc>
            <type name="Color" c:type="PopplerColor*"/>
          </parameter>
        </parameters>
      </method>
      <method name="get_baseline_shift"
              c:identifier="poppler_structure_element_get_baseline_shift"
              version="0.26">
        <doc xml:space="preserve">Obtains how much the text contained in the inline-level structure element should be shifted,
measuring from the baseline of the glyphs.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">A numeric value.</doc>
          <type name="gdouble" c:type="gdouble"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureElement</doc>
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_block_align"
              c:identifier="poppler_structure_element_get_block_align"
              version="0.26">
        <doc xml:space="preserve">Obtains the block-alignment mode of the block-level structure element.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">A #PopplerStructureBlockAlign value.</doc>
          <type name="StructureBlockAlign"
                c:type="PopplerStructureBlockAlign"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureElement</doc>
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_border_color"
              c:identifier="poppler_structure_element_get_border_color"
              version="0.26">
        <doc xml:space="preserve">Obtains the color of border around the element. The result values
are in before-after-start-end ordering (for the typical Western
left-to-right writing, that is top-bottom-left-right).
If this attribute is not specified, the border color for this element shall
be the current text fill color in effect at the start of its associated
content.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">%TRUE if a color is defined for the element,
   %FALSE otherwise.</doc>
          <type name="gboolean" c:type="gboolean"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureElement</doc>
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
          <parameter name="colors"
                     direction="out"
                     caller-allocates="1"
                     transfer-ownership="none">
            <doc xml:space="preserve">An array
   of four #PopplerColor.</doc>
            <array zero-terminated="0" c:type="PopplerColor*" fixed-size="4">
              <type name="Color"/>
            </array>
          </parameter>
        </parameters>
      </method>
      <method name="get_border_style"
              c:identifier="poppler_structure_element_get_border_style"
              version="0.26">
        <doc xml:space="preserve">Obtains the border style of a structure element. The result values
are in before-after-start-end ordering. For example, using Western
left-to-right writing, that is top-bottom-left-right.</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureElement</doc>
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
          <parameter name="border_styles"
                     direction="out"
                     caller-allocates="0"
                     transfer-ownership="full">
            <doc xml:space="preserve">
   An array of four #PopplerStructureBorderStyle elements.</doc>
            <array zero-terminated="0"
                   c:type="PopplerStructureBorderStyle*"
                   fixed-size="4">
              <type name="StructureBorderStyle"/>
            </array>
          </parameter>
        </parameters>
      </method>
      <method name="get_border_thickness"
              c:identifier="poppler_structure_element_get_border_thickness"
              version="0.26">
        <doc xml:space="preserve">Obtains the thickness of the border of an element. The result values
are in before-after-start-end ordering (for the typical Western
left-to-right writing, that is top-bottom-left-right).
A value of 0 indicates that the border shall not be drawn.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">%TRUE if the border thickness attribute is defined for
   the element, %FALSE otherwise.</doc>
          <type name="gboolean" c:type="gboolean"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureElement</doc>
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
          <parameter name="border_thicknesses"
                     direction="out"
                     caller-allocates="0"
                     transfer-ownership="full">
            <doc xml:space="preserve">
   Array with the four values of border thicknesses.</doc>
            <array zero-terminated="0" c:type="gdouble*" fixed-size="4">
              <type name="gdouble"/>
            </array>
          </parameter>
        </parameters>
      </method>
      <method name="get_bounding_box"
              c:identifier="poppler_structure_element_get_bounding_box"
              version="0.26">
        <doc xml:space="preserve">Obtains the size of the bounding box of a block-level structure element.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">%TRUE if a bounding box is defined for the element,
   %FALSE otherwise.</doc>
          <type name="gboolean" c:type="gboolean"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureElement</doc>
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
          <parameter name="bounding_box"
                     direction="out"
                     caller-allocates="1"
                     transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerRectangle.</doc>
            <type name="Rectangle" c:type="PopplerRectangle*"/>
          </parameter>
        </parameters>
      </method>
      <method name="get_color"
              c:identifier="poppler_structure_element_get_color"
              version="0.26">
        <doc xml:space="preserve">Obtains the color of the content contained in the element.
If this attribute is not specified, the color for this element shall
be the current text fill color in effect at the start of its associated content.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">%TRUE if a color is defined for the element,
   %FALSE otherwise.</doc>
          <type name="gboolean" c:type="gboolean"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureElement</doc>
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
          <parameter name="color"
                     direction="out"
                     caller-allocates="1"
                     transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerColor.</doc>
            <type name="Color" c:type="PopplerColor*"/>
          </parameter>
        </parameters>
      </method>
      <method name="get_column_count"
              c:identifier="poppler_structure_element_get_column_count"
              version="0.26">
        <doc xml:space="preserve">Obtains the number of columns used to lay out the content contained
in the grouping element.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">Number of columns.</doc>
          <type name="guint" c:type="guint"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureElement</doc>
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_column_gaps"
              c:identifier="poppler_structure_element_get_column_gaps"
              version="0.26">
        <doc xml:space="preserve">Obtains the size of the gaps in between adjacent columns. Returns an
array of elements: the first one is the size of the gap in between
columns 1 and 2, second is the size between columns 2 and 3, and so on.

For elements which use a single column, %NULL is returned and @n_values
is set to zero.

If the attribute is undefined, %NULL is returned and @n_values is set
to a non-zero value.

The array with the results is allocated by the function. When it is
not needed anymore, be sure to call g_free() on it.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">
   Array containing the values for the column gaps, or %NULL if the
   array is empty or the attribute is not defined.</doc>
          <array length="0" zero-terminated="0" c:type="gdouble*">
            <type name="gdouble"/>
          </array>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureElement</doc>
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
          <parameter name="n_values"
                     direction="out"
                     caller-allocates="0"
                     transfer-ownership="full">
            <doc xml:space="preserve">Size of the returned array.</doc>
            <type name="guint" c:type="guint*"/>
          </parameter>
        </parameters>
      </method>
      <method name="get_column_widths"
              c:identifier="poppler_structure_element_get_column_widths"
              version="0.26">
        <doc xml:space="preserve">Obtains an array with the widths of the columns.

The array with the results is allocated by the function. When it is
not needed anymore, be sure to call g_free() on it.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">
   Array containing widths of the columns, or %NULL if the attribute
   is not defined.</doc>
          <array length="0" zero-terminated="0" c:type="gdouble*">
            <type name="gdouble"/>
          </array>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureElement</doc>
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
          <parameter name="n_values"
                     direction="out"
                     caller-allocates="0"
                     transfer-ownership="full">
            <doc xml:space="preserve">Size of the returned array.</doc>
            <type name="guint" c:type="guint*"/>
          </parameter>
        </parameters>
      </method>
      <method name="get_end_indent"
              c:identifier="poppler_structure_element_get_end_indent"
              version="0.26">
        <doc xml:space="preserve">Obtains the amount of indentation at the end of the block-level structure element.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">A numeric value.</doc>
          <type name="gdouble" c:type="gdouble"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureElement</doc>
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_form_description"
              c:identifier="poppler_structure_element_get_form_description"
              version="0.26">
        <doc xml:space="preserve">Obtains the textual description of the form element. Note that the
description is for informative purposes, and it is not intended
to be rendered. For example, assistive technologies may use the
description field to provide an alternate way of presenting an
element to the user.

The returned string is allocated by the function. When it is
not needed anymore, be sure to call g_free() on it.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">A string, or %NULL if the attribute
   is not defined.</doc>
          <type name="utf8" c:type="gchar*"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureElement</doc>
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_form_role"
              c:identifier="poppler_structure_element_get_form_role"
              version="0.26">
        <doc xml:space="preserve">Obtains the role of a form structure element that is part of a form, or is
a form field. This hints how the control for the element is intended
to be rendered.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">A #PopplerStructureFormRole value.</doc>
          <type name="StructureFormRole" c:type="PopplerStructureFormRole"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureElement</doc>
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_form_state"
              c:identifier="poppler_structure_element_get_form_state"
              version="0.26">
        <doc xml:space="preserve">For a structure element that is a form field, obtains in which state
the associated control is expected to be rendered.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">A #PopplerStructureFormState value.</doc>
          <type name="StructureFormState" c:type="PopplerStructureFormState"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureElement</doc>
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_glyph_orientation"
              c:identifier="poppler_structure_element_get_glyph_orientation"
              version="0.26">
        <doc xml:space="preserve">Obtains the glyph orientation for the text contained in a
inline-level structure element.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">A #PopplerStructureGlyphOrientation value.</doc>
          <type name="StructureGlyphOrientation"
                c:type="PopplerStructureGlyphOrientation"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureElement</doc>
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_height"
              c:identifier="poppler_structure_element_get_height"
              version="0.26">
        <doc xml:space="preserve">Obtains the height of the block-level structure element. Note that for elements which do
not specify a height, it has to be calculated, and in this case -1 is returned.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">A positive value if a width is defined, or -1
   if the height is to be calculated automatically.</doc>
          <type name="gdouble" c:type="gdouble"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureElement</doc>
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_id"
              c:identifier="poppler_structure_element_get_id"
              version="0.26">
        <doc xml:space="preserve">Obtains the identifier of an element.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">The identifier of the element (if
   defined), or %NULL.</doc>
          <type name="utf8" c:type="gchar*"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureElement</doc>
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_inline_align"
              c:identifier="poppler_structure_element_get_inline_align"
              version="0.26">
        <doc xml:space="preserve">Obtains the inline-alignment mode of the block-level structure element.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">A #PopplerStructureInlineAlign value.</doc>
          <type name="StructureInlineAlign"
                c:type="PopplerStructureInlineAlign"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureElement</doc>
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_kind"
              c:identifier="poppler_structure_element_get_kind"
              version="0.26">
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">A #PopplerStructureElementKind value.</doc>
          <type name="StructureElementKind"
                c:type="PopplerStructureElementKind"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureElement</doc>
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_language"
              c:identifier="poppler_structure_element_get_language"
              version="0.26">
        <doc xml:space="preserve">Obtains the language and country code for the content in an element,
in two-letter ISO format, e.g. &lt;code&gt;en_ES&lt;/code&gt;, or %NULL if not
defined.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">language and country code, or %NULL.</doc>
          <type name="utf8" c:type="gchar*"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureElement</doc>
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_line_height"
              c:identifier="poppler_structure_element_get_line_height"
              version="0.26">
        <doc xml:space="preserve">Obtains the line height for the text contained in the inline-level structure element.
Note that for elements which do not specify a line height, it has to be calculated,
and in this case -1 is returned.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">A positive value if a line height is defined, or -1
   if the height is to be calculated automatically.</doc>
          <type name="gdouble" c:type="gdouble"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureElement</doc>
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_list_numbering"
              c:identifier="poppler_structure_element_get_list_numbering"
              version="0.26">
        <doc xml:space="preserve">Obtains the list numbering style for list items.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">A #PopplerStructureListNumbering value.</doc>
          <type name="StructureListNumbering"
                c:type="PopplerStructureListNumbering"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureElement</doc>
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_padding"
              c:identifier="poppler_structure_element_get_padding"
              version="0.26">
        <doc xml:space="preserve">Obtains the padding of an element (space around it). The result
values are in before-after-start-end ordering. For example using
Western left-to-right writing, that is top-bottom-left-right.</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureElement</doc>
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
          <parameter name="paddings"
                     direction="out"
                     caller-allocates="0"
                     transfer-ownership="full">
            <doc xml:space="preserve">
   Padding for the four sides of the element.</doc>
            <array zero-terminated="0" c:type="gdouble*" fixed-size="4">
              <type name="gdouble"/>
            </array>
          </parameter>
        </parameters>
      </method>
      <method name="get_page"
              c:identifier="poppler_structure_element_get_page"
              version="0.26">
        <doc xml:space="preserve">Obtains the page number in which the element is contained.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">Number of the page that contains the element, of
   &lt;code&gt;-1&lt;/code&gt; if not defined.</doc>
          <type name="gint" c:type="gint"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureElement</doc>
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_placement"
              c:identifier="poppler_structure_element_get_placement"
              version="0.26">
        <doc xml:space="preserve">Obtains the placement type of the structure element.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">A #PopplerStructurePlacement value.</doc>
          <type name="StructurePlacement" c:type="PopplerStructurePlacement"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureElement</doc>
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_ruby_align"
              c:identifier="poppler_structure_element_get_ruby_align"
              version="0.26">
        <doc xml:space="preserve">Obtains the alignment for the ruby text contained in a
inline-level structure element.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">A #PopplerStructureRubyAlign value.</doc>
          <type name="StructureRubyAlign" c:type="PopplerStructureRubyAlign"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureElement</doc>
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_ruby_position"
              c:identifier="poppler_structure_element_get_ruby_position"
              version="0.26">
        <doc xml:space="preserve">Obtains the position for the ruby text contained in a
inline-level structure element.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">A #PopplerStructureRubyPosition value.</doc>
          <type name="StructureRubyPosition"
                c:type="PopplerStructureRubyPosition"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureElement</doc>
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_space_after"
              c:identifier="poppler_structure_element_get_space_after"
              version="0.26">
        <doc xml:space="preserve">Obtains the amount of empty space after the block-level structure element.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">A positive value.</doc>
          <type name="gdouble" c:type="gdouble"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureElement</doc>
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_space_before"
              c:identifier="poppler_structure_element_get_space_before"
              version="0.26">
        <doc xml:space="preserve">Obtains the amount of empty space before the block-level structure element.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">A positive value.</doc>
          <type name="gdouble" c:type="gdouble"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureElement</doc>
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_start_indent"
              c:identifier="poppler_structure_element_get_start_indent"
              version="0.26">
        <doc xml:space="preserve">Obtains the amount of indentation at the beginning of the block-level structure element.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">A numeric value.</doc>
          <type name="gdouble" c:type="gdouble"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureElement</doc>
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_table_border_style"
              c:identifier="poppler_structure_element_get_table_border_style"
              version="0.26">
        <doc xml:space="preserve">Obtains the table cell border style of a block-level structure element. The result values
are in before-after-start-end ordering. For example, using Western
left-to-right writing, that is top-bottom-left-right.</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureElement</doc>
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
          <parameter name="border_styles"
                     direction="out"
                     caller-allocates="0"
                     transfer-ownership="full">
            <doc xml:space="preserve">
   An array of four #PopplerStructureBorderStyle elements.</doc>
            <array zero-terminated="0"
                   c:type="PopplerStructureBorderStyle*"
                   fixed-size="4">
              <type name="StructureBorderStyle"/>
            </array>
          </parameter>
        </parameters>
      </method>
      <method name="get_table_column_span"
              c:identifier="poppler_structure_element_get_table_column_span"
              version="0.26">
        <doc xml:space="preserve">Obtains the number of columns the table element spans to.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">A positive, non-zero value.</doc>
          <type name="guint" c:type="guint"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureElement</doc>
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_table_headers"
              c:identifier="poppler_structure_element_get_table_headers"
              version="0.26">
        <doc xml:space="preserve">Obtains an array with the names of the table column headers. This is only
useful for table header row elements.

The array with the results is allocated by the function. The number
of items in the returned array can be obtained with g_strv_length().
The returned value must be freed using g_strfreev().</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">
   Zero-terminated array of strings with the table header names,
   or %NULL if the attribute is not defined.</doc>
          <array c:type="gchar**">
            <type name="utf8"/>
          </array>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureElement</doc>
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_table_padding"
              c:identifier="poppler_structure_element_get_table_padding"
              version="0.26">
        <doc xml:space="preserve">Obtains the padding between the table cell’s content rectangle and the
surrounding border of a block-level structure element. The result
values are in before-after-start-end ordering (for the typical
Western left-to-right writing, that is top-bottom-left-right).</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureElement</doc>
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
          <parameter name="paddings"
                     direction="out"
                     caller-allocates="0"
                     transfer-ownership="full">
            <doc xml:space="preserve">
   Padding for the four sides of the element.</doc>
            <array zero-terminated="0" c:type="gdouble*" fixed-size="4">
              <type name="gdouble"/>
            </array>
          </parameter>
        </parameters>
      </method>
      <method name="get_table_row_span"
              c:identifier="poppler_structure_element_get_table_row_span"
              version="0.26">
        <doc xml:space="preserve">Obtains the number of rows the table element spans to.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">A positive, non-zero value.</doc>
          <type name="guint" c:type="guint"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureElement</doc>
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_table_scope"
              c:identifier="poppler_structure_element_get_table_scope"
              version="0.26">
        <doc xml:space="preserve">Obtains the scope of a table structure element.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">A #PopplerStructureTableScope value.</doc>
          <type name="StructureTableScope"
                c:type="PopplerStructureTableScope"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureElement</doc>
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_table_summary"
              c:identifier="poppler_structure_element_get_table_summary"
              version="0.26">
        <doc xml:space="preserve">Obtains the textual summary of the contents of the table element. Note that
the summary is meant for informative purposes, and it is not intended
to be rendered. For example, assistive technologies may use the
description field to provide an alternate way of presenting an element
to the user, or a document indexer may want to scan it for additional
keywords.

The returned string is allocated by the function. When it is
not needed anymore, be sure to call g_free() on it.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">A string, or %NULL if the attribute
   is not defined.</doc>
          <type name="utf8" c:type="gchar*"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureElement</doc>
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_text"
              c:identifier="poppler_structure_element_get_text"
              version="0.26">
        <doc xml:space="preserve">Obtains the text enclosed by an element, or the text enclosed by the
elements in the subtree (including the element itself).</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">A string.</doc>
          <type name="utf8" c:type="gchar*"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureElement</doc>
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
          <parameter name="flags" transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureGetTextFlags value, or
   %POPPLER_STRUCTURE_GET_TEXT_NONE to disable all the flags.</doc>
            <type name="StructureGetTextFlags"
                  c:type="PopplerStructureGetTextFlags"/>
          </parameter>
        </parameters>
      </method>
      <method name="get_text_align"
              c:identifier="poppler_structure_element_get_text_align"
              version="0.26">
        <doc xml:space="preserve">Obtains the text alignment mode of the text contained into a
block-level structure element.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">A #PopplerStructureTextAlign value.</doc>
          <type name="StructureTextAlign" c:type="PopplerStructureTextAlign"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureElement</doc>
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_text_decoration_color"
              c:identifier="poppler_structure_element_get_text_decoration_color"
              version="0.26">
        <doc xml:space="preserve">Obtains the color of the text decoration for the text contained
in the inline-level structure element.
If this attribute is not specified, the color for this element shall be the current fill
color in effect at the start of its associated content.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">%TRUE if a color is defined for the element,
   %FALSE otherwise.</doc>
          <type name="gboolean" c:type="gboolean"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureElement</doc>
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
          <parameter name="color"
                     direction="out"
                     caller-allocates="1"
                     transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerColor.</doc>
            <type name="Color" c:type="PopplerColor*"/>
          </parameter>
        </parameters>
      </method>
      <method name="get_text_decoration_thickness"
              c:identifier="poppler_structure_element_get_text_decoration_thickness"
              version="0.26">
        <doc xml:space="preserve">Obtains the thickness of the text decoration for the text contained
in the inline-level structure element.
If this attribute is not specified, it shall be derived from the current
stroke thickness in effect at the start of the element’s associated content.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">Thickness of the text decoration, or NAN if not defined.</doc>
          <type name="gdouble" c:type="gdouble"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureElement</doc>
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_text_decoration_type"
              c:identifier="poppler_structure_element_get_text_decoration_type"
              version="0.26">
        <doc xml:space="preserve">Obtains the text decoration type of the text contained in the
inline-level structure element.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">A #PopplerStructureTextDecoration value.</doc>
          <type name="StructureTextDecoration"
                c:type="PopplerStructureTextDecoration"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureElement</doc>
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_text_indent"
              c:identifier="poppler_structure_element_get_text_indent"
              version="0.26">
        <doc xml:space="preserve">Obtains the amount of indentation of the text contained in the block-level structure element.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">A numeric value.</doc>
          <type name="gdouble" c:type="gdouble"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureElement</doc>
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_text_spans"
              c:identifier="poppler_structure_element_get_text_spans"
              version="0.26">
        <doc xml:space="preserve">Obtains the text enclosed by an element, as an array of #PopplerTextSpan
structures. Each item in the list is a piece of text which share the same
attributes, plus its attributes. The following example shows how to
obtain and free the text spans of an element:

&lt;informalexample&gt;&lt;programlisting&gt;
guint i, n_spans;
PopplerTextSpan **text_spans =
   poppler_structure_element_get_text_spans (element, &amp;n_spans);
/&lt;!-- --&gt;* Use the text spans *&lt;!-- --&gt;/
for (i = 0; i &lt; n_spans; i++)
   poppler_text_span_free (text_spans[i]);
g_free (text_spans);
&lt;/programlisting&gt;&lt;/informalexample&gt;</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">
   An array of #PopplerTextSpan elments.</doc>
          <array length="0" zero-terminated="0" c:type="PopplerTextSpan**">
            <type name="TextSpan"/>
          </array>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureElement</doc>
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
          <parameter name="n_text_spans"
                     direction="out"
                     caller-allocates="0"
                     transfer-ownership="full">
            <doc xml:space="preserve">A pointer to the location where the number of elements in
   the returned array will be stored.</doc>
            <type name="guint" c:type="guint*"/>
          </parameter>
        </parameters>
      </method>
      <method name="get_title"
              c:identifier="poppler_structure_element_get_title"
              version="0.26">
        <doc xml:space="preserve">Obtains the title of an element.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">The title of the element, or %NULL.</doc>
          <type name="utf8" c:type="gchar*"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureElement</doc>
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_width"
              c:identifier="poppler_structure_element_get_width"
              version="0.26">
        <doc xml:space="preserve">Obtains the width of the block-level structure element. Note that for elements which do
not specify a width, it has to be calculated, and in this case -1 is returned.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">A positive value if a width is defined, or -1
   if the width is to be calculated automatically.</doc>
          <type name="gdouble" c:type="gdouble"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureElement</doc>
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_writing_mode"
              c:identifier="poppler_structure_element_get_writing_mode"
              version="0.26">
        <doc xml:space="preserve">Obtains the writing mode (writing direction) of the content associated
with a structure element.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">A #PopplerStructureWritingMode value.</doc>
          <type name="StructureWritingMode"
                c:type="PopplerStructureWritingMode"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureElement</doc>
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="is_block"
              c:identifier="poppler_structure_element_is_block"
              version="0.26">
        <doc xml:space="preserve">Checks whether an element is a block element.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">%TRUE if  the element is a block element, or %FALSE otherwise.</doc>
          <type name="gboolean" c:type="gboolean"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureElement</doc>
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="is_content"
              c:identifier="poppler_structure_element_is_content"
              version="0.26">
        <doc xml:space="preserve">Checks whether an element is actual document content.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">%TRUE if the element is content, or %FALSE otherwise.</doc>
          <type name="gboolean" c:type="gboolean"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureElement</doc>
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="is_grouping"
              c:identifier="poppler_structure_element_is_grouping"
              version="0.26">
        <doc xml:space="preserve">Checks whether an element is a grouping element.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">%TRUE if the element is a grouping element, %FALSE
   otherwise.</doc>
          <type name="gboolean" c:type="gboolean"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureElement</doc>
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="is_inline"
              c:identifier="poppler_structure_element_is_inline"
              version="0.26">
        <doc xml:space="preserve">Checks whether an element is an inline element.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">%TRUE if the element is an inline element, or %FALSE otherwise.</doc>
          <type name="gboolean" c:type="gboolean"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_structure_element"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerStructureElement</doc>
            <type name="StructureElement" c:type="PopplerStructureElement*"/>
          </instance-parameter>
        </parameters>
      </method>
    </class>
    <record name="StructureElementIter"
            c:type="PopplerStructureElementIter"
            glib:type-name="PopplerStructureElementIter"
            glib:get-type="poppler_structure_element_iter_get_type"
            c:symbol-prefix="structure_element_iter">
      <constructor name="new"
                   c:identifier="poppler_structure_element_iter_new"
                   version="0.26">
        <doc xml:space="preserve">Returns the root #PopplerStructureElementIter for @document, or %NULL. The
returned value must be freed with poppler_structure_element_iter_free().

Documents may have an associated structure tree &amp;mdashmostly, Tagged-PDF
compliant documents&amp;mdash; which can be used to obtain information about
the document structure and its contents. Each node in the tree contains
a #PopplerStructureElement.

Here is a simple example that walks the whole tree:

&lt;informalexample&gt;&lt;programlisting&gt;
static void
walk_structure (PopplerStructureElementIter *iter)
{
  do {
    /&lt;!-- --&gt;* Get the element and do something with it *&lt;!-- --&gt;/
    PopplerStructureElementIter *child = poppler_structure_element_iter_get_child (iter);
    if (child)
      walk_structure (child);
    poppler_structure_element_iter_free (child);
  } while (poppler_structure_element_iter_next (iter));
}
...
{
  iter = poppler_structure_element_iter_new (document);
  walk_structure (iter);
  poppler_structure_element_iter_free (iter);
}
&lt;/programlisting&gt;&lt;/informalexample&gt;</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new #PopplerStructureElementIter, or %NULL if document
   doesn't have structure tree.</doc>
          <type name="StructureElementIter"
                c:type="PopplerStructureElementIter*"/>
        </return-value>
        <parameters>
          <parameter name="poppler_document" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerDocument.</doc>
            <type name="Document" c:type="PopplerDocument*"/>
          </parameter>
        </parameters>
      </constructor>
      <method name="copy"
              c:identifier="poppler_structure_element_iter_copy"
              version="0.26">
        <doc xml:space="preserve">Creates a new #PopplerStructureElementIter as a copy of @iter. The
returned value must be freed with poppler_structure_element_iter_free().</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new #PopplerStructureElementIter</doc>
          <type name="StructureElementIter"
                c:type="PopplerStructureElementIter*"/>
        </return-value>
        <parameters>
          <instance-parameter name="iter" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerStructureElementIter</doc>
            <type name="StructureElementIter"
                  c:type="PopplerStructureElementIter*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="free"
              c:identifier="poppler_structure_element_iter_free"
              version="0.26">
        <doc xml:space="preserve">Frees @iter.</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="iter" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerStructureElementIter</doc>
            <type name="StructureElementIter"
                  c:type="PopplerStructureElementIter*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_child"
              c:identifier="poppler_structure_element_iter_get_child"
              version="0.26">
        <doc xml:space="preserve">Returns a new iterator to the children elements of the
#PopplerStructureElement associated with @iter. The returned value must
be freed with poppler_structure_element_iter_free().</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new #PopplerStructureElementIter</doc>
          <type name="StructureElementIter"
                c:type="PopplerStructureElementIter*"/>
        </return-value>
        <parameters>
          <instance-parameter name="parent" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerStructureElementIter</doc>
            <type name="StructureElementIter"
                  c:type="PopplerStructureElementIter*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_element"
              c:identifier="poppler_structure_element_iter_get_element"
              version="0.26">
        <doc xml:space="preserve">Returns the #PopplerStructureElementIter associated with @iter.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new #PopplerStructureElementIter</doc>
          <type name="StructureElement" c:type="PopplerStructureElement*"/>
        </return-value>
        <parameters>
          <instance-parameter name="iter" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerStructureElementIter</doc>
            <type name="StructureElementIter"
                  c:type="PopplerStructureElementIter*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="next"
              c:identifier="poppler_structure_element_iter_next"
              version="0.26">
        <doc xml:space="preserve">Sets @iter to point to the next structure element at the current level
of the tree, if valid. See poppler_structure_element_iter_new() for more
information.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">%TRUE, if @iter was set to the next structure element</doc>
          <type name="gboolean" c:type="gboolean"/>
        </return-value>
        <parameters>
          <instance-parameter name="iter" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerStructureElementIter</doc>
            <type name="StructureElementIter"
                  c:type="PopplerStructureElementIter*"/>
          </instance-parameter>
        </parameters>
      </method>
    </record>
    <enumeration name="StructureElementKind"
                 glib:type-name="PopplerStructureElementKind"
                 glib:get-type="poppler_structure_element_kind_get_type"
                 c:type="PopplerStructureElementKind">
      <member name="content"
              value="0"
              c:identifier="POPPLER_STRUCTURE_ELEMENT_CONTENT"
              glib:nick="content">
      </member>
      <member name="object_reference"
              value="1"
              c:identifier="POPPLER_STRUCTURE_ELEMENT_OBJECT_REFERENCE"
              glib:nick="object-reference">
      </member>
      <member name="document"
              value="2"
              c:identifier="POPPLER_STRUCTURE_ELEMENT_DOCUMENT"
              glib:nick="document">
      </member>
      <member name="part"
              value="3"
              c:identifier="POPPLER_STRUCTURE_ELEMENT_PART"
              glib:nick="part">
      </member>
      <member name="article"
              value="4"
              c:identifier="POPPLER_STRUCTURE_ELEMENT_ARTICLE"
              glib:nick="article">
      </member>
      <member name="section"
              value="5"
              c:identifier="POPPLER_STRUCTURE_ELEMENT_SECTION"
              glib:nick="section">
      </member>
      <member name="div"
              value="6"
              c:identifier="POPPLER_STRUCTURE_ELEMENT_DIV"
              glib:nick="div">
      </member>
      <member name="span"
              value="7"
              c:identifier="POPPLER_STRUCTURE_ELEMENT_SPAN"
              glib:nick="span">
      </member>
      <member name="quote"
              value="8"
              c:identifier="POPPLER_STRUCTURE_ELEMENT_QUOTE"
              glib:nick="quote">
      </member>
      <member name="note"
              value="9"
              c:identifier="POPPLER_STRUCTURE_ELEMENT_NOTE"
              glib:nick="note">
      </member>
      <member name="reference"
              value="10"
              c:identifier="POPPLER_STRUCTURE_ELEMENT_REFERENCE"
              glib:nick="reference">
      </member>
      <member name="bibentry"
              value="11"
              c:identifier="POPPLER_STRUCTURE_ELEMENT_BIBENTRY"
              glib:nick="bibentry">
      </member>
      <member name="code"
              value="12"
              c:identifier="POPPLER_STRUCTURE_ELEMENT_CODE"
              glib:nick="code">
      </member>
      <member name="link"
              value="13"
              c:identifier="POPPLER_STRUCTURE_ELEMENT_LINK"
              glib:nick="link">
      </member>
      <member name="annot"
              value="14"
              c:identifier="POPPLER_STRUCTURE_ELEMENT_ANNOT"
              glib:nick="annot">
      </member>
      <member name="blockquote"
              value="15"
              c:identifier="POPPLER_STRUCTURE_ELEMENT_BLOCKQUOTE"
              glib:nick="blockquote">
      </member>
      <member name="caption"
              value="16"
              c:identifier="POPPLER_STRUCTURE_ELEMENT_CAPTION"
              glib:nick="caption">
      </member>
      <member name="nonstruct"
              value="17"
              c:identifier="POPPLER_STRUCTURE_ELEMENT_NONSTRUCT"
              glib:nick="nonstruct">
      </member>
      <member name="toc"
              value="18"
              c:identifier="POPPLER_STRUCTURE_ELEMENT_TOC"
              glib:nick="toc">
      </member>
      <member name="toc_item"
              value="19"
              c:identifier="POPPLER_STRUCTURE_ELEMENT_TOC_ITEM"
              glib:nick="toc-item">
      </member>
      <member name="index"
              value="20"
              c:identifier="POPPLER_STRUCTURE_ELEMENT_INDEX"
              glib:nick="index">
      </member>
      <member name="private"
              value="21"
              c:identifier="POPPLER_STRUCTURE_ELEMENT_PRIVATE"
              glib:nick="private">
      </member>
      <member name="paragraph"
              value="22"
              c:identifier="POPPLER_STRUCTURE_ELEMENT_PARAGRAPH"
              glib:nick="paragraph">
      </member>
      <member name="heading"
              value="23"
              c:identifier="POPPLER_STRUCTURE_ELEMENT_HEADING"
              glib:nick="heading">
      </member>
      <member name="heading_1"
              value="24"
              c:identifier="POPPLER_STRUCTURE_ELEMENT_HEADING_1"
              glib:nick="heading-1">
      </member>
      <member name="heading_2"
              value="25"
              c:identifier="POPPLER_STRUCTURE_ELEMENT_HEADING_2"
              glib:nick="heading-2">
      </member>
      <member name="heading_3"
              value="26"
              c:identifier="POPPLER_STRUCTURE_ELEMENT_HEADING_3"
              glib:nick="heading-3">
      </member>
      <member name="heading_4"
              value="27"
              c:identifier="POPPLER_STRUCTURE_ELEMENT_HEADING_4"
              glib:nick="heading-4">
      </member>
      <member name="heading_5"
              value="28"
              c:identifier="POPPLER_STRUCTURE_ELEMENT_HEADING_5"
              glib:nick="heading-5">
      </member>
      <member name="heading_6"
              value="29"
              c:identifier="POPPLER_STRUCTURE_ELEMENT_HEADING_6"
              glib:nick="heading-6">
      </member>
      <member name="list"
              value="30"
              c:identifier="POPPLER_STRUCTURE_ELEMENT_LIST"
              glib:nick="list">
      </member>
      <member name="list_item"
              value="31"
              c:identifier="POPPLER_STRUCTURE_ELEMENT_LIST_ITEM"
              glib:nick="list-item">
      </member>
      <member name="list_label"
              value="32"
              c:identifier="POPPLER_STRUCTURE_ELEMENT_LIST_LABEL"
              glib:nick="list-label">
      </member>
      <member name="list_body"
              value="33"
              c:identifier="POPPLER_STRUCTURE_ELEMENT_LIST_BODY"
              glib:nick="list-body">
      </member>
      <member name="table"
              value="34"
              c:identifier="POPPLER_STRUCTURE_ELEMENT_TABLE"
              glib:nick="table">
      </member>
      <member name="table_row"
              value="35"
              c:identifier="POPPLER_STRUCTURE_ELEMENT_TABLE_ROW"
              glib:nick="table-row">
      </member>
      <member name="table_heading"
              value="36"
              c:identifier="POPPLER_STRUCTURE_ELEMENT_TABLE_HEADING"
              glib:nick="table-heading">
      </member>
      <member name="table_data"
              value="37"
              c:identifier="POPPLER_STRUCTURE_ELEMENT_TABLE_DATA"
              glib:nick="table-data">
      </member>
      <member name="table_header"
              value="38"
              c:identifier="POPPLER_STRUCTURE_ELEMENT_TABLE_HEADER"
              glib:nick="table-header">
      </member>
      <member name="table_footer"
              value="39"
              c:identifier="POPPLER_STRUCTURE_ELEMENT_TABLE_FOOTER"
              glib:nick="table-footer">
      </member>
      <member name="table_body"
              value="40"
              c:identifier="POPPLER_STRUCTURE_ELEMENT_TABLE_BODY"
              glib:nick="table-body">
      </member>
      <member name="ruby"
              value="41"
              c:identifier="POPPLER_STRUCTURE_ELEMENT_RUBY"
              glib:nick="ruby">
      </member>
      <member name="ruby_base_text"
              value="42"
              c:identifier="POPPLER_STRUCTURE_ELEMENT_RUBY_BASE_TEXT"
              glib:nick="ruby-base-text">
      </member>
      <member name="ruby_annot_text"
              value="43"
              c:identifier="POPPLER_STRUCTURE_ELEMENT_RUBY_ANNOT_TEXT"
              glib:nick="ruby-annot-text">
      </member>
      <member name="ruby_punctuation"
              value="44"
              c:identifier="POPPLER_STRUCTURE_ELEMENT_RUBY_PUNCTUATION"
              glib:nick="ruby-punctuation">
      </member>
      <member name="warichu"
              value="45"
              c:identifier="POPPLER_STRUCTURE_ELEMENT_WARICHU"
              glib:nick="warichu">
      </member>
      <member name="warichu_text"
              value="46"
              c:identifier="POPPLER_STRUCTURE_ELEMENT_WARICHU_TEXT"
              glib:nick="warichu-text">
      </member>
      <member name="warichu_punctuation"
              value="47"
              c:identifier="POPPLER_STRUCTURE_ELEMENT_WARICHU_PUNCTUATION"
              glib:nick="warichu-punctuation">
      </member>
      <member name="figure"
              value="48"
              c:identifier="POPPLER_STRUCTURE_ELEMENT_FIGURE"
              glib:nick="figure">
      </member>
      <member name="formula"
              value="49"
              c:identifier="POPPLER_STRUCTURE_ELEMENT_FORMULA"
              glib:nick="formula">
      </member>
      <member name="form"
              value="50"
              c:identifier="POPPLER_STRUCTURE_ELEMENT_FORM"
              glib:nick="form">
      </member>
    </enumeration>
    <enumeration name="StructureFormRole"
                 glib:type-name="PopplerStructureFormRole"
                 glib:get-type="poppler_structure_form_role_get_type"
                 c:type="PopplerStructureFormRole">
      <member name="undefined"
              value="0"
              c:identifier="POPPLER_STRUCTURE_FORM_ROLE_UNDEFINED"
              glib:nick="undefined">
      </member>
      <member name="radio_button"
              value="1"
              c:identifier="POPPLER_STRUCTURE_FORM_ROLE_RADIO_BUTTON"
              glib:nick="radio-button">
      </member>
      <member name="push_button"
              value="2"
              c:identifier="POPPLER_STRUCTURE_FORM_ROLE_PUSH_BUTTON"
              glib:nick="push-button">
      </member>
      <member name="text_value"
              value="3"
              c:identifier="POPPLER_STRUCTURE_FORM_ROLE_TEXT_VALUE"
              glib:nick="text-value">
      </member>
      <member name="checkbox"
              value="4"
              c:identifier="POPPLER_STRUCTURE_FORM_ROLE_CHECKBOX"
              glib:nick="checkbox">
      </member>
    </enumeration>
    <enumeration name="StructureFormState"
                 glib:type-name="PopplerStructureFormState"
                 glib:get-type="poppler_structure_form_state_get_type"
                 c:type="PopplerStructureFormState">
      <member name="on"
              value="0"
              c:identifier="POPPLER_STRUCTURE_FORM_STATE_ON"
              glib:nick="on">
      </member>
      <member name="off"
              value="1"
              c:identifier="POPPLER_STRUCTURE_FORM_STATE_OFF"
              glib:nick="off">
      </member>
      <member name="neutral"
              value="2"
              c:identifier="POPPLER_STRUCTURE_FORM_STATE_NEUTRAL"
              glib:nick="neutral">
      </member>
    </enumeration>
    <bitfield name="StructureGetTextFlags"
              glib:type-name="PopplerStructureGetTextFlags"
              glib:get-type="poppler_structure_get_text_flags_get_type"
              c:type="PopplerStructureGetTextFlags">
      <member name="none"
              value="0"
              c:identifier="POPPLER_STRUCTURE_GET_TEXT_NONE"
              glib:nick="none">
        <doc xml:space="preserve">No flags.</doc>
      </member>
      <member name="recursive"
              value="1"
              c:identifier="POPPLER_STRUCTURE_GET_TEXT_RECURSIVE"
              glib:nick="recursive">
        <doc xml:space="preserve">For non-leaf, non-content
   elements, recursively obtain the text from all the elements
   enclosed in the subtree.</doc>
      </member>
    </bitfield>
    <enumeration name="StructureGlyphOrientation"
                 glib:type-name="PopplerStructureGlyphOrientation"
                 glib:get-type="poppler_structure_glyph_orientation_get_type"
                 c:type="PopplerStructureGlyphOrientation">
      <member name="auto"
              value="0"
              c:identifier="POPPLER_STRUCTURE_GLYPH_ORIENTATION_AUTO"
              glib:nick="auto">
      </member>
      <member name="0"
              value="0"
              c:identifier="POPPLER_STRUCTURE_GLYPH_ORIENTATION_0"
              glib:nick="0">
      </member>
      <member name="90"
              value="1"
              c:identifier="POPPLER_STRUCTURE_GLYPH_ORIENTATION_90"
              glib:nick="90">
      </member>
      <member name="180"
              value="2"
              c:identifier="POPPLER_STRUCTURE_GLYPH_ORIENTATION_180"
              glib:nick="180">
      </member>
      <member name="270"
              value="3"
              c:identifier="POPPLER_STRUCTURE_GLYPH_ORIENTATION_270"
              glib:nick="270">
      </member>
    </enumeration>
    <enumeration name="StructureInlineAlign"
                 glib:type-name="PopplerStructureInlineAlign"
                 glib:get-type="poppler_structure_inline_align_get_type"
                 c:type="PopplerStructureInlineAlign">
      <member name="start"
              value="0"
              c:identifier="POPPLER_STRUCTURE_INLINE_ALIGN_START"
              glib:nick="start">
      </member>
      <member name="center"
              value="1"
              c:identifier="POPPLER_STRUCTURE_INLINE_ALIGN_CENTER"
              glib:nick="center">
      </member>
      <member name="end"
              value="2"
              c:identifier="POPPLER_STRUCTURE_INLINE_ALIGN_END"
              glib:nick="end">
      </member>
    </enumeration>
    <enumeration name="StructureListNumbering"
                 glib:type-name="PopplerStructureListNumbering"
                 glib:get-type="poppler_structure_list_numbering_get_type"
                 c:type="PopplerStructureListNumbering">
      <member name="none"
              value="0"
              c:identifier="POPPLER_STRUCTURE_LIST_NUMBERING_NONE"
              glib:nick="none">
      </member>
      <member name="disc"
              value="1"
              c:identifier="POPPLER_STRUCTURE_LIST_NUMBERING_DISC"
              glib:nick="disc">
      </member>
      <member name="circle"
              value="2"
              c:identifier="POPPLER_STRUCTURE_LIST_NUMBERING_CIRCLE"
              glib:nick="circle">
      </member>
      <member name="square"
              value="3"
              c:identifier="POPPLER_STRUCTURE_LIST_NUMBERING_SQUARE"
              glib:nick="square">
      </member>
      <member name="decimal"
              value="4"
              c:identifier="POPPLER_STRUCTURE_LIST_NUMBERING_DECIMAL"
              glib:nick="decimal">
      </member>
      <member name="upper_roman"
              value="5"
              c:identifier="POPPLER_STRUCTURE_LIST_NUMBERING_UPPER_ROMAN"
              glib:nick="upper-roman">
      </member>
      <member name="lower_roman"
              value="6"
              c:identifier="POPPLER_STRUCTURE_LIST_NUMBERING_LOWER_ROMAN"
              glib:nick="lower-roman">
      </member>
      <member name="upper_alpha"
              value="7"
              c:identifier="POPPLER_STRUCTURE_LIST_NUMBERING_UPPER_ALPHA"
              glib:nick="upper-alpha">
      </member>
      <member name="lower_alpha"
              value="8"
              c:identifier="POPPLER_STRUCTURE_LIST_NUMBERING_LOWER_ALPHA"
              glib:nick="lower-alpha">
      </member>
    </enumeration>
    <enumeration name="StructurePlacement"
                 glib:type-name="PopplerStructurePlacement"
                 glib:get-type="poppler_structure_placement_get_type"
                 c:type="PopplerStructurePlacement">
      <member name="block"
              value="0"
              c:identifier="POPPLER_STRUCTURE_PLACEMENT_BLOCK"
              glib:nick="block">
      </member>
      <member name="inline"
              value="1"
              c:identifier="POPPLER_STRUCTURE_PLACEMENT_INLINE"
              glib:nick="inline">
      </member>
      <member name="before"
              value="2"
              c:identifier="POPPLER_STRUCTURE_PLACEMENT_BEFORE"
              glib:nick="before">
      </member>
      <member name="start"
              value="3"
              c:identifier="POPPLER_STRUCTURE_PLACEMENT_START"
              glib:nick="start">
      </member>
      <member name="end"
              value="4"
              c:identifier="POPPLER_STRUCTURE_PLACEMENT_END"
              glib:nick="end">
      </member>
    </enumeration>
    <enumeration name="StructureRubyAlign"
                 glib:type-name="PopplerStructureRubyAlign"
                 glib:get-type="poppler_structure_ruby_align_get_type"
                 c:type="PopplerStructureRubyAlign">
      <member name="start"
              value="0"
              c:identifier="POPPLER_STRUCTURE_RUBY_ALIGN_START"
              glib:nick="start">
      </member>
      <member name="center"
              value="1"
              c:identifier="POPPLER_STRUCTURE_RUBY_ALIGN_CENTER"
              glib:nick="center">
      </member>
      <member name="end"
              value="2"
              c:identifier="POPPLER_STRUCTURE_RUBY_ALIGN_END"
              glib:nick="end">
      </member>
      <member name="justify"
              value="3"
              c:identifier="POPPLER_STRUCTURE_RUBY_ALIGN_JUSTIFY"
              glib:nick="justify">
      </member>
      <member name="distribute"
              value="4"
              c:identifier="POPPLER_STRUCTURE_RUBY_ALIGN_DISTRIBUTE"
              glib:nick="distribute">
      </member>
    </enumeration>
    <enumeration name="StructureRubyPosition"
                 glib:type-name="PopplerStructureRubyPosition"
                 glib:get-type="poppler_structure_ruby_position_get_type"
                 c:type="PopplerStructureRubyPosition">
      <member name="before"
              value="0"
              c:identifier="POPPLER_STRUCTURE_RUBY_POSITION_BEFORE"
              glib:nick="before">
      </member>
      <member name="after"
              value="1"
              c:identifier="POPPLER_STRUCTURE_RUBY_POSITION_AFTER"
              glib:nick="after">
      </member>
      <member name="warichu"
              value="2"
              c:identifier="POPPLER_STRUCTURE_RUBY_POSITION_WARICHU"
              glib:nick="warichu">
      </member>
      <member name="inline"
              value="3"
              c:identifier="POPPLER_STRUCTURE_RUBY_POSITION_INLINE"
              glib:nick="inline">
      </member>
    </enumeration>
    <enumeration name="StructureTableScope"
                 glib:type-name="PopplerStructureTableScope"
                 glib:get-type="poppler_structure_table_scope_get_type"
                 c:type="PopplerStructureTableScope">
      <member name="row"
              value="0"
              c:identifier="POPPLER_STRUCTURE_TABLE_SCOPE_ROW"
              glib:nick="row">
      </member>
      <member name="column"
              value="1"
              c:identifier="POPPLER_STRUCTURE_TABLE_SCOPE_COLUMN"
              glib:nick="column">
      </member>
      <member name="both"
              value="2"
              c:identifier="POPPLER_STRUCTURE_TABLE_SCOPE_BOTH"
              glib:nick="both">
      </member>
    </enumeration>
    <enumeration name="StructureTextAlign"
                 glib:type-name="PopplerStructureTextAlign"
                 glib:get-type="poppler_structure_text_align_get_type"
                 c:type="PopplerStructureTextAlign">
      <member name="start"
              value="0"
              c:identifier="POPPLER_STRUCTURE_TEXT_ALIGN_START"
              glib:nick="start">
      </member>
      <member name="center"
              value="1"
              c:identifier="POPPLER_STRUCTURE_TEXT_ALIGN_CENTER"
              glib:nick="center">
      </member>
      <member name="end"
              value="2"
              c:identifier="POPPLER_STRUCTURE_TEXT_ALIGN_END"
              glib:nick="end">
      </member>
      <member name="justify"
              value="3"
              c:identifier="POPPLER_STRUCTURE_TEXT_ALIGN_JUSTIFY"
              glib:nick="justify">
      </member>
    </enumeration>
    <enumeration name="StructureTextDecoration"
                 glib:type-name="PopplerStructureTextDecoration"
                 glib:get-type="poppler_structure_text_decoration_get_type"
                 c:type="PopplerStructureTextDecoration">
      <member name="none"
              value="0"
              c:identifier="POPPLER_STRUCTURE_TEXT_DECORATION_NONE"
              glib:nick="none">
      </member>
      <member name="underline"
              value="1"
              c:identifier="POPPLER_STRUCTURE_TEXT_DECORATION_UNDERLINE"
              glib:nick="underline">
      </member>
      <member name="overline"
              value="2"
              c:identifier="POPPLER_STRUCTURE_TEXT_DECORATION_OVERLINE"
              glib:nick="overline">
      </member>
      <member name="linethrough"
              value="3"
              c:identifier="POPPLER_STRUCTURE_TEXT_DECORATION_LINETHROUGH"
              glib:nick="linethrough">
      </member>
    </enumeration>
    <enumeration name="StructureWritingMode"
                 glib:type-name="PopplerStructureWritingMode"
                 glib:get-type="poppler_structure_writing_mode_get_type"
                 c:type="PopplerStructureWritingMode">
      <member name="lr_tb"
              value="0"
              c:identifier="POPPLER_STRUCTURE_WRITING_MODE_LR_TB"
              glib:nick="lr-tb">
      </member>
      <member name="rl_tb"
              value="1"
              c:identifier="POPPLER_STRUCTURE_WRITING_MODE_RL_TB"
              glib:nick="rl-tb">
      </member>
      <member name="tb_rl"
              value="2"
              c:identifier="POPPLER_STRUCTURE_WRITING_MODE_TB_RL"
              glib:nick="tb-rl">
      </member>
    </enumeration>
    <record name="TextAttributes"
            c:type="PopplerTextAttributes"
            version="0.18"
            glib:type-name="PopplerTextAttributes"
            glib:get-type="poppler_text_attributes_get_type"
            c:symbol-prefix="text_attributes">
      <doc xml:space="preserve">A #PopplerTextAttributes is used to describe text attributes of a range of text</doc>
      <field name="font_name" writable="1">
        <doc xml:space="preserve">font name</doc>
        <type name="utf8" c:type="gchar*"/>
      </field>
      <field name="font_size" writable="1">
        <doc xml:space="preserve">font size</doc>
        <type name="gdouble" c:type="gdouble"/>
      </field>
      <field name="is_underlined" writable="1">
        <doc xml:space="preserve">if text is underlined</doc>
        <type name="gboolean" c:type="gboolean"/>
      </field>
      <field name="color" writable="1">
        <doc xml:space="preserve">a #PopplerColor, the foreground color</doc>
        <type name="Color" c:type="PopplerColor"/>
      </field>
      <field name="start_index" writable="1">
        <doc xml:space="preserve">start position this text attributes apply</doc>
        <type name="gint" c:type="gint"/>
      </field>
      <field name="end_index" writable="1">
        <doc xml:space="preserve">end position this text text attributes apply</doc>
        <type name="gint" c:type="gint"/>
      </field>
      <constructor name="new"
                   c:identifier="poppler_text_attributes_new"
                   version="0.18">
        <doc xml:space="preserve">Creates a new #PopplerTextAttributes</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new #PopplerTextAttributes, use poppler_text_attributes_free() to free it</doc>
          <type name="TextAttributes" c:type="PopplerTextAttributes*"/>
        </return-value>
      </constructor>
      <method name="copy"
              c:identifier="poppler_text_attributes_copy"
              version="0.18">
        <doc xml:space="preserve">Creates a copy of @text_attrs</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">a new allocated copy of @text_attrs</doc>
          <type name="TextAttributes" c:type="PopplerTextAttributes*"/>
        </return-value>
        <parameters>
          <instance-parameter name="text_attrs" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerTextAttributes to copy</doc>
            <type name="TextAttributes" c:type="PopplerTextAttributes*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="free"
              c:identifier="poppler_text_attributes_free"
              version="0.18">
        <doc xml:space="preserve">Frees the given #PopplerTextAttributes</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="text_attrs" transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerTextAttributes</doc>
            <type name="TextAttributes" c:type="PopplerTextAttributes*"/>
          </instance-parameter>
        </parameters>
      </method>
    </record>
    <record name="TextSpan"
            c:type="PopplerTextSpan"
            glib:type-name="PopplerTextSpan"
            glib:get-type="poppler_text_span_get_type"
            c:symbol-prefix="text_span">
      <method name="copy" c:identifier="poppler_text_span_copy" version="0.26">
        <doc xml:space="preserve">Makes a copy of a text span.</doc>
        <return-value transfer-ownership="full">
          <doc xml:space="preserve">A new #PopplerTextSpan</doc>
          <type name="TextSpan" c:type="PopplerTextSpan*"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_text_span"
                              transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerTextSpan</doc>
            <type name="TextSpan" c:type="PopplerTextSpan*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="free" c:identifier="poppler_text_span_free" version="0.26">
        <doc xml:space="preserve">Frees a text span.</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_text_span"
                              transfer-ownership="none">
            <doc xml:space="preserve">A #PopplerTextSpan</doc>
            <type name="TextSpan" c:type="PopplerTextSpan*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_color"
              c:identifier="poppler_text_span_get_color"
              version="0.26">
        <doc xml:space="preserve">Obtains the color in which the text is to be rendered.</doc>
        <return-value transfer-ownership="none">
          <type name="none" c:type="void"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_text_span"
                              transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerTextSpan</doc>
            <type name="TextSpan" c:type="PopplerTextSpan*"/>
          </instance-parameter>
          <parameter name="color"
                     direction="out"
                     caller-allocates="1"
                     transfer-ownership="none">
            <doc xml:space="preserve">a return location for a #PopplerColor</doc>
            <type name="Color" c:type="PopplerColor*"/>
          </parameter>
        </parameters>
      </method>
      <method name="get_font_name"
              c:identifier="poppler_text_span_get_font_name"
              version="0.26">
        <doc xml:space="preserve">Obtains the name of the font in which the span is to be rendered.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">A string containing the font name, or
  %NULL if a font is not defined.</doc>
          <type name="utf8" c:type="const gchar*"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_text_span"
                              transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerTextSpan</doc>
            <type name="TextSpan" c:type="PopplerTextSpan*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="get_text"
              c:identifier="poppler_text_span_get_text"
              version="0.26">
        <doc xml:space="preserve">Obtains the text contained in the span.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">A string.</doc>
          <type name="utf8" c:type="const gchar*"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_text_span"
                              transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerTextSpan</doc>
            <type name="TextSpan" c:type="PopplerTextSpan*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="is_bold_font"
              c:identifier="poppler_text_span_is_bold_font"
              version="0.26">
        <doc xml:space="preserve">Check whether a text span is meant to be rendered using a bold font.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">Whether the span uses bold font.</doc>
          <type name="gboolean" c:type="gboolean"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_text_span"
                              transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerTextSpan</doc>
            <type name="TextSpan" c:type="PopplerTextSpan*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="is_fixed_width_font"
              c:identifier="poppler_text_span_is_fixed_width_font"
              version="0.26">
        <doc xml:space="preserve">Check wether a text span is meant to be rendered using a fixed-width font.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">Whether the span uses a fixed-width font.</doc>
          <type name="gboolean" c:type="gboolean"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_text_span"
                              transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerTextSpan</doc>
            <type name="TextSpan" c:type="PopplerTextSpan*"/>
          </instance-parameter>
        </parameters>
      </method>
      <method name="is_serif_font"
              c:identifier="poppler_text_span_is_serif_font"
              version="0.26">
        <doc xml:space="preserve">Check whether a text span is meant to be rendered using a serif font.</doc>
        <return-value transfer-ownership="none">
          <doc xml:space="preserve">Whether the span uses a serif font.</doc>
          <type name="gboolean" c:type="gboolean"/>
        </return-value>
        <parameters>
          <instance-parameter name="poppler_text_span"
                              transfer-ownership="none">
            <doc xml:space="preserve">a #PopplerTextSpan</doc>
            <type name="TextSpan" c:type="PopplerTextSpan*"/>
          </instance-parameter>
        </parameters>
      </method>
    </record>
    <bitfield name="ViewerPreferences"
              glib:type-name="PopplerViewerPreferences"
              glib:get-type="poppler_viewer_preferences_get_type"
              c:type="PopplerViewerPreferences">
      <doc xml:space="preserve">Viewer preferences</doc>
      <member name="unset"
              value="0"
              c:identifier="POPPLER_VIEWER_PREFERENCES_UNSET"
              glib:nick="unset">
        <doc xml:space="preserve">no preferences set</doc>
      </member>
      <member name="hide_toolbar"
              value="1"
              c:identifier="POPPLER_VIEWER_PREFERENCES_HIDE_TOOLBAR"
              glib:nick="hide-toolbar">
        <doc xml:space="preserve">hider toolbars when document is active</doc>
      </member>
      <member name="hide_menubar"
              value="2"
              c:identifier="POPPLER_VIEWER_PREFERENCES_HIDE_MENUBAR"
              glib:nick="hide-menubar">
        <doc xml:space="preserve">hide menu bar when document is active</doc>
      </member>
      <member name="hide_windowui"
              value="4"
              c:identifier="POPPLER_VIEWER_PREFERENCES_HIDE_WINDOWUI"
              glib:nick="hide-windowui">
        <doc xml:space="preserve">hide UI elements in document's window</doc>
      </member>
      <member name="fit_window"
              value="8"
              c:identifier="POPPLER_VIEWER_PREFERENCES_FIT_WINDOW"
              glib:nick="fit-window">
        <doc xml:space="preserve">resize document's window to fit the size of the first displayed page</doc>
      </member>
      <member name="center_window"
              value="16"
              c:identifier="POPPLER_VIEWER_PREFERENCES_CENTER_WINDOW"
              glib:nick="center-window">
        <doc xml:space="preserve">position the document's window in the center of the screen</doc>
      </member>
      <member name="display_doc_title"
              value="32"
              c:identifier="POPPLER_VIEWER_PREFERENCES_DISPLAY_DOC_TITLE"
              glib:nick="display-doc-title">
        <doc xml:space="preserve">display document title in window's title bar</doc>
      </member>
      <member name="direction_rtl"
              value="64"
              c:identifier="POPPLER_VIEWER_PREFERENCES_DIRECTION_RTL"
              glib:nick="direction-rtl">
        <doc xml:space="preserve">the predominant reading order for text is right to left</doc>
      </member>
    </bitfield>
    <function name="date_parse"
              c:identifier="poppler_date_parse"
              version="0.12">
      <doc xml:space="preserve">Parses a PDF format date string and converts it to a #time_t. Returns #FALSE
if the parsing fails or the input string is not a valid PDF format date string</doc>
      <return-value transfer-ownership="none">
        <doc xml:space="preserve">#TRUE, if @timet was set</doc>
        <type name="gboolean" c:type="gboolean"/>
      </return-value>
      <parameters>
        <parameter name="date" transfer-ownership="none">
          <doc xml:space="preserve">string to parse</doc>
          <type name="utf8" c:type="const gchar*"/>
        </parameter>
        <parameter name="timet" transfer-ownership="none">
          <doc xml:space="preserve">an uninitialized #time_t</doc>
          <type name="glong" c:type="time_t*"/>
        </parameter>
      </parameters>
    </function>
    <function name="error_quark"
              c:identifier="poppler_error_quark"
              moved-to="Error.quark">
      <return-value transfer-ownership="none">
        <type name="GLib.Quark" c:type="GQuark"/>
      </return-value>
    </function>
    <function name="get_backend" c:identifier="poppler_get_backend">
      <doc xml:space="preserve">Returns the backend compiled into the poppler library.</doc>
      <return-value transfer-ownership="none">
        <doc xml:space="preserve">The backend used by poppler</doc>
        <type name="Backend" c:type="PopplerBackend"/>
      </return-value>
    </function>
    <function name="get_version" c:identifier="poppler_get_version">
      <doc xml:space="preserve">Returns the version of poppler in use.  This result is not to be freed.</doc>
      <return-value transfer-ownership="none">
        <doc xml:space="preserve">the version of poppler.</doc>
        <type name="utf8" c:type="const char*"/>
      </return-value>
    </function>
  </namespace>
</repository>
