'use client';

import { useState } from 'react';
import { FileUpload } from '@/components/FileUpload';
import { QuizGenerator } from '@/components/QuizGenerator';
import { QuizDisplay } from '@/components/QuizDisplay';
import { ProcessedDocument, Quiz } from '@/types/quiz';

export default function Home() {
  const [processedDocument, setProcessedDocument] = useState<ProcessedDocument | null>(null);
  const [generatedQuiz, setGeneratedQuiz] = useState<Quiz | null>(null);
  const [currentStep, setCurrentStep] = useState<'upload' | 'generate' | 'display'>('upload');

  const handleDocumentProcessed = (document: ProcessedDocument) => {
    setProcessedDocument(document);
    setCurrentStep('generate');
  };

  const handleQuizGenerated = (quiz: Quiz) => {
    setGeneratedQuiz(quiz);
    setCurrentStep('display');
  };

  const handleReset = () => {
    setProcessedDocument(null);
    setGeneratedQuiz(null);
    setCurrentStep('upload');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-8">
        <header className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-2">
            PDF Quiz Generator
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300">
            Upload large PDFs and generate intelligent quizzes using AI
          </p>
          <div className="mt-2">
            <span className="inline-block bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
              v2.2.0 - FIXED: Real PDF Text Extraction with pdfjs-dist
            </span>
          </div>
        </header>

        <div className="max-w-4xl mx-auto">
          {/* Progress Steps */}
          <div className="flex items-center justify-center mb-8">
            <div className="flex items-center space-x-4">
              <div className={`flex items-center ${currentStep === 'upload' ? 'text-blue-600' : currentStep === 'generate' || currentStep === 'display' ? 'text-green-600' : 'text-gray-400'}`}>
                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${currentStep === 'upload' ? 'bg-blue-600 text-white' : currentStep === 'generate' || currentStep === 'display' ? 'bg-green-600 text-white' : 'bg-gray-300'}`}>
                  1
                </div>
                <span className="ml-2 font-medium">Upload PDF</span>
              </div>
              <div className="w-8 h-0.5 bg-gray-300"></div>
              <div className={`flex items-center ${currentStep === 'generate' ? 'text-blue-600' : currentStep === 'display' ? 'text-green-600' : 'text-gray-400'}`}>
                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${currentStep === 'generate' ? 'bg-blue-600 text-white' : currentStep === 'display' ? 'bg-green-600 text-white' : 'bg-gray-300'}`}>
                  2
                </div>
                <span className="ml-2 font-medium">Generate Quiz</span>
              </div>
              <div className="w-8 h-0.5 bg-gray-300"></div>
              <div className={`flex items-center ${currentStep === 'display' ? 'text-blue-600' : 'text-gray-400'}`}>
                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${currentStep === 'display' ? 'bg-blue-600 text-white' : 'bg-gray-300'}`}>
                  3
                </div>
                <span className="ml-2 font-medium">View Quiz</span>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            {currentStep === 'upload' && (
              <FileUpload onDocumentProcessed={handleDocumentProcessed} />
            )}

            {currentStep === 'generate' && processedDocument && (
              <QuizGenerator
                document={processedDocument}
                onQuizGenerated={handleQuizGenerated}
                onBack={() => setCurrentStep('upload')}
              />
            )}

            {currentStep === 'display' && generatedQuiz && (
              <QuizDisplay
                quiz={generatedQuiz}
                onReset={handleReset}
                onBack={() => setCurrentStep('generate')}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
