import OpenAI from 'openai';
import { Question, QuestionType, DifficultyLevel, DocumentChunk } from '@/types/quiz';

const deepseek = new OpenAI({
  apiKey: process.env.DEEPSEEK_API_KEY,
  baseURL: process.env.DEEPSEEK_BASE_URL || 'https://api.deepseek.com',
});

// Note: DeepSeek doesn't provide embedding models, so we'll skip embeddings
// and focus on direct question generation from text chunks
export function generateEmbedding(text: string): number[] {
  // Return a simple hash-based pseudo-embedding for compatibility
  // This is just for maintaining the interface, not for semantic similarity
  const hash = text.split('').reduce((a, b) => {
    a = ((a << 5) - a) + b.charCodeAt(0);
    return a & a;
  }, 0);

  // Generate a simple 384-dimensional vector based on text characteristics
  const embedding = Array.from({ length: 384 }, (_, i) => {
    return Math.sin(hash * (i + 1) * 0.01) * 0.1;
  });

  return embedding;
}

export async function generateQuestions(
  chunks: DocumentChunk[],
  questionTypes: QuestionType[],
  difficulty: DifficultyLevel[],
  questionsPerType: number,
  language?: string
): Promise<Question[]> {
  const questions: Question[] = [];

  // Limit total questions to prevent long generation times
  const maxTotalQuestions = 10;
  const totalRequestedQuestions = questionTypes.length * difficulty.length * questionsPerType;

  if (totalRequestedQuestions > maxTotalQuestions) {
    console.warn(`Requested ${totalRequestedQuestions} questions, limiting to ${maxTotalQuestions} for performance`);
  }

  // Generate questions more efficiently - batch by type
  for (const questionType of questionTypes) {
    for (const diff of difficulty) {
      const adjustedCount = Math.min(questionsPerType, Math.ceil(maxTotalQuestions / (questionTypes.length * difficulty.length)));
      const typeQuestions = await generateQuestionsForType(
        chunks,
        questionType,
        diff,
        adjustedCount,
        language
      );
      questions.push(...typeQuestions);

      // Stop if we've reached the limit
      if (questions.length >= maxTotalQuestions) {
        break;
      }
    }
    if (questions.length >= maxTotalQuestions) {
      break;
    }
  }

  return questions.slice(0, maxTotalQuestions);
}

async function generateQuestionsForType(
  chunks: DocumentChunk[],
  questionType: QuestionType,
  difficulty: DifficultyLevel,
  count: number,
  language?: string
): Promise<Question[]> {
  const selectedChunks = selectRelevantChunks(chunks, count);
  const questions: Question[] = [];
  
  for (let i = 0; i < Math.min(count, selectedChunks.length); i++) {
    const chunk = selectedChunks[i];
    
    try {
      const question = await generateSingleQuestion(chunk, questionType, difficulty, language);
      if (question) {
        questions.push(question);
      }
    } catch (error) {
      console.error(`Error generating question for chunk ${chunk.id}:`, error);
    }
  }
  
  return questions;
}

async function generateSingleQuestion(
  chunk: DocumentChunk,
  questionType: QuestionType,
  difficulty: DifficultyLevel,
  language?: string
): Promise<Question | null> {
  const prompt = createPrompt(chunk.content, questionType, difficulty, language);

  try {
    const systemMessage = language === 'arabic'
      ? 'أنت خبير في إنشاء الاختبارات. قم بإنشاء أسئلة فقط من النص المقدم. مهم: استجب فقط بتنسيق JSON صالح. لا تتضمن كتل كود markdown أو نص توضيحي أو أي تنسيق آخر. أرجع فقط كائن JSON الخام.'
      : 'You are an expert quiz generator. Create questions ONLY from the provided text content. IMPORTANT: Respond ONLY with valid JSON format. Do not include markdown code blocks, explanatory text, or any other formatting. Return only the raw JSON object.';

    const response = await deepseek.chat.completions.create({
      model: 'deepseek-chat',
      messages: [
        {
          role: 'system',
          content: systemMessage
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.3, // Lower temperature for more focused responses
      max_tokens: 800,  // Increased for Arabic text which may need more tokens
    });
    
    const content = response.choices[0]?.message?.content;
    if (!content) return null;

    // Clean the response - remove markdown code blocks if present
    let cleanContent = content.trim();
    if (cleanContent.startsWith('```json')) {
      cleanContent = cleanContent.replace(/^```json\s*/, '').replace(/\s*```$/, '');
    } else if (cleanContent.startsWith('```')) {
      cleanContent = cleanContent.replace(/^```\s*/, '').replace(/\s*```$/, '');
    }

    const questionData = JSON.parse(cleanContent);
    return createQuestionObject(questionData, questionType, difficulty, chunk.content);
  } catch (error) {
    console.error('Error generating question:', error);
    return null;
  }
}

function createPrompt(text: string, questionType: QuestionType, difficulty: DifficultyLevel, language?: string): string {
  // Check if text contains mostly garbage characters
  const readableChars = text.match(/[a-zA-Z\u0600-\u06FF\u0750-\u077F]/g);
  const totalChars = text.replace(/\s/g, '').length;

  if (!readableChars || readableChars.length < totalChars * 0.3) {
    throw new Error('The extracted text appears to be corrupted or unreadable. Please ensure the PDF contains proper text content.');
  }

  const isArabic = language === 'arabic';

  const basePrompt = isArabic
    ? `مهم: قم بإنشاء سؤال فقط بناءً على المعلومات المذكورة صراحة في النص التالي. لا تستخدم أي معرفة خارجية أو معلومات غير موجودة في هذا النص.

النص: "${text.substring(0, 1000)}"

قم بإنشاء سؤال بمستوى صعوبة ${difficulty} من نوع ${questionType} باستخدام المعلومات من النص أعلاه فقط. يجب أن يكون السؤال قابلاً للإجابة من المحتوى المقدم فقط.

`
    : `IMPORTANT: Create a question ONLY based on the information explicitly stated in the following text. Do NOT use any external knowledge or information not present in this text.

Text: "${text.substring(0, 1000)}"

Generate a ${difficulty} difficulty ${questionType} question using ONLY the information from the above text. The question must be answerable solely from the provided content.

`;

  switch (questionType) {
    case 'mcq':
      return basePrompt + `Create a multiple-choice question with 4 options where the correct answer is directly found in the provided text. Respond in JSON format:
{
  "question": "Your question based only on the provided text",
  "options": [
    {"text": "Option A", "isCorrect": false},
    {"text": "Option B", "isCorrect": true},
    {"text": "Option C", "isCorrect": false},
    {"text": "Option D", "isCorrect": false}
  ],
  "explanation": "Brief explanation referencing the provided text"
}`;

    case 'true-false':
      return basePrompt + `Create a true/false statement that can be verified directly from the provided text. Respond in JSON format:
{
  "question": "Your true/false statement based only on the provided text",
  "correctAnswer": true,
  "explanation": "Brief explanation referencing the provided text"
}`;

    case 'fill-blank':
      return basePrompt + `Create a fill-in-the-blank question where the missing word/phrase appears in the provided text. Respond in JSON format:
{
  "question": "Your question with _____ blank (based only on provided text)",
  "correctAnswer": "correct answer from the text",
  "acceptableAnswers": ["alternative answer 1", "alternative answer 2"],
  "explanation": "Brief explanation referencing the provided text"
}`;

    default:
      return basePrompt;
  }
}

function createQuestionObject(
  data: any,
  questionType: QuestionType,
  difficulty: DifficultyLevel,
  sourceChunk: string
): Question {
  const baseQuestion = {
    id: Math.random().toString(36).substring(2, 11),
    difficulty,
    sourceChunk,
    explanation: data.explanation,
  };
  
  switch (questionType) {
    case 'mcq':
      return {
        ...baseQuestion,
        type: 'mcq',
        question: data.question,
        options: data.options.map((opt: any, index: number) => ({
          id: `opt_${index}`,
          text: opt.text,
          isCorrect: opt.isCorrect,
        })),
      };
    
    case 'true-false':
      return {
        ...baseQuestion,
        type: 'true-false',
        question: data.question,
        correctAnswer: data.correctAnswer,
      };
    
    case 'fill-blank':
      return {
        ...baseQuestion,
        type: 'fill-blank',
        question: data.question,
        correctAnswer: data.correctAnswer,
        acceptableAnswers: data.acceptableAnswers || [],
      };
    
    default:
      throw new Error(`Unsupported question type: ${questionType}`);
  }
}

function selectRelevantChunks(chunks: DocumentChunk[], count: number): DocumentChunk[] {
  // Simple selection - in a real app, you might use embedding similarity
  const shuffled = [...chunks].sort(() => Math.random() - 0.5);
  return shuffled.slice(0, count);
}
