import OpenAI from 'openai';
import { Question, QuestionType, DifficultyLevel, DocumentChunk } from '@/types/quiz';

const deepseek = new OpenAI({
  apiKey: process.env.DEEPSEEK_API_KEY,
  baseURL: process.env.DEEPSEEK_BASE_URL || 'https://api.deepseek.com',
});

export async function generateEmbedding(text: string): Promise<number[]> {
  try {
    const response = await deepseek.embeddings.create({
      model: 'deepseek-embed',
      input: text,
    });
    
    return response.data[0].embedding;
  } catch (error) {
    console.error('Error generating embedding:', error);
    throw new Error('Failed to generate embedding');
  }
}

export async function generateQuestions(
  chunks: DocumentChunk[],
  questionTypes: QuestionType[],
  difficulty: DifficultyLevel[],
  questionsPerType: number
): Promise<Question[]> {
  const questions: Question[] = [];
  
  for (const questionType of questionTypes) {
    for (const diff of difficulty) {
      const typeQuestions = await generateQuestionsForType(
        chunks,
        questionType,
        diff,
        questionsPerType
      );
      questions.push(...typeQuestions);
    }
  }
  
  return questions;
}

async function generateQuestionsForType(
  chunks: DocumentChunk[],
  questionType: QuestionType,
  difficulty: DifficultyLevel,
  count: number
): Promise<Question[]> {
  const selectedChunks = selectRelevantChunks(chunks, count);
  const questions: Question[] = [];
  
  for (let i = 0; i < Math.min(count, selectedChunks.length); i++) {
    const chunk = selectedChunks[i];
    
    try {
      const question = await generateSingleQuestion(chunk, questionType, difficulty);
      if (question) {
        questions.push(question);
      }
    } catch (error) {
      console.error(`Error generating question for chunk ${chunk.id}:`, error);
    }
  }
  
  return questions;
}

async function generateSingleQuestion(
  chunk: DocumentChunk,
  questionType: QuestionType,
  difficulty: DifficultyLevel
): Promise<Question | null> {
  const prompt = createPrompt(chunk.content, questionType, difficulty);
  
  try {
    const response = await deepseek.chat.completions.create({
      model: 'deepseek-chat',
      messages: [
        {
          role: 'system',
          content: 'You are an expert quiz generator. Generate high-quality questions based on the provided text. Always respond with valid JSON format.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.7,
      max_tokens: 1000,
    });
    
    const content = response.choices[0]?.message?.content;
    if (!content) return null;
    
    const questionData = JSON.parse(content);
    return createQuestionObject(questionData, questionType, difficulty, chunk.content);
  } catch (error) {
    console.error('Error generating question:', error);
    return null;
  }
}

function createPrompt(text: string, questionType: QuestionType, difficulty: DifficultyLevel): string {
  const basePrompt = `Based on the following text, generate a ${difficulty} difficulty ${questionType} question:\n\n"${text}"\n\n`;
  
  switch (questionType) {
    case 'mcq':
      return basePrompt + `Generate a multiple-choice question with 4 options. Respond in JSON format:
{
  "question": "Your question here",
  "options": [
    {"text": "Option A", "isCorrect": false},
    {"text": "Option B", "isCorrect": true},
    {"text": "Option C", "isCorrect": false},
    {"text": "Option D", "isCorrect": false}
  ],
  "explanation": "Brief explanation of the correct answer"
}`;
    
    case 'true-false':
      return basePrompt + `Generate a true/false question. Respond in JSON format:
{
  "question": "Your true/false statement here",
  "correctAnswer": true,
  "explanation": "Brief explanation of why this is true/false"
}`;
    
    case 'fill-blank':
      return basePrompt + `Generate a fill-in-the-blank question. Respond in JSON format:
{
  "question": "Your question with _____ blank",
  "correctAnswer": "correct answer",
  "acceptableAnswers": ["alternative answer 1", "alternative answer 2"],
  "explanation": "Brief explanation of the answer"
}`;
    
    default:
      return basePrompt;
  }
}

function createQuestionObject(
  data: any,
  questionType: QuestionType,
  difficulty: DifficultyLevel,
  sourceChunk: string
): Question {
  const baseQuestion = {
    id: Math.random().toString(36).substr(2, 9),
    difficulty,
    sourceChunk,
    explanation: data.explanation,
  };
  
  switch (questionType) {
    case 'mcq':
      return {
        ...baseQuestion,
        type: 'mcq',
        question: data.question,
        options: data.options.map((opt: any, index: number) => ({
          id: `opt_${index}`,
          text: opt.text,
          isCorrect: opt.isCorrect,
        })),
      };
    
    case 'true-false':
      return {
        ...baseQuestion,
        type: 'true-false',
        question: data.question,
        correctAnswer: data.correctAnswer,
      };
    
    case 'fill-blank':
      return {
        ...baseQuestion,
        type: 'fill-blank',
        question: data.question,
        correctAnswer: data.correctAnswer,
        acceptableAnswers: data.acceptableAnswers || [],
      };
    
    default:
      throw new Error(`Unsupported question type: ${questionType}`);
  }
}

function selectRelevantChunks(chunks: DocumentChunk[], count: number): DocumentChunk[] {
  // Simple selection - in a real app, you might use embedding similarity
  const shuffled = [...chunks].sort(() => Math.random() - 0.5);
  return shuffled.slice(0, count);
}
