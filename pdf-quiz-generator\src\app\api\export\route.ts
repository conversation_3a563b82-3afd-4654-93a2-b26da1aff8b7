import { NextRequest, NextResponse } from 'next/server';
import { Quiz, Question } from '@/types/quiz';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { quiz, format, includeAnswers = true, includeExplanations = true } = body as {
      quiz: Quiz;
      format: 'json' | 'csv';
      includeAnswers?: boolean;
      includeExplanations?: boolean;
    };
    
    if (!quiz) {
      return NextResponse.json({ error: 'Quiz data is required' }, { status: 400 });
    }
    
    if (!format || !['json', 'csv'].includes(format)) {
      return NextResponse.json({ error: 'Invalid format. Must be json or csv' }, { status: 400 });
    }
    
    if (format === 'json') {
      // Export as JSON
      const exportData = {
        ...quiz,
        questions: quiz.questions.map(question => {
          const baseQuestion = {
            id: question.id,
            type: question.type,
            question: question.question,
            difficulty: question.difficulty,
            sourceChunk: question.sourceChunk,
          };
          
          if (includeExplanations && question.explanation) {
            (baseQuestion as any).explanation = question.explanation;
          }
          
          if (includeAnswers) {
            switch (question.type) {
              case 'mcq':
                return {
                  ...baseQuestion,
                  options: question.options,
                };
              case 'true-false':
                return {
                  ...baseQuestion,
                  correctAnswer: question.correctAnswer,
                };
              case 'fill-blank':
                return {
                  ...baseQuestion,
                  correctAnswer: question.correctAnswer,
                  acceptableAnswers: question.acceptableAnswers,
                };
            }
          } else {
            // Remove answers for practice mode
            switch (question.type) {
              case 'mcq':
                return {
                  ...baseQuestion,
                  options: question.options.map(opt => ({
                    id: opt.id,
                    text: opt.text,
                    // Don't include isCorrect
                  })),
                };
              case 'true-false':
                return baseQuestion; // Don't include correctAnswer
              case 'fill-blank':
                return baseQuestion; // Don't include correctAnswer and acceptableAnswers
            }
          }
          
          return baseQuestion;
        }),
      };
      
      return NextResponse.json({
        success: true,
        data: exportData,
        filename: `${quiz.title.replace(/[^a-zA-Z0-9]/g, '_')}.json`,
      });
      
    } else {
      // Export as CSV
      const csvData = quiz.questions.map((question, index) => {
        const baseRow = {
          questionNumber: index + 1,
          type: question.type,
          question: question.question,
          difficulty: question.difficulty,
        };
        
        if (includeAnswers) {
          (baseRow as any).correctAnswer = getCorrectAnswerText(question);
        }
        
        if (includeExplanations && question.explanation) {
          (baseRow as any).explanation = question.explanation;
        }
        
        // Add type-specific fields
        if (question.type === 'mcq' && includeAnswers) {
          question.options.forEach((option, optIndex) => {
            (baseRow as any)[`option${optIndex + 1}`] = option.text;
            if (includeAnswers) {
              (baseRow as any)[`option${optIndex + 1}_correct`] = option.isCorrect;
            }
          });
        }
        
        return baseRow;
      });
      
      // Convert to CSV string
      if (csvData.length === 0) {
        return NextResponse.json({ error: 'No data to export' }, { status: 400 });
      }
      
      const headers = Object.keys(csvData[0]);
      const csvContent = [
        headers.join(','),
        ...csvData.map(row => 
          headers.map(header => {
            const value = (row as any)[header];
            if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
              return `"${value.replace(/"/g, '""')}"`;
            }
            return value || '';
          }).join(',')
        )
      ].join('\n');
      
      return NextResponse.json({
        success: true,
        data: csvContent,
        filename: `${quiz.title.replace(/[^a-zA-Z0-9]/g, '_')}.csv`,
      });
    }
    
  } catch (error) {
    console.error('Export error:', error);
    return NextResponse.json(
      { error: 'Failed to export quiz data' },
      { status: 500 }
    );
  }
}

function getCorrectAnswerText(question: Question): string {
  switch (question.type) {
    case 'mcq':
      return question.options.find(opt => opt.isCorrect)?.text || '';
    case 'true-false':
      return question.correctAnswer.toString();
    case 'fill-blank':
      return question.correctAnswer;
    default:
      return '';
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Quiz export endpoint',
    supportedFormats: ['json', 'csv'],
    options: {
      includeAnswers: 'boolean - include correct answers in export',
      includeExplanations: 'boolean - include explanations in export',
    },
  });
}
