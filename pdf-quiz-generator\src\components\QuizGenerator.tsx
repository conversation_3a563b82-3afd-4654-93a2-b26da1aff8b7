'use client';

import { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Brain } from 'lucide-react';
import { ProcessedDocument, Quiz, QuestionType, DifficultyLevel, QuizGenerationOptions } from '@/types/quiz';

interface QuizGeneratorProps {
  document: ProcessedDocument;
  onQuizGenerated: (quiz: Quiz) => void;
  onBack: () => void;
}

export function QuizGenerator({ document, onQuizGenerated, onBack }: QuizGeneratorProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [options, setOptions] = useState<QuizGenerationOptions>({
    questionTypes: ['mcq'],
    difficulty: ['medium'],
    questionsPerType: 5,
    includeExplanations: true,
  });

  const handleGenerate = async () => {
    setIsGenerating(true);
    setError(null);

    try {
      const response = await fetch('/api/generate-quiz', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          chunks: document.chunks,
          options,
          documentName: document.filename,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate quiz');
      }

      const result = await response.json();
      
      if (result.success && result.quiz) {
        onQuizGenerated(result.quiz);
      } else {
        throw new Error('Invalid response from server');
      }

    } catch (err) {
      console.error('Quiz generation error:', err);
      setError(err instanceof Error ? err.message : 'Failed to generate quiz');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleQuestionTypeChange = (type: QuestionType, checked: boolean) => {
    setOptions(prev => ({
      ...prev,
      questionTypes: checked 
        ? [...prev.questionTypes, type]
        : prev.questionTypes.filter(t => t !== type)
    }));
  };

  const handleDifficultyChange = (difficulty: DifficultyLevel, checked: boolean) => {
    setOptions(prev => ({
      ...prev,
      difficulty: checked 
        ? [...prev.difficulty, difficulty]
        : prev.difficulty.filter(d => d !== difficulty)
    }));
  };

  return (
    <div className="w-full max-w-4xl mx-auto">
      <div className="flex items-center mb-6">
        <button
          onClick={onBack}
          className="flex items-center space-x-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
        >
          <ArrowLeft className="h-5 w-5" />
          <span>Back to Upload</span>
        </button>
      </div>

      <div className="mb-6">
        <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-2">
          Configure Quiz Generation
        </h2>
        <p className="text-gray-600 dark:text-gray-300">
          Customize your quiz settings and generate intelligent questions from your PDF document.
        </p>
      </div>

      {/* Document Info */}
      <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 mb-6">
        <div className="flex items-center space-x-3">
          <BookOpen className="h-6 w-6 text-blue-600" />
          <div>
            <h3 className="font-medium text-blue-900 dark:text-blue-100">{document.filename}</h3>
            <p className="text-sm text-blue-700 dark:text-blue-300">
              {document.chunks.length} text chunks • {document.totalPages} pages
            </p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Question Types */}
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center space-x-2 mb-4">
            <Settings className="h-5 w-5 text-gray-600 dark:text-gray-300" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">Question Types</h3>
          </div>
          
          <div className="space-y-3">
            <label className="flex items-center space-x-3">
              <input
                type="checkbox"
                checked={options.questionTypes.includes('mcq')}
                onChange={(e) => handleQuestionTypeChange('mcq', e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <div>
                <span className="font-medium text-gray-900 dark:text-white">Multiple Choice</span>
                <p className="text-sm text-gray-500 dark:text-gray-400">4-option multiple choice questions</p>
              </div>
            </label>
            
            <label className="flex items-center space-x-3">
              <input
                type="checkbox"
                checked={options.questionTypes.includes('true-false')}
                onChange={(e) => handleQuestionTypeChange('true-false', e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <div>
                <span className="font-medium text-gray-900 dark:text-white">True/False</span>
                <p className="text-sm text-gray-500 dark:text-gray-400">Binary true or false questions</p>
              </div>
            </label>
            
            <label className="flex items-center space-x-3">
              <input
                type="checkbox"
                checked={options.questionTypes.includes('fill-blank')}
                onChange={(e) => handleQuestionTypeChange('fill-blank', e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <div>
                <span className="font-medium text-gray-900 dark:text-white">Fill in the Blank</span>
                <p className="text-sm text-gray-500 dark:text-gray-400">Complete the missing word or phrase</p>
              </div>
            </label>
          </div>
        </div>

        {/* Difficulty & Settings */}
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center space-x-2 mb-4">
            <Brain className="h-5 w-5 text-gray-600 dark:text-gray-300" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">Difficulty & Settings</h3>
          </div>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Difficulty Levels
              </label>
              <div className="space-y-2">
                {(['easy', 'medium', 'hard'] as DifficultyLevel[]).map((level) => (
                  <label key={level} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={options.difficulty.includes(level)}
                      onChange={(e) => handleDifficultyChange(level, e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="capitalize text-gray-900 dark:text-white">{level}</span>
                  </label>
                ))}
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Questions per Type: {options.questionsPerType}
              </label>
              <input
                type="range"
                min="1"
                max="10"
                value={options.questionsPerType}
                onChange={(e) => setOptions(prev => ({ ...prev, questionsPerType: parseInt(e.target.value) }))}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
              />
              <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
                <span>1</span>
                <span>10</span>
              </div>
            </div>
            
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={options.includeExplanations}
                onChange={(e) => setOptions(prev => ({ ...prev, includeExplanations: e.target.checked }))}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-gray-900 dark:text-white">Include explanations</span>
            </label>
          </div>
        </div>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
          <p className="text-sm text-red-800 dark:text-red-200">{error}</p>
        </div>
      )}

      <div className="flex justify-center">
        <button
          onClick={handleGenerate}
          disabled={isGenerating || options.questionTypes.length === 0 || options.difficulty.length === 0}
          className="flex items-center space-x-2 px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {isGenerating ? (
            <>
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
              <span>Generating Quiz...</span>
            </>
          ) : (
            <>
              <Zap className="h-5 w-5" />
              <span>Generate Quiz</span>
            </>
          )}
        </button>
      </div>
    </div>
  );
}
