{"version": 3, "sources": ["../../../src/shared/lib/server-inserted-metadata.shared-runtime.ts"], "sourcesContent": ["'use client'\n\nimport type React from 'react'\nimport { createContext } from 'react'\n\nexport type MetadataResolver = () => React.ReactNode\ntype MetadataResolverSetter = (m: MetadataResolver) => void\n\nexport const ServerInsertedMetadataContext =\n  createContext<MetadataResolverSetter | null>(null)\n"], "names": ["createContext", "ServerInsertedMetadataContext"], "mappings": "AAAA;AAGA,SAASA,aAAa,QAAQ,QAAO;AAKrC,OAAO,MAAMC,gCACXD,cAA6C,MAAK"}