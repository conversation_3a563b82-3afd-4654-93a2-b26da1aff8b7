{"version": 3, "sources": ["../../../src/server/use-cache/use-cache-errors.ts"], "sourcesContent": ["const USE_CACHE_TIMEOUT_ERROR_CODE = 'USE_CACHE_TIMEOUT'\n\nexport class UseCacheTimeoutError extends Error {\n  digest: typeof USE_CACHE_TIMEOUT_ERROR_CODE = USE_CACHE_TIMEOUT_ERROR_CODE\n\n  constructor() {\n    super(\n      'Filling a cache during prerender timed out, likely because request-specific arguments such as params, searchParams, cookies() or dynamic data were used inside \"use cache\".'\n    )\n  }\n}\n\nexport function isUseCacheTimeoutError(\n  err: unknown\n): err is UseCacheTimeoutError {\n  if (\n    typeof err !== 'object' ||\n    err === null ||\n    !('digest' in err) ||\n    typeof err.digest !== 'string'\n  ) {\n    return false\n  }\n\n  return err.digest === USE_CACHE_TIMEOUT_ERROR_CODE\n}\n"], "names": ["UseCacheTimeoutError", "isUseCacheTimeoutError", "USE_CACHE_TIMEOUT_ERROR_CODE", "Error", "constructor", "digest", "err"], "mappings": ";;;;;;;;;;;;;;;IAEaA,oBAAoB;eAApBA;;IAUGC,sBAAsB;eAAtBA;;;AAZhB,MAAMC,+BAA+B;AAE9B,MAAMF,6BAA6BG;IAGxCC,aAAc;QACZ,KAAK,CACH,qLAJJC,SAA8CH;IAM9C;AACF;AAEO,SAASD,uBACdK,GAAY;IAEZ,IACE,OAAOA,QAAQ,YACfA,QAAQ,QACR,CAAE,CAAA,YAAYA,GAAE,KAChB,OAAOA,IAAID,MAAM,KAAK,UACtB;QACA,OAAO;IACT;IAEA,OAAOC,IAAID,MAAM,KAAKH;AACxB"}