%!PS-Adobe-3.0 Resource-CMap
%%DocumentNeededResources: ProcSet (CIDInit)
%%DocumentNeededResources: CMap (UniKS-UTF32-H)
%%IncludeResource: ProcSet (CIDInit)
%%IncludeResource: CMap (UniKS-UTF32-H)
%%BeginResource: CMap (UniKS-UTF32-V)
%%Title: (UniKS-UTF32-V Adobe Korea1 1)
%%Version: 1.004
%%Copyright: -----------------------------------------------------------
%%Copyright: Copyright 1990-2015 Adobe Systems Incorporated.
%%Copyright: All rights reserved.
%%Copyright:
%%Copyright: Redistribution and use in source and binary forms, with or
%%Copyright: without modification, are permitted provided that the
%%Copyright: following conditions are met:
%%Copyright:
%%Copyright: Redistributions of source code must retain the above
%%Copyright: copyright notice, this list of conditions and the following
%%Copyright: disclaimer.
%%Copyright:
%%Copyright: Redistributions in binary form must reproduce the above
%%Copyright: copyright notice, this list of conditions and the following
%%Copyright: disclaimer in the documentation and/or other materials
%%Copyright: provided with the distribution. 
%%Copyright:
%%Copyright: Neither the name of Adobe Systems Incorporated nor the names
%%Copyright: of its contributors may be used to endorse or promote
%%Copyright: products derived from this software without specific prior
%%Copyright: written permission. 
%%Copyright:
%%Copyright: THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND
%%Copyright: CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES,
%%Copyright: INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
%%Copyright: MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
%%Copyright: DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR
%%Copyright: CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
%%Copyright: SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT
%%Copyright: NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
%%Copyright: LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
%%Copyright: HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
%%Copyright: CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
%%Copyright: OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
%%Copyright: SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
%%Copyright: -----------------------------------------------------------
%%EndComments

/CIDInit /ProcSet findresource begin

12 dict begin

begincmap

/UniKS-UTF32-H usecmap

/CIDSystemInfo 3 dict dup begin
  /Registry (Adobe) def
  /Ordering (Korea1) def
  /Supplement 1 def
end def

/CMapName /UniKS-UTF32-V def
/CMapVersion 1.004 def
/CMapType 1 def

/XUID [1 10 25543] def

/WMode 1 def

11 begincidchar
<00002016> 8061
<00002025> 8058
<00003013> 8075
<0000ff01> 8076
<0000ff0c> 8079
<0000ff0e> 8080
<0000ff3b> 8087
<0000ff3d> 8088
<0000ff3f> 8089
<0000ff5e> 8062
<0000ffe3> 8093
endcidchar

7 begincidrange
<00002013> <00002014> 8059
<00003001> <00003002> 8056
<00003008> <00003011> 8065
<00003014> <00003015> 8063
<0000ff08> <0000ff09> 8077
<0000ff1a> <0000ff1f> 8081
<0000ff5b> <0000ff5d> 8090
endcidrange

endcmap
CMapName currentdict /CMap defineresource pop
end
end

%%EndResource
%%EOF
