/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M12 14v4", key: "1thi36" }],
  [
    "path",
    {
      d: "M14.172 2a2 2 0 0 1 1.414.586l3.828 3.828A2 2 0 0 1 20 7.828V20a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2z",
      key: "1o66bk"
    }
  ],
  ["path", { d: "M8 14h8", key: "1fgep2" }],
  ["rect", { x: "8", y: "10", width: "8", height: "8", rx: "1", key: "1aonk6" }]
];
const CardSim = createLucideIcon("card-sim", __iconNode);

export { __iconNode, CardSim as default };
//# sourceMappingURL=card-sim.js.map
