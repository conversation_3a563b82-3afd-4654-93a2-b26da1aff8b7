import { NextRequest, NextResponse } from 'next/server';
import { chunkText, generateId } from '@/lib/utils';
import { DocumentChunk } from '@/types/quiz';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { text } = body as { text: string };
    
    if (!text) {
      return NextResponse.json({ error: 'Text is required' }, { status: 400 });
    }
    
    // Test chunking
    const textChunks = chunkText(text, 1000, 100);
    
    // Create document chunks
    const chunks: DocumentChunk[] = textChunks.map((chunk, index) => ({
      id: generateId(),
      content: chunk,
      pageNumber: Math.floor(index / 3) + 1,
      metadata: {
        chunkIndex: index,
        wordCount: chunk.split(' ').length,
        charCount: chunk.length,
      },
    }));
    
    return NextResponse.json({
      success: true,
      originalLength: text.length,
      totalChunks: chunks.length,
      chunks: chunks.map(chunk => ({
        id: chunk.id,
        preview: chunk.content.substring(0, 100) + '...',
        wordCount: chunk.metadata?.wordCount,
        charCount: chunk.metadata?.charCount,
      })),
      fullChunks: chunks, // Include full chunks for testing
    });
    
  } catch (error) {
    console.error('Chunking test error:', error);
    return NextResponse.json(
      { error: 'Failed to test chunking' },
      { status: 500 }
    );
  }
}

export async function GET() {
  const sampleText = `
    Artificial Intelligence (AI) is a branch of computer science that aims to create intelligent machines that work and react like humans. Some of the activities computers with artificial intelligence are designed for include speech recognition, learning, planning, and problem solving.

    Machine Learning is a subset of AI that provides systems the ability to automatically learn and improve from experience without being explicitly programmed. Machine learning focuses on the development of computer programs that can access data and use it to learn for themselves.

    Deep Learning is a subset of machine learning in artificial intelligence that has networks capable of learning unsupervised from data that is unstructured or unlabeled. Also known as deep neural learning or deep neural network, it is a computational approach that mimics the workings of the human brain in processing data and creating patterns for use in decision making.

    Natural Language Processing (NLP) is a branch of artificial intelligence that helps computers understand, interpret and manipulate human language. NLP draws from many disciplines, including computer science and computational linguistics, in its pursuit to help computers understand human language.

    Computer Vision is a field of artificial intelligence that trains computers to interpret and understand the visual world. Using digital images from cameras and videos and deep learning models, machines can accurately identify and classify objects and then react to what they see.
  `.trim();
  
  // Test with sample text
  const textChunks = chunkText(sampleText, 500, 50);
  
  return NextResponse.json({
    message: 'Chunking test endpoint',
    sampleText: sampleText.substring(0, 200) + '...',
    sampleChunks: textChunks.length,
    chunkPreviews: textChunks.map((chunk, index) => ({
      index,
      preview: chunk.substring(0, 100) + '...',
      length: chunk.length,
    })),
  });
}
