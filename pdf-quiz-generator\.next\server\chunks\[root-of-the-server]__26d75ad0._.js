module.exports = {

"[project]/.next-internal/server/app/api/upload/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/fs/promises [external] (fs/promises, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs/promises", () => require("fs/promises"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[project]/src/lib/utils.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "chunkText": (()=>chunkText),
    "cn": (()=>cn),
    "downloadAsCSV": (()=>downloadAsCSV),
    "downloadAsJSON": (()=>downloadAsJSON),
    "formatFileSize": (()=>formatFileSize),
    "generateId": (()=>generateId),
    "sanitizeText": (()=>sanitizeText),
    "validatePDF": (()=>validatePDF)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/clsx/dist/clsx.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tailwind-merge/dist/bundle-mjs.mjs [app-route] (ecmascript)");
;
;
function cn(...inputs) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["twMerge"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["clsx"])(inputs));
}
function generateId() {
    return Math.random().toString(36).substr(2, 9);
}
function chunkText(text, maxChunkSize = 1000, overlap = 100) {
    const chunks = [];
    const sentences = text.split(/[.!?]+/).filter((s)=>s.trim().length > 0);
    let currentChunk = '';
    let currentSize = 0;
    for (const sentence of sentences){
        const sentenceSize = sentence.trim().length;
        if (currentSize + sentenceSize > maxChunkSize && currentChunk.length > 0) {
            chunks.push(currentChunk.trim());
            // Create overlap by keeping the last few sentences
            const overlapSentences = currentChunk.split(/[.!?]+/).slice(-2).join('. ');
            currentChunk = overlapSentences + '. ' + sentence.trim();
            currentSize = currentChunk.length;
        } else {
            currentChunk += (currentChunk ? '. ' : '') + sentence.trim();
            currentSize = currentChunk.length;
        }
    }
    if (currentChunk.trim().length > 0) {
        chunks.push(currentChunk.trim());
    }
    return chunks.filter((chunk)=>chunk.length > 50) // Filter out very short chunks
    ;
}
function sanitizeText(text) {
    return text.replace(/\s+/g, ' ').replace(/[^\w\s.,!?;:()\-"']/g, '').trim();
}
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = [
        'Bytes',
        'KB',
        'MB',
        'GB'
    ];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
function validatePDF(file) {
    if (!file) {
        return {
            isValid: false,
            error: 'No file provided'
        };
    }
    if (file.type !== 'application/pdf') {
        return {
            isValid: false,
            error: 'File must be a PDF'
        };
    }
    // 50MB limit
    if (file.size > 50 * 1024 * 1024) {
        return {
            isValid: false,
            error: 'File size must be less than 50MB'
        };
    }
    return {
        isValid: true
    };
}
function downloadAsJSON(data, filename) {
    const blob = new Blob([
        JSON.stringify(data, null, 2)
    ], {
        type: 'application/json'
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}
function downloadAsCSV(data, filename) {
    if (data.length === 0) return;
    const headers = Object.keys(data[0]);
    const csvContent = [
        headers.join(','),
        ...data.map((row)=>headers.map((header)=>{
                const value = row[header];
                if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
                    return `"${value.replace(/"/g, '""')}"`;
                }
                return value;
            }).join(','))
    ].join('\n');
    const blob = new Blob([
        csvContent
    ], {
        type: 'text/csv'
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}
}}),
"[project]/src/lib/pdf-parser.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// PDF parsing utility with proper text extraction and language detection
__turbopack_context__.s({
    "extractTextFromPDF": (()=>extractTextFromPDF),
    "validatePDFBuffer": (()=>validatePDFBuffer)
});
async function extractTextFromPDF(buffer) {
    try {
        // Check if it's a valid PDF by looking for PDF signature
        if (!validatePDFBuffer(buffer)) {
            throw new Error('Invalid PDF file format');
        }
        console.log('Starting PDF text extraction...');
        // Convert buffer to string for processing
        const bufferString = buffer.toString('latin1'); // Use latin1 to preserve all bytes
        let extractedText = '';
        // Method 1: Extract text from PDF text objects (Tj commands)
        const tjMatches = bufferString.match(/\([^)]*\)\s*Tj/g);
        if (tjMatches) {
            console.log(`Found ${tjMatches.length} text objects`);
            for (const match of tjMatches){
                const text = match.replace(/^\(/, '').replace(/\)\s*Tj$/, '');
                // Decode PDF text (handle escape sequences)
                const decodedText = text.replace(/\\n/g, '\n').replace(/\\r/g, '\r').replace(/\\t/g, '\t').replace(/\\b/g, '\b').replace(/\\f/g, '\f').replace(/\\\(/g, '(').replace(/\\\)/g, ')').replace(/\\\\/g, '\\');
                if (decodedText.trim().length > 0) {
                    extractedText += decodedText + ' ';
                }
            }
        }
        // Method 2: Extract text from TJ array commands
        const tjArrayMatches = bufferString.match(/\[[^\]]*\]\s*TJ/g);
        if (tjArrayMatches) {
            console.log(`Found ${tjArrayMatches.length} text arrays`);
            for (const match of tjArrayMatches){
                const arrayContent = match.replace(/^\[/, '').replace(/\]\s*TJ$/, '');
                const textParts = arrayContent.match(/\([^)]*\)/g);
                if (textParts) {
                    for (const part of textParts){
                        const text = part.replace(/^\(/, '').replace(/\)$/, '');
                        if (text.trim().length > 0) {
                            extractedText += text + ' ';
                        }
                    }
                }
            }
        }
        // Method 3: Look for stream content with text
        const streamMatches = bufferString.match(/stream\s*([\s\S]*?)\s*endstream/g);
        if (streamMatches && extractedText.length < 100) {
            console.log(`Found ${streamMatches.length} streams, checking for text content`);
            for (const stream of streamMatches){
                const content = stream.replace(/^stream\s*/, '').replace(/\s*endstream$/, '');
                // Look for readable text in streams
                const readableChars = content.match(/[\u0020-\u007E\u00A0-\u00FF\u0600-\u06FF\u0750-\u077F]+/g);
                if (readableChars) {
                    for (const chars of readableChars){
                        if (chars.length > 3 && !chars.match(/^[0-9\s]+$/)) {
                            extractedText += chars + ' ';
                        }
                    }
                }
            }
        }
        // Clean up extracted text
        extractedText = extractedText.replace(/\s+/g, ' ').replace(/[^\u0020-\u007E\u00A0-\u00FF\u0600-\u06FF\u0750-\u077F\s]/g, '') // Keep ASCII, Latin-1, and Arabic
        .trim();
        console.log(`Extracted text length: ${extractedText.length}`);
        console.log(`First 200 chars: ${extractedText.substring(0, 200)}`);
        // Detect language
        const language = detectLanguage(extractedText);
        console.log(`Detected language: ${language}`);
        // If still no meaningful text, throw error
        if (extractedText.length < 20) {
            throw new Error('Could not extract readable text from this PDF. The file might be image-based, encrypted, or corrupted.');
        }
        // Estimate page count
        const estimatedPages = Math.max(1, Math.ceil(extractedText.length / 2000));
        return {
            text: extractedText,
            numpages: estimatedPages,
            language: language
        };
    } catch (error) {
        console.error('PDF parsing error:', error);
        throw new Error(`Unable to extract text from PDF: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
// Simple language detection function
function detectLanguage(text) {
    if (!text || text.length < 10) return 'unknown';
    // Count Arabic characters
    const arabicChars = (text.match(/[\u0600-\u06FF\u0750-\u077F]/g) || []).length;
    const totalChars = text.replace(/\s/g, '').length;
    if (arabicChars > totalChars * 0.3) {
        return 'arabic';
    }
    // Count Latin characters
    const latinChars = (text.match(/[a-zA-Z]/g) || []).length;
    if (latinChars > totalChars * 0.5) {
        return 'english';
    }
    return 'unknown';
}
function validatePDFBuffer(buffer) {
    // Check if buffer starts with PDF signature
    const pdfSignature = Buffer.from([
        0x25,
        0x50,
        0x44,
        0x46
    ]); // %PDF
    return buffer.length > 4 && buffer.subarray(0, 4).equals(pdfSignature);
}
}}),
"[project]/src/app/api/upload/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs$2f$promises__$5b$external$5d$__$28$fs$2f$promises$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs/promises [external] (fs/promises, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs [external] (fs, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pdf$2d$parser$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/pdf-parser.ts [app-route] (ecmascript)");
;
;
;
;
;
;
async function POST(request) {
    try {
        const formData = await request.formData();
        const file = formData.get('file');
        if (!file) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'No file uploaded'
            }, {
                status: 400
            });
        }
        if (file.type !== 'application/pdf') {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'File must be a PDF'
            }, {
                status: 400
            });
        }
        // Convert file to buffer
        const bytes = await file.arrayBuffer();
        const buffer = Buffer.from(bytes);
        // Validate PDF buffer
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pdf$2d$parser$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["validatePDFBuffer"])(buffer)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Invalid PDF file format'
            }, {
                status: 400
            });
        }
        // Create uploads directory if it doesn't exist
        const uploadsDir = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["join"])(process.cwd(), 'uploads');
        if (!(0, __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["existsSync"])(uploadsDir)) {
            await (0, __TURBOPACK__imported__module__$5b$externals$5d2f$fs$2f$promises__$5b$external$5d$__$28$fs$2f$promises$2c$__cjs$29$__["mkdir"])(uploadsDir, {
                recursive: true
            });
        }
        // Save file temporarily
        const filename = `${(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateId"])()}_${file.name}`;
        const filepath = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["join"])(uploadsDir, filename);
        await (0, __TURBOPACK__imported__module__$5b$externals$5d2f$fs$2f$promises__$5b$external$5d$__$28$fs$2f$promises$2c$__cjs$29$__["writeFile"])(filepath, buffer);
        try {
            // Extract text from PDF using our custom parser
            const pdfData = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pdf$2d$parser$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["extractTextFromPDF"])(buffer);
            const rawText = pdfData.text;
            if (!rawText || rawText.trim().length === 0) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'No text found in PDF'
                }, {
                    status: 400
                });
            }
            // Clean and sanitize text
            const cleanText = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["sanitizeText"])(rawText);
            // Chunk the text
            const textChunks = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["chunkText"])(cleanText, 1000, 100);
            // Create document chunks
            const chunks = textChunks.map((chunk, index)=>({
                    id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateId"])(),
                    content: chunk,
                    pageNumber: Math.floor(index / 3) + 1,
                    metadata: {
                        chunkIndex: index,
                        wordCount: chunk.split(' ').length
                    }
                }));
            // Create processed document
            const processedDoc = {
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateId"])(),
                filename: file.name,
                chunks,
                totalPages: pdfData.numpages,
                processedAt: new Date(),
                metadata: {
                    language: pdfData.language || 'unknown',
                    originalTextLength: cleanText.length
                }
            };
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: true,
                document: processedDoc,
                stats: {
                    totalChunks: chunks.length,
                    totalPages: pdfData.numpages,
                    totalWords: cleanText.split(' ').length,
                    fileSize: file.size
                }
            });
        } catch (parseError) {
            console.error('PDF parsing error:', parseError);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Failed to parse PDF. The file might be corrupted or password-protected.'
            }, {
                status: 400
            });
        }
    } catch (error) {
        console.error('Upload error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Internal server error during file processing'
        }, {
            status: 500
        });
    }
}
async function GET() {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
        message: 'PDF upload endpoint'
    });
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__26d75ad0._.js.map