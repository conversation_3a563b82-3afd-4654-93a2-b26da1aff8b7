export type QuestionType = 'mcq' | 'true-false' | 'fill-blank';
export type DifficultyLevel = 'easy' | 'medium' | 'hard';

export interface MCQOption {
  id: string;
  text: string;
  isCorrect: boolean;
}

export interface MCQQuestion {
  type: 'mcq';
  id: string;
  question: string;
  options: MCQOption[];
  explanation?: string;
  difficulty: DifficultyLevel;
  sourceChunk: string;
}

export interface TrueFalseQuestion {
  type: 'true-false';
  id: string;
  question: string;
  correctAnswer: boolean;
  explanation?: string;
  difficulty: DifficultyLevel;
  sourceChunk: string;
}

export interface FillBlankQuestion {
  type: 'fill-blank';
  id: string;
  question: string;
  correctAnswer: string;
  acceptableAnswers?: string[];
  explanation?: string;
  difficulty: DifficultyLevel;
  sourceChunk: string;
}

export type Question = MCQQuestion | TrueFalseQuestion | FillBlankQuestion;

export interface Quiz {
  id: string;
  title: string;
  description?: string;
  questions: Question[];
  createdAt: Date;
  sourceDocument: string;
}

export interface DocumentChunk {
  id: string;
  content: string;
  pageNumber?: number;
  embedding?: number[];
  metadata?: Record<string, any>;
}

export interface ProcessedDocument {
  id: string;
  filename: string;
  chunks: DocumentChunk[];
  totalPages?: number;
  processedAt: Date;
  metadata?: {
    language?: string;
    originalTextLength?: number;
    [key: string]: any;
  };
}

export interface QuizGenerationOptions {
  questionTypes: QuestionType[];
  difficulty: DifficultyLevel[];
  questionsPerType: number;
  includeExplanations: boolean;
}

export interface ExportFormat {
  format: 'json' | 'csv';
  includeAnswers: boolean;
  includeExplanations: boolean;
}
