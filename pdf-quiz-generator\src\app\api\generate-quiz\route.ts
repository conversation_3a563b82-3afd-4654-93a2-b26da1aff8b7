import { NextRequest, NextResponse } from 'next/server';
import { generateQuestions, generateEmbedding } from '@/lib/deepseek';
import { DocumentChunk, QuizGenerationOptions, Quiz } from '@/types/quiz';
import { generateId } from '@/lib/utils';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { chunks, options, documentName, language } = body as {
      chunks: DocumentChunk[];
      options: QuizGenerationOptions;
      documentName: string;
      language?: string;
    };
    
    if (!chunks || chunks.length === 0) {
      return NextResponse.json({ error: 'No document chunks provided' }, { status: 400 });
    }
    
    if (!options) {
      return NextResponse.json({ error: 'Quiz generation options not provided' }, { status: 400 });
    }
    
    // Validate options
    if (!options.questionTypes || options.questionTypes.length === 0) {
      return NextResponse.json({ error: 'At least one question type must be selected' }, { status: 400 });
    }
    
    if (!options.difficulty || options.difficulty.length === 0) {
      return NextResponse.json({ error: 'At least one difficulty level must be selected' }, { status: 400 });
    }
    
    if (options.questionsPerType < 1 || options.questionsPerType > 10) {
      return NextResponse.json({ error: 'Questions per type must be between 1 and 10' }, { status: 400 });
    }
    
    try {
      // Generate simple embeddings for chunks if not already present
      // Note: DeepSeek doesn't provide embedding models, so we use a simple approach
      const chunksWithEmbeddings = chunks.map((chunk) => {
        if (!chunk.embedding) {
          try {
            const embedding = generateEmbedding(chunk.content);
            return { ...chunk, embedding };
          } catch (embeddingError) {
            console.warn(`Failed to generate embedding for chunk ${chunk.id}:`, embeddingError);
            return chunk; // Return chunk without embedding
          }
        }
        return chunk;
      });
      
      // Generate questions
      const questions = await generateQuestions(
        chunksWithEmbeddings,
        options.questionTypes,
        options.difficulty,
        options.questionsPerType,
        language
      );
      
      if (questions.length === 0) {
        return NextResponse.json(
          { error: 'Failed to generate any questions. Please try with different options or check your document content.' },
          { status: 400 }
        );
      }
      
      // Create quiz object
      const quiz: Quiz = {
        id: generateId(),
        title: `Quiz: ${documentName}`,
        description: `Generated quiz with ${questions.length} questions from ${documentName}`,
        questions,
        createdAt: new Date(),
        sourceDocument: documentName,
      };
      
      return NextResponse.json({
        success: true,
        quiz,
        stats: {
          totalQuestions: questions.length,
          questionTypes: options.questionTypes,
          difficulties: options.difficulty,
          questionsPerType: options.questionsPerType,
        },
      });
      
    } catch (generationError) {
      console.error('Quiz generation error:', generationError);
      
      // Check if it's an API key issue
      if (generationError instanceof Error && generationError.message.includes('API key')) {
        return NextResponse.json(
          { error: 'DeepSeek API key is not configured or invalid. Please check your environment variables.' },
          { status: 401 }
        );
      }
      
      return NextResponse.json(
        { error: 'Failed to generate quiz. Please try again or contact support if the issue persists.' },
        { status: 500 }
      );
    }
    
  } catch (error) {
    console.error('Request processing error:', error);
    return NextResponse.json(
      { error: 'Invalid request format or internal server error' },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({ 
    message: 'Quiz generation endpoint',
    supportedQuestionTypes: ['mcq', 'true-false', 'fill-blank'],
    supportedDifficulties: ['easy', 'medium', 'hard'],
  });
}
