{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///Z:/projectx/pdf-quiz-generator/node_modules/pdf-parse/index.js"], "sourcesContent": ["const Fs = require('fs');\nconst Pdf = require('./lib/pdf-parse.js');\n\nmodule.exports = Pdf;\n\nlet isDebugMode = !module.parent; \n\n//process.env.AUTO_KENT_DEBUG\n\n\n//for testing purpose\nif (isDebugMode) {\n\n    let PDF_FILE = './test/data/05-versions-space.pdf';\n    let dataBuffer = Fs.readFileSync(PDF_FILE);\n    Pdf(dataBuffer).then(function(data) {\n        Fs.writeFileSync(`${PDF_FILE}.txt`, data.text, {\n            encoding: 'utf8',\n            flag: 'w'\n        });\n        debugger;\n    }).catch(function(err) {\n        debugger;\n    });\n\n}\n"], "names": [], "mappings": "AAAA,MAAM;AACN,MAAM;AAEN,OAAO,OAAO,GAAG;AAEjB,IAAI,cAAc,CAAC,OAAO,MAAM;AAEhC,6BAA6B;AAG7B,qBAAqB;AACrB,IAAI,aAAa;IAEb,IAAI,WAAW;IACf,IAAI,aAAa,GAAG,YAAY,CAAC;IACjC,IAAI,YAAY,IAAI,CAAC,SAAS,IAAI;QAC9B,GAAG,aAAa,CAAC,GAAG,SAAS,IAAI,CAAC,EAAE,KAAK,IAAI,EAAE;YAC3C,UAAU;YACV,MAAM;QACV;QACA,QAAS;IACb,GAAG,KAAK,CAAC,SAAS,GAAG;QACjB,QAAS;IACb;AAEJ", "ignoreList": [0], "debugId": null}}]}