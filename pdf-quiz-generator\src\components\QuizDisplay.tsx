'use client';

import { useState } from 'react';
import { ArrowLeft, Download, RotateCcw, CheckCircle, XCircle, HelpCircle } from 'lucide-react';
import { Quiz, Question, MCQQuestion, TrueFalseQuestion, FillBlankQuestion } from '@/types/quiz';
import { downloadAsJSON, downloadAsCSV } from '@/lib/utils';

interface QuizDisplayProps {
  quiz: Quiz;
  onReset: () => void;
  onBack: () => void;
}

interface UserAnswer {
  questionId: string;
  answer: string | boolean;
  isCorrect?: boolean;
}

export function QuizDisplay({ quiz, onReset, onBack }: QuizDisplayProps) {
  const [userAnswers, setUserAnswers] = useState<UserAnswer[]>([]);
  const [showResults, setShowResults] = useState(false);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);

  const handleAnswer = (questionId: string, answer: string | boolean) => {
    setUserAnswers(prev => {
      const existing = prev.find(a => a.questionId === questionId);
      if (existing) {
        return prev.map(a => a.questionId === questionId ? { ...a, answer } : a);
      }
      return [...prev, { questionId, answer }];
    });
  };

  const checkAnswers = () => {
    const checkedAnswers = userAnswers.map(userAnswer => {
      const question = quiz.questions.find(q => q.id === userAnswer.questionId);
      if (!question) return userAnswer;

      let isCorrect = false;
      
      switch (question.type) {
        case 'mcq':
          const mcqQuestion = question as MCQQuestion;
          const correctOption = mcqQuestion.options.find(opt => opt.isCorrect);
          isCorrect = correctOption?.id === userAnswer.answer;
          break;
        case 'true-false':
          const tfQuestion = question as TrueFalseQuestion;
          isCorrect = tfQuestion.correctAnswer === userAnswer.answer;
          break;
        case 'fill-blank':
          const fbQuestion = question as FillBlankQuestion;
          const userAnswerStr = (userAnswer.answer as string).toLowerCase().trim();
          isCorrect = fbQuestion.correctAnswer.toLowerCase() === userAnswerStr ||
                     (fbQuestion.acceptableAnswers || []).some(acc => acc.toLowerCase() === userAnswerStr);
          break;
      }

      return { ...userAnswer, isCorrect };
    });

    setUserAnswers(checkedAnswers);
    setShowResults(true);
  };

  const exportQuiz = (format: 'json' | 'csv') => {
    if (format === 'json') {
      downloadAsJSON(quiz, `${quiz.title.replace(/[^a-zA-Z0-9]/g, '_')}.json`);
    } else {
      const csvData = quiz.questions.map(q => ({
        type: q.type,
        question: q.question,
        difficulty: q.difficulty,
        correctAnswer: getCorrectAnswerText(q),
        explanation: q.explanation || '',
      }));
      downloadAsCSV(csvData, `${quiz.title.replace(/[^a-zA-Z0-9]/g, '_')}.csv`);
    }
  };

  const getCorrectAnswerText = (question: Question): string => {
    switch (question.type) {
      case 'mcq':
        const mcqQ = question as MCQQuestion;
        return mcqQ.options.find(opt => opt.isCorrect)?.text || '';
      case 'true-false':
        const tfQ = question as TrueFalseQuestion;
        return tfQ.correctAnswer.toString();
      case 'fill-blank':
        const fbQ = question as FillBlankQuestion;
        return fbQ.correctAnswer;
      default:
        return '';
    }
  };

  const getUserAnswer = (questionId: string) => {
    return userAnswers.find(a => a.questionId === questionId);
  };

  const getScore = () => {
    const correct = userAnswers.filter(a => a.isCorrect).length;
    const total = quiz.questions.length;
    return { correct, total, percentage: Math.round((correct / total) * 100) };
  };

  const renderQuestion = (question: Question, index: number) => {
    const userAnswer = getUserAnswer(question.id);
    
    return (
      <div key={question.id} className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 mb-6">
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-2">
              <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded text-sm font-medium">
                Question {index + 1}
              </span>
              <span className="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-2 py-1 rounded text-sm">
                {question.type.replace('-', ' ').toUpperCase()}
              </span>
              <span className={`px-2 py-1 rounded text-sm font-medium ${
                question.difficulty === 'easy' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                question.difficulty === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
              }`}>
                {question.difficulty}
              </span>
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              {question.question}
            </h3>
          </div>
          {showResults && userAnswer && (
            <div className="ml-4">
              {userAnswer.isCorrect ? (
                <CheckCircle className="h-6 w-6 text-green-600" />
              ) : (
                <XCircle className="h-6 w-6 text-red-600" />
              )}
            </div>
          )}
        </div>

        {question.type === 'mcq' && (
          <div className="space-y-3">
            {(question as MCQQuestion).options.map((option) => (
              <label key={option.id} className={`flex items-center space-x-3 p-3 rounded-lg border cursor-pointer transition-colors ${
                showResults 
                  ? option.isCorrect 
                    ? 'border-green-500 bg-green-50 dark:bg-green-900/20'
                    : userAnswer?.answer === option.id && !option.isCorrect
                    ? 'border-red-500 bg-red-50 dark:bg-red-900/20'
                    : 'border-gray-200 dark:border-gray-600'
                  : 'border-gray-200 dark:border-gray-600 hover:border-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900/20'
              }`}>
                <input
                  type="radio"
                  name={`question-${question.id}`}
                  value={option.id}
                  checked={userAnswer?.answer === option.id}
                  onChange={() => !showResults && handleAnswer(question.id, option.id)}
                  disabled={showResults}
                  className="text-blue-600 focus:ring-blue-500"
                />
                <span className="text-gray-900 dark:text-white">{option.text}</span>
              </label>
            ))}
          </div>
        )}

        {question.type === 'true-false' && (
          <div className="space-y-3">
            {[true, false].map((value) => (
              <label key={value.toString()} className={`flex items-center space-x-3 p-3 rounded-lg border cursor-pointer transition-colors ${
                showResults 
                  ? (question as TrueFalseQuestion).correctAnswer === value
                    ? 'border-green-500 bg-green-50 dark:bg-green-900/20'
                    : userAnswer?.answer === value && (question as TrueFalseQuestion).correctAnswer !== value
                    ? 'border-red-500 bg-red-50 dark:bg-red-900/20'
                    : 'border-gray-200 dark:border-gray-600'
                  : 'border-gray-200 dark:border-gray-600 hover:border-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900/20'
              }`}>
                <input
                  type="radio"
                  name={`question-${question.id}`}
                  value={value.toString()}
                  checked={userAnswer?.answer === value}
                  onChange={() => !showResults && handleAnswer(question.id, value)}
                  disabled={showResults}
                  className="text-blue-600 focus:ring-blue-500"
                />
                <span className="text-gray-900 dark:text-white">{value ? 'True' : 'False'}</span>
              </label>
            ))}
          </div>
        )}

        {question.type === 'fill-blank' && (
          <div>
            <input
              type="text"
              value={userAnswer?.answer as string || ''}
              onChange={(e) => !showResults && handleAnswer(question.id, e.target.value)}
              disabled={showResults}
              placeholder="Enter your answer..."
              className={`w-full p-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                showResults 
                  ? userAnswer?.isCorrect
                    ? 'border-green-500 bg-green-50 dark:bg-green-900/20'
                    : 'border-red-500 bg-red-50 dark:bg-red-900/20'
                  : 'border-gray-300 dark:border-gray-600'
              } dark:bg-gray-700 dark:text-white`}
            />
            {showResults && (
              <p className="mt-2 text-sm text-gray-600 dark:text-gray-300">
                Correct answer: <span className="font-medium">{(question as FillBlankQuestion).correctAnswer}</span>
              </p>
            )}
          </div>
        )}

        {showResults && question.explanation && (
          <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
            <div className="flex items-start space-x-2">
              <HelpCircle className="h-5 w-5 text-blue-600 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-blue-900 dark:text-blue-100">Explanation</p>
                <p className="text-sm text-blue-800 dark:text-blue-200">{question.explanation}</p>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  };

  const score = showResults ? getScore() : null;

  return (
    <div className="w-full max-w-4xl mx-auto">
      <div className="flex items-center justify-between mb-6">
        <button
          onClick={onBack}
          className="flex items-center space-x-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
        >
          <ArrowLeft className="h-5 w-5" />
          <span>Back to Generator</span>
        </button>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={() => exportQuiz('json')}
            className="flex items-center space-x-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
          >
            <Download className="h-4 w-4" />
            <span>JSON</span>
          </button>
          <button
            onClick={() => exportQuiz('csv')}
            className="flex items-center space-x-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
          >
            <Download className="h-4 w-4" />
            <span>CSV</span>
          </button>
          <button
            onClick={onReset}
            className="flex items-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            <RotateCcw className="h-4 w-4" />
            <span>New Quiz</span>
          </button>
        </div>
      </div>

      <div className="mb-6">
        <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-2">
          {quiz.title}
        </h2>
        <p className="text-gray-600 dark:text-gray-300">
          {quiz.description}
        </p>
        {showResults && score && (
          <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-lg font-semibold text-blue-900 dark:text-blue-100">
                  Score: {score.correct}/{score.total} ({score.percentage}%)
                </p>
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  {score.percentage >= 80 ? 'Excellent work!' : 
                   score.percentage >= 60 ? 'Good job!' : 
                   'Keep studying!'}
                </p>
              </div>
            </div>
          </div>
        )}
      </div>

      <div className="space-y-6">
        {quiz.questions.map((question, index) => renderQuestion(question, index))}
      </div>

      {!showResults && (
        <div className="flex justify-center mt-8">
          <button
            onClick={checkAnswers}
            disabled={userAnswers.length !== quiz.questions.length}
            className="px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            Submit Quiz
          </button>
        </div>
      )}
    </div>
  );
}
