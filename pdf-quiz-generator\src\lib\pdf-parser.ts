// PDF parsing utility using pdf-parse with proper error handling

export async function extractTextFromPDF(buffer: Buffer): Promise<{ text: string; numpages: number; language?: string }> {
  try {
    // Check if it's a valid PDF by looking for PDF signature
    if (!validatePDFBuffer(buffer)) {
      throw new Error('Invalid PDF file format');
    }

    console.log('Starting PDF text extraction with pdf-parse...');

    // Import pdf-parse dynamically to avoid module loading issues
    let pdfParse: any;
    try {
      const pdfParseModule = await import('pdf-parse');
      pdfParse = pdfParseModule.default || pdfParseModule;
    } catch (importError) {
      console.error('Failed to import pdf-parse:', importError);
      throw new Error('PDF parsing library is not available');
    }

    // Parse the PDF
    const pdfData = await pdfParse(buffer, {
      // Options to improve text extraction
      max: 0, // Parse all pages
      version: 'v1.10.100', // Use specific version
    });

    let extractedText = pdfData.text || '';
    const numPages = pdfData.numpages || 1;

    console.log(`PDF parsed successfully. Pages: ${numPages}`);
    console.log(`Raw extracted text length: ${extractedText.length}`);

    // Clean up the extracted text
    extractedText = extractedText
      .replace(/\r\n/g, '\n') // Normalize line endings
      .replace(/\r/g, '\n')   // Handle old Mac line endings
      .replace(/\n+/g, '\n')  // Remove multiple consecutive newlines
      .replace(/\s+/g, ' ')   // Normalize whitespace
      .trim();

    console.log(`Cleaned text length: ${extractedText.length}`);
    console.log(`First 200 chars: ${extractedText.substring(0, 200)}`);

    // Check if we got meaningful text
    if (extractedText.length < 20) {
      throw new Error('Could not extract readable text from this PDF. The file might be image-based, encrypted, or corrupted.');
    }

    // Detect language
    const language = detectLanguage(extractedText);
    console.log(`Detected language: ${language}`);

    return {
      text: extractedText,
      numpages: numPages,
      language: language,
    };

  } catch (error) {
    console.error('PDF parsing error:', error);

    // Provide specific error messages
    if (error instanceof Error) {
      if (error.message.includes('Invalid PDF')) {
        throw new Error('The uploaded file is not a valid PDF or is corrupted.');
      }
      if (error.message.includes('encrypted')) {
        throw new Error('This PDF is password-protected. Please upload an unprotected PDF.');
      }
      if (error.message.includes('ENOENT')) {
        throw new Error('PDF parsing library error. Please try again.');
      }
    }

    throw new Error(`Unable to extract text from PDF: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Simple language detection function
function detectLanguage(text: string): string {
  if (!text || text.length < 10) return 'unknown';

  // Count Arabic characters
  const arabicChars = (text.match(/[\u0600-\u06FF\u0750-\u077F]/g) || []).length;
  const totalChars = text.replace(/\s/g, '').length;

  if (arabicChars > totalChars * 0.3) {
    return 'arabic';
  }

  // Count Latin characters
  const latinChars = (text.match(/[a-zA-Z]/g) || []).length;
  if (latinChars > totalChars * 0.5) {
    return 'english';
  }

  return 'unknown';
}

export function validatePDFBuffer(buffer: Buffer): boolean {
  // Check if buffer starts with PDF signature
  const pdfSignature = Buffer.from([0x25, 0x50, 0x44, 0x46]); // %PDF
  return buffer.length > 4 && buffer.subarray(0, 4).equals(pdfSignature);
}
