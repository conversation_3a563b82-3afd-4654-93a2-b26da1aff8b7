// PDF parsing utility with proper text extraction and language detection

export async function extractTextFromPDF(buffer: Buffer): Promise<{ text: string; numpages: number; language?: string }> {
  try {
    // Check if it's a valid PDF by looking for PDF signature
    if (!validatePDFBuffer(buffer)) {
      throw new Error('Invalid PDF file format');
    }

    console.log('Starting PDF text extraction...');

    // Convert buffer to string for processing
    const bufferString = buffer.toString('latin1'); // Use latin1 to preserve all bytes
    let extractedText = '';

    // Method 1: Extract text from PDF text objects (Tj commands)
    const tjMatches = bufferString.match(/\([^)]*\)\s*Tj/g);
    if (tjMatches) {
      console.log(`Found ${tjMatches.length} text objects`);
      for (const match of tjMatches) {
        const text = match.replace(/^\(/, '').replace(/\)\s*Tj$/, '');
        // Decode PDF text (handle escape sequences)
        const decodedText = text
          .replace(/\\n/g, '\n')
          .replace(/\\r/g, '\r')
          .replace(/\\t/g, '\t')
          .replace(/\\b/g, '\b')
          .replace(/\\f/g, '\f')
          .replace(/\\\(/g, '(')
          .replace(/\\\)/g, ')')
          .replace(/\\\\/g, '\\');

        if (decodedText.trim().length > 0) {
          extractedText += decodedText + ' ';
        }
      }
    }

    // Method 2: Extract text from TJ array commands
    const tjArrayMatches = bufferString.match(/\[[^\]]*\]\s*TJ/g);
    if (tjArrayMatches) {
      console.log(`Found ${tjArrayMatches.length} text arrays`);
      for (const match of tjArrayMatches) {
        const arrayContent = match.replace(/^\[/, '').replace(/\]\s*TJ$/, '');
        const textParts = arrayContent.match(/\([^)]*\)/g);
        if (textParts) {
          for (const part of textParts) {
            const text = part.replace(/^\(/, '').replace(/\)$/, '');
            if (text.trim().length > 0) {
              extractedText += text + ' ';
            }
          }
        }
      }
    }

    // Method 3: Look for stream content with text
    const streamMatches = bufferString.match(/stream\s*([\s\S]*?)\s*endstream/g);
    if (streamMatches && extractedText.length < 100) {
      console.log(`Found ${streamMatches.length} streams, checking for text content`);
      for (const stream of streamMatches) {
        const content = stream.replace(/^stream\s*/, '').replace(/\s*endstream$/, '');
        // Look for readable text in streams
        const readableChars = content.match(/[\u0020-\u007E\u00A0-\u00FF\u0600-\u06FF\u0750-\u077F]+/g);
        if (readableChars) {
          for (const chars of readableChars) {
            if (chars.length > 3 && !chars.match(/^[0-9\s]+$/)) {
              extractedText += chars + ' ';
            }
          }
        }
      }
    }

    // Clean up extracted text
    extractedText = extractedText
      .replace(/\s+/g, ' ')
      .replace(/[^\u0020-\u007E\u00A0-\u00FF\u0600-\u06FF\u0750-\u077F\s]/g, '') // Keep ASCII, Latin-1, and Arabic
      .trim();

    console.log(`Extracted text length: ${extractedText.length}`);
    console.log(`First 200 chars: ${extractedText.substring(0, 200)}`);

    // Detect language
    const language = detectLanguage(extractedText);
    console.log(`Detected language: ${language}`);

    // If still no meaningful text, throw error
    if (extractedText.length < 20) {
      throw new Error('Could not extract readable text from this PDF. The file might be image-based, encrypted, or corrupted.');
    }

    // Estimate page count
    const estimatedPages = Math.max(1, Math.ceil(extractedText.length / 2000));

    return {
      text: extractedText,
      numpages: estimatedPages,
      language: language,
    };

  } catch (error) {
    console.error('PDF parsing error:', error);
    throw new Error(`Unable to extract text from PDF: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Simple language detection function
function detectLanguage(text: string): string {
  if (!text || text.length < 10) return 'unknown';

  // Count Arabic characters
  const arabicChars = (text.match(/[\u0600-\u06FF\u0750-\u077F]/g) || []).length;
  const totalChars = text.replace(/\s/g, '').length;

  if (arabicChars > totalChars * 0.3) {
    return 'arabic';
  }

  // Count Latin characters
  const latinChars = (text.match(/[a-zA-Z]/g) || []).length;
  if (latinChars > totalChars * 0.5) {
    return 'english';
  }

  return 'unknown';
}

export function validatePDFBuffer(buffer: Buffer): boolean {
  // Check if buffer starts with PDF signature
  const pdfSignature = Buffer.from([0x25, 0x50, 0x44, 0x46]); // %PDF
  return buffer.length > 4 && buffer.subarray(0, 4).equals(pdfSignature);
}
