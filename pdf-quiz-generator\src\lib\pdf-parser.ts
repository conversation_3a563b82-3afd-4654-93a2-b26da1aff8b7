// PDF parsing utility using pdfjs-dist for proper text extraction

export async function extractTextFromPDF(buffer: Buffer): Promise<{ text: string; numpages: number; language?: string }> {
  try {
    // Check if it's a valid PDF by looking for PDF signature
    if (!validatePDFBuffer(buffer)) {
      throw new Error('Invalid PDF file format');
    }

    console.log('Starting PDF text extraction with pdfjs-dist...');

    // Import pdfjs-dist dynamically
    const pdfjsLib = await import('pdfjs-dist');

    // Convert buffer to Uint8Array
    const pdfData = new Uint8Array(buffer);

    // Load the PDF document
    const loadingTask = pdfjsLib.getDocument({
      data: pdfData,
      verbosity: 0, // Reduce console output
    });

    const pdfDocument = await loadingTask.promise;
    const numPages = pdfDocument.numPages;

    console.log(`PDF loaded successfully. Pages: ${numPages}`);

    let fullText = '';

    // Extract text from each page
    for (let pageNum = 1; pageNum <= numPages; pageNum++) {
      try {
        const page = await pdfDocument.getPage(pageNum);
        const textContent = await page.getTextContent();

        // Combine all text items from the page
        const pageText = textContent.items
          .map((item: any) => item.str)
          .join(' ')
          .trim();

        if (pageText.length > 0) {
          fullText += pageText + '\n';
        }

        console.log(`Page ${pageNum}: extracted ${pageText.length} characters`);
      } catch (pageError) {
        console.warn(`Error extracting text from page ${pageNum}:`, pageError);
        // Continue with other pages
      }
    }

    // Clean up the extracted text
    fullText = fullText
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim();

    console.log(`Total extracted text length: ${fullText.length}`);
    console.log(`First 200 chars: ${fullText.substring(0, 200)}`);

    // Detect language
    const language = detectLanguage(fullText);
    console.log(`Detected language: ${language}`);

    // Check if we got meaningful text
    if (fullText.length < 20) {
      throw new Error('Could not extract readable text from this PDF. The file might be image-based, encrypted, or corrupted.');
    }

    return {
      text: fullText,
      numpages: numPages,
      language: language,
    };

  } catch (error) {
    console.error('PDF parsing error:', error);

    // If pdfjs-dist fails, provide a helpful error message
    if (error instanceof Error && error.message.includes('Invalid PDF')) {
      throw new Error('The uploaded file is not a valid PDF or is corrupted.');
    }

    throw new Error(`Unable to extract text from PDF: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Simple language detection function
function detectLanguage(text: string): string {
  if (!text || text.length < 10) return 'unknown';

  // Count Arabic characters
  const arabicChars = (text.match(/[\u0600-\u06FF\u0750-\u077F]/g) || []).length;
  const totalChars = text.replace(/\s/g, '').length;

  if (arabicChars > totalChars * 0.3) {
    return 'arabic';
  }

  // Count Latin characters
  const latinChars = (text.match(/[a-zA-Z]/g) || []).length;
  if (latinChars > totalChars * 0.5) {
    return 'english';
  }

  return 'unknown';
}

export function validatePDFBuffer(buffer: Buffer): boolean {
  // Check if buffer starts with PDF signature
  const pdfSignature = Buffer.from([0x25, 0x50, 0x44, 0x46]); // %PDF
  return buffer.length > 4 && buffer.subarray(0, 4).equals(pdfSignature);
}
