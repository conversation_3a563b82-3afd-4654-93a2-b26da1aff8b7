// PDF parsing utility with better error handling

export async function extractTextFromPDF(buffer: Buffer): Promise<{ text: string; numpages: number }> {
  try {
    // Try to dynamically import pdf-parse
    const pdfParse = await import('pdf-parse');
    const parseFunction = pdfParse.default || pdfParse;
    
    const result = await parseFunction(buffer);
    
    return {
      text: result.text || '',
      numpages: result.numpages || 1,
    };
  } catch (error) {
    console.error('PDF parsing error:', error);
    
    // If pdf-parse fails, try alternative approach
    try {
      // Fallback: try to extract text using a different method
      const textContent = buffer.toString('utf8');
      
      // Simple heuristic to check if we got readable text
      if (textContent.length > 100 && /[a-zA-Z]/.test(textContent)) {
        return {
          text: textContent,
          numpages: Math.ceil(textContent.length / 3000), // Rough estimate
        };
      }
    } catch (fallbackError) {
      console.error('Fallback parsing error:', fallbackError);
    }
    
    throw new Error('Unable to extract text from PDF. The file might be image-based, corrupted, or password-protected.');
  }
}

export function validatePDFBuffer(buffer: Buffer): boolean {
  // Check if buffer starts with PDF signature
  const pdfSignature = Buffer.from([0x25, 0x50, 0x44, 0x46]); // %PDF
  return buffer.length > 4 && buffer.subarray(0, 4).equals(pdfSignature);
}
