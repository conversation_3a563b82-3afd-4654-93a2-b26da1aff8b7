// PDF parsing utility with better error handling

export async function extractTextFromPDF(buffer: Buffer): Promise<{ text: string; numpages: number }> {
  try {
    // Check if it's a valid PDF by looking for PDF signature
    if (!validatePDFBuffer(buffer)) {
      throw new Error('Invalid PDF file format');
    }

    // Try to extract actual text from PDF buffer
    // This is a simple approach that works for text-based PDFs
    const bufferString = buffer.toString('binary');

    // Look for text content in PDF structure
    let extractedText = '';

    // Simple PDF text extraction - look for text between stream objects
    const textMatches = bufferString.match(/BT\s+[\s\S]*?ET/g);
    if (textMatches) {
      for (const match of textMatches) {
        // Extract text from PDF text objects
        const textContent = match.replace(/BT\s+/, '').replace(/\s+ET$/, '');
        // Remove PDF operators and extract readable text
        const cleanText = textContent
          .replace(/\/\w+\s+\d+\s+Tf/g, '') // Remove font commands
          .replace(/\d+\s+\d+\s+Td/g, '') // Remove positioning commands
          .replace(/\([^)]*\)\s*Tj/g, (match) => {
            // Extract text from Tj commands
            return match.replace(/^\(/, '').replace(/\)\s*Tj$/, '') + ' ';
          })
          .replace(/\s+/g, ' ')
          .trim();

        if (cleanText.length > 10) {
          extractedText += cleanText + '\n';
        }
      }
    }

    // If no text found with the above method, try alternative extraction
    if (extractedText.length < 100) {
      // Look for readable text patterns in the buffer
      const readableText = bufferString
        .replace(/[^\x20-\x7E\n\r\t]/g, ' ') // Keep only printable ASCII
        .replace(/\s+/g, ' ')
        .trim();

      // Extract sentences that look like real content
      const sentences = readableText.split(/[.!?]+/).filter(sentence => {
        const clean = sentence.trim();
        return clean.length > 20 &&
               clean.split(' ').length > 3 &&
               /[a-zA-Z]/.test(clean) &&
               !clean.includes('obj') &&
               !clean.includes('endobj') &&
               !clean.includes('stream');
      });

      if (sentences.length > 0) {
        extractedText = sentences.join('. ').trim() + '.';
      }
    }

    // Final fallback - if still no meaningful text
    if (extractedText.length < 50) {
      throw new Error('Could not extract readable text from this PDF. The file might be image-based, encrypted, or corrupted.');
    }

    // Estimate page count based on content length
    const estimatedPages = Math.max(1, Math.ceil(extractedText.length / 2000));

    return {
      text: extractedText,
      numpages: estimatedPages,
    };

  } catch (error) {
    console.error('PDF parsing error:', error);
    throw new Error('Unable to extract text from PDF. Please ensure the PDF contains readable text and is not password-protected.');
  }
}

export function validatePDFBuffer(buffer: Buffer): boolean {
  // Check if buffer starts with PDF signature
  const pdfSignature = Buffer.from([0x25, 0x50, 0x44, 0x46]); // %PDF
  return buffer.length > 4 && buffer.subarray(0, 4).equals(pdfSignature);
}
