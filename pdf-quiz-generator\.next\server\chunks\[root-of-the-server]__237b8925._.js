module.exports = {

"[project]/.next-internal/server/app/api/generate-quiz/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/lib/deepseek.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "generateEmbedding": (()=>generateEmbedding),
    "generateQuestions": (()=>generateQuestions)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/index.mjs [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/client.mjs [app-route] (ecmascript) <export OpenAI as default>");
;
const deepseek = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__["default"]({
    apiKey: process.env.DEEPSEEK_API_KEY,
    baseURL: process.env.DEEPSEEK_BASE_URL || 'https://api.deepseek.com'
});
function generateEmbedding(text) {
    // Return a simple hash-based pseudo-embedding for compatibility
    // This is just for maintaining the interface, not for semantic similarity
    const hash = text.split('').reduce((a, b)=>{
        a = (a << 5) - a + b.charCodeAt(0);
        return a & a;
    }, 0);
    // Generate a simple 384-dimensional vector based on text characteristics
    const embedding = Array.from({
        length: 384
    }, (_, i)=>{
        return Math.sin(hash * (i + 1) * 0.01) * 0.1;
    });
    return embedding;
}
async function generateQuestions(chunks, questionTypes, difficulty, questionsPerType) {
    const questions = [];
    for (const questionType of questionTypes){
        for (const diff of difficulty){
            const typeQuestions = await generateQuestionsForType(chunks, questionType, diff, questionsPerType);
            questions.push(...typeQuestions);
        }
    }
    return questions;
}
async function generateQuestionsForType(chunks, questionType, difficulty, count) {
    const selectedChunks = selectRelevantChunks(chunks, count);
    const questions = [];
    for(let i = 0; i < Math.min(count, selectedChunks.length); i++){
        const chunk = selectedChunks[i];
        try {
            const question = await generateSingleQuestion(chunk, questionType, difficulty);
            if (question) {
                questions.push(question);
            }
        } catch (error) {
            console.error(`Error generating question for chunk ${chunk.id}:`, error);
        }
    }
    return questions;
}
async function generateSingleQuestion(chunk, questionType, difficulty) {
    const prompt = createPrompt(chunk.content, questionType, difficulty);
    try {
        const response = await deepseek.chat.completions.create({
            model: 'deepseek-chat',
            messages: [
                {
                    role: 'system',
                    content: 'You are an expert quiz generator. Generate high-quality questions based on the provided text. Always respond with valid JSON format. Do not include any explanatory text outside the JSON.'
                },
                {
                    role: 'user',
                    content: prompt
                }
            ],
            temperature: 0.7,
            max_tokens: 1000
        });
        const content = response.choices[0]?.message?.content;
        if (!content) return null;
        const questionData = JSON.parse(content);
        return createQuestionObject(questionData, questionType, difficulty, chunk.content);
    } catch (error) {
        console.error('Error generating question:', error);
        return null;
    }
}
function createPrompt(text, questionType, difficulty) {
    const basePrompt = `Based on the following text, generate a ${difficulty} difficulty ${questionType} question:\n\n"${text}"\n\n`;
    switch(questionType){
        case 'mcq':
            return basePrompt + `Generate a multiple-choice question with 4 options. Respond in JSON format:
{
  "question": "Your question here",
  "options": [
    {"text": "Option A", "isCorrect": false},
    {"text": "Option B", "isCorrect": true},
    {"text": "Option C", "isCorrect": false},
    {"text": "Option D", "isCorrect": false}
  ],
  "explanation": "Brief explanation of the correct answer"
}`;
        case 'true-false':
            return basePrompt + `Generate a true/false question. Respond in JSON format:
{
  "question": "Your true/false statement here",
  "correctAnswer": true,
  "explanation": "Brief explanation of why this is true/false"
}`;
        case 'fill-blank':
            return basePrompt + `Generate a fill-in-the-blank question. Respond in JSON format:
{
  "question": "Your question with _____ blank",
  "correctAnswer": "correct answer",
  "acceptableAnswers": ["alternative answer 1", "alternative answer 2"],
  "explanation": "Brief explanation of the answer"
}`;
        default:
            return basePrompt;
    }
}
function createQuestionObject(data, questionType, difficulty, sourceChunk) {
    const baseQuestion = {
        id: Math.random().toString(36).substring(2, 11),
        difficulty,
        sourceChunk,
        explanation: data.explanation
    };
    switch(questionType){
        case 'mcq':
            return {
                ...baseQuestion,
                type: 'mcq',
                question: data.question,
                options: data.options.map((opt, index)=>({
                        id: `opt_${index}`,
                        text: opt.text,
                        isCorrect: opt.isCorrect
                    }))
            };
        case 'true-false':
            return {
                ...baseQuestion,
                type: 'true-false',
                question: data.question,
                correctAnswer: data.correctAnswer
            };
        case 'fill-blank':
            return {
                ...baseQuestion,
                type: 'fill-blank',
                question: data.question,
                correctAnswer: data.correctAnswer,
                acceptableAnswers: data.acceptableAnswers || []
            };
        default:
            throw new Error(`Unsupported question type: ${questionType}`);
    }
}
function selectRelevantChunks(chunks, count) {
    // Simple selection - in a real app, you might use embedding similarity
    const shuffled = [
        ...chunks
    ].sort(()=>Math.random() - 0.5);
    return shuffled.slice(0, count);
}
}}),
"[project]/src/lib/utils.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "chunkText": (()=>chunkText),
    "cn": (()=>cn),
    "downloadAsCSV": (()=>downloadAsCSV),
    "downloadAsJSON": (()=>downloadAsJSON),
    "formatFileSize": (()=>formatFileSize),
    "generateId": (()=>generateId),
    "sanitizeText": (()=>sanitizeText),
    "validatePDF": (()=>validatePDF)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/clsx/dist/clsx.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tailwind-merge/dist/bundle-mjs.mjs [app-route] (ecmascript)");
;
;
function cn(...inputs) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["twMerge"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["clsx"])(inputs));
}
function generateId() {
    return Math.random().toString(36).substr(2, 9);
}
function chunkText(text, maxChunkSize = 1000, overlap = 100) {
    const chunks = [];
    const sentences = text.split(/[.!?]+/).filter((s)=>s.trim().length > 0);
    let currentChunk = '';
    let currentSize = 0;
    for (const sentence of sentences){
        const sentenceSize = sentence.trim().length;
        if (currentSize + sentenceSize > maxChunkSize && currentChunk.length > 0) {
            chunks.push(currentChunk.trim());
            // Create overlap by keeping the last few sentences
            const overlapSentences = currentChunk.split(/[.!?]+/).slice(-2).join('. ');
            currentChunk = overlapSentences + '. ' + sentence.trim();
            currentSize = currentChunk.length;
        } else {
            currentChunk += (currentChunk ? '. ' : '') + sentence.trim();
            currentSize = currentChunk.length;
        }
    }
    if (currentChunk.trim().length > 0) {
        chunks.push(currentChunk.trim());
    }
    return chunks.filter((chunk)=>chunk.length > 50) // Filter out very short chunks
    ;
}
function sanitizeText(text) {
    return text.replace(/\s+/g, ' ').replace(/[^\w\s.,!?;:()\-"']/g, '').trim();
}
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = [
        'Bytes',
        'KB',
        'MB',
        'GB'
    ];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
function validatePDF(file) {
    if (!file) {
        return {
            isValid: false,
            error: 'No file provided'
        };
    }
    if (file.type !== 'application/pdf') {
        return {
            isValid: false,
            error: 'File must be a PDF'
        };
    }
    // 50MB limit
    if (file.size > 50 * 1024 * 1024) {
        return {
            isValid: false,
            error: 'File size must be less than 50MB'
        };
    }
    return {
        isValid: true
    };
}
function downloadAsJSON(data, filename) {
    const blob = new Blob([
        JSON.stringify(data, null, 2)
    ], {
        type: 'application/json'
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}
function downloadAsCSV(data, filename) {
    if (data.length === 0) return;
    const headers = Object.keys(data[0]);
    const csvContent = [
        headers.join(','),
        ...data.map((row)=>headers.map((header)=>{
                const value = row[header];
                if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
                    return `"${value.replace(/"/g, '""')}"`;
                }
                return value;
            }).join(','))
    ].join('\n');
    const blob = new Blob([
        csvContent
    ], {
        type: 'text/csv'
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}
}}),
"[project]/src/app/api/generate-quiz/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$deepseek$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/deepseek.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-route] (ecmascript)");
;
;
;
async function POST(request) {
    try {
        const body = await request.json();
        const { chunks, options, documentName } = body;
        if (!chunks || chunks.length === 0) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'No document chunks provided'
            }, {
                status: 400
            });
        }
        if (!options) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Quiz generation options not provided'
            }, {
                status: 400
            });
        }
        // Validate options
        if (!options.questionTypes || options.questionTypes.length === 0) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'At least one question type must be selected'
            }, {
                status: 400
            });
        }
        if (!options.difficulty || options.difficulty.length === 0) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'At least one difficulty level must be selected'
            }, {
                status: 400
            });
        }
        if (options.questionsPerType < 1 || options.questionsPerType > 10) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Questions per type must be between 1 and 10'
            }, {
                status: 400
            });
        }
        try {
            // Generate simple embeddings for chunks if not already present
            // Note: DeepSeek doesn't provide embedding models, so we use a simple approach
            const chunksWithEmbeddings = chunks.map((chunk)=>{
                if (!chunk.embedding) {
                    try {
                        const embedding = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$deepseek$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateEmbedding"])(chunk.content);
                        return {
                            ...chunk,
                            embedding
                        };
                    } catch (embeddingError) {
                        console.warn(`Failed to generate embedding for chunk ${chunk.id}:`, embeddingError);
                        return chunk; // Return chunk without embedding
                    }
                }
                return chunk;
            });
            // Generate questions
            const questions = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$deepseek$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateQuestions"])(chunksWithEmbeddings, options.questionTypes, options.difficulty, options.questionsPerType);
            if (questions.length === 0) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'Failed to generate any questions. Please try with different options or check your document content.'
                }, {
                    status: 400
                });
            }
            // Create quiz object
            const quiz = {
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateId"])(),
                title: `Quiz: ${documentName}`,
                description: `Generated quiz with ${questions.length} questions from ${documentName}`,
                questions,
                createdAt: new Date(),
                sourceDocument: documentName
            };
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: true,
                quiz,
                stats: {
                    totalQuestions: questions.length,
                    questionTypes: options.questionTypes,
                    difficulties: options.difficulty,
                    questionsPerType: options.questionsPerType
                }
            });
        } catch (generationError) {
            console.error('Quiz generation error:', generationError);
            // Check if it's an API key issue
            if (generationError instanceof Error && generationError.message.includes('API key')) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'DeepSeek API key is not configured or invalid. Please check your environment variables.'
                }, {
                    status: 401
                });
            }
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Failed to generate quiz. Please try again or contact support if the issue persists.'
            }, {
                status: 500
            });
        }
    } catch (error) {
        console.error('Request processing error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Invalid request format or internal server error'
        }, {
            status: 500
        });
    }
}
async function GET() {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
        message: 'Quiz generation endpoint',
        supportedQuestionTypes: [
            'mcq',
            'true-false',
            'fill-blank'
        ],
        supportedDifficulties: [
            'easy',
            'medium',
            'hard'
        ]
    });
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__237b8925._.js.map