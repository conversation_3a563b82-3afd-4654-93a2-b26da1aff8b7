%!PS-Adobe-3.0 Resource-CMap
%%DocumentNeededResources: ProcSet (CIDInit)
%%DocumentNeededResources: CMap (UniGB-UTF8-H)
%%IncludeResource: ProcSet (CIDInit)
%%IncludeResource: CMap (UniGB-UTF8-H)
%%BeginResource: CMap (UniGB-UTF8-V)
%%Title: (UniGB-UTF8-V Adobe GB1 5)
%%Version: 13.006
%%Copyright: -----------------------------------------------------------
%%Copyright: Copyright 1990-2015 Adobe Systems Incorporated.
%%Copyright: All rights reserved.
%%Copyright:
%%Copyright: Redistribution and use in source and binary forms, with or
%%Copyright: without modification, are permitted provided that the
%%Copyright: following conditions are met:
%%Copyright:
%%Copyright: Redistributions of source code must retain the above
%%Copyright: copyright notice, this list of conditions and the following
%%Copyright: disclaimer.
%%Copyright:
%%Copyright: Redistributions in binary form must reproduce the above
%%Copyright: copyright notice, this list of conditions and the following
%%Copyright: disclaimer in the documentation and/or other materials
%%Copyright: provided with the distribution. 
%%Copyright:
%%Copyright: Neither the name of Adobe Systems Incorporated nor the names
%%Copyright: of its contributors may be used to endorse or promote
%%Copyright: products derived from this software without specific prior
%%Copyright: written permission. 
%%Copyright:
%%Copyright: THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND
%%Copyright: CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES,
%%Copyright: INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
%%Copyright: MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
%%Copyright: DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR
%%Copyright: CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
%%Copyright: SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT
%%Copyright: NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
%%Copyright: LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
%%Copyright: HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
%%Copyright: CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
%%Copyright: OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
%%Copyright: SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
%%Copyright: -----------------------------------------------------------
%%EndComments

/CIDInit /ProcSet findresource begin

12 dict begin

begincmap

/UniGB-UTF8-H usecmap

/CIDSystemInfo 3 dict dup begin
  /Registry (Adobe) def
  /Ordering (GB1) def
  /Supplement 5 def
end def

/CMapName /UniGB-UTF8-V def
/CMapVersion 13.006 def
/CMapType 1 def

/XUID [1 10 25450] def

/WMode 1 def

17 begincidchar
<e28094> 598
<e280a6> 599
<e38081> 575
<e38082> 574
<e38093> 7706
<efbc81> 578
<efbc8c> 573
<efbc8e> 7707
<efbc9d> 7708
<efbc9f> 579
<efbcbb> 7709
<efbcbd> 7710
<efbcbf> 600
<efbd9b> 596
<efbd9d> 597
<efbd9e> 7704
<efbfa3> 7711
endcidchar

6 begincidrange
<e38088> <e3808f> 584
<e38090> <e38091> 594
<e38094> <e38095> 582
<e38096> <e38097> 592
<efbc88> <efbc89> 580
<efbc9a> <efbc9b> 576
endcidrange

endcmap
CMapName currentdict /CMap defineresource pop
end
end

%%EndResource
%%EOF
