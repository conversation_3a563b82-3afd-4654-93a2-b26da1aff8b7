{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Z:/projectx/pdf-quiz-generator/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9)\n}\n\nexport function chunkText(text: string, maxChunkSize: number = 1000, overlap: number = 100): string[] {\n  const chunks: string[] = []\n  const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0)\n  \n  let currentChunk = ''\n  let currentSize = 0\n  \n  for (const sentence of sentences) {\n    const sentenceSize = sentence.trim().length\n    \n    if (currentSize + sentenceSize > maxChunkSize && currentChunk.length > 0) {\n      chunks.push(currentChunk.trim())\n      \n      // Create overlap by keeping the last few sentences\n      const overlapSentences = currentChunk.split(/[.!?]+/).slice(-2).join('. ')\n      currentChunk = overlapSentences + '. ' + sentence.trim()\n      currentSize = currentChunk.length\n    } else {\n      currentChunk += (currentChunk ? '. ' : '') + sentence.trim()\n      currentSize = currentChunk.length\n    }\n  }\n  \n  if (currentChunk.trim().length > 0) {\n    chunks.push(currentChunk.trim())\n  }\n  \n  return chunks.filter(chunk => chunk.length > 50) // Filter out very short chunks\n}\n\nexport function sanitizeText(text: string): string {\n  return text\n    .replace(/\\s+/g, ' ')\n    .replace(/[^\\w\\s.,!?;:()\\-\"']/g, '')\n    .trim()\n}\n\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes'\n  \n  const k = 1024\n  const sizes = ['Bytes', 'KB', 'MB', 'GB']\n  const i = Math.floor(Math.log(bytes) / Math.log(k))\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n}\n\nexport function validatePDF(file: File): { isValid: boolean; error?: string } {\n  if (!file) {\n    return { isValid: false, error: 'No file provided' }\n  }\n  \n  if (file.type !== 'application/pdf') {\n    return { isValid: false, error: 'File must be a PDF' }\n  }\n  \n  // 50MB limit\n  if (file.size > 50 * 1024 * 1024) {\n    return { isValid: false, error: 'File size must be less than 50MB' }\n  }\n  \n  return { isValid: true }\n}\n\nexport function downloadAsJSON(data: any, filename: string) {\n  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })\n  const url = URL.createObjectURL(blob)\n  const a = document.createElement('a')\n  a.href = url\n  a.download = filename\n  document.body.appendChild(a)\n  a.click()\n  document.body.removeChild(a)\n  URL.revokeObjectURL(url)\n}\n\nexport function downloadAsCSV(data: any[], filename: string) {\n  if (data.length === 0) return\n  \n  const headers = Object.keys(data[0])\n  const csvContent = [\n    headers.join(','),\n    ...data.map(row => \n      headers.map(header => {\n        const value = row[header]\n        if (typeof value === 'string' && (value.includes(',') || value.includes('\"'))) {\n          return `\"${value.replace(/\"/g, '\"\"')}\"`\n        }\n        return value\n      }).join(',')\n    )\n  ].join('\\n')\n  \n  const blob = new Blob([csvContent], { type: 'text/csv' })\n  const url = URL.createObjectURL(blob)\n  const a = document.createElement('a')\n  a.href = url\n  a.download = filename\n  document.body.appendChild(a)\n  a.click()\n  document.body.removeChild(a)\n  URL.revokeObjectURL(url)\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,UAAU,IAAY,EAAE,eAAuB,IAAI,EAAE,UAAkB,GAAG;IACxF,MAAM,SAAmB,EAAE;IAC3B,MAAM,YAAY,KAAK,KAAK,CAAC,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,GAAG,MAAM,GAAG;IAErE,IAAI,eAAe;IACnB,IAAI,cAAc;IAElB,KAAK,MAAM,YAAY,UAAW;QAChC,MAAM,eAAe,SAAS,IAAI,GAAG,MAAM;QAE3C,IAAI,cAAc,eAAe,gBAAgB,aAAa,MAAM,GAAG,GAAG;YACxE,OAAO,IAAI,CAAC,aAAa,IAAI;YAE7B,mDAAmD;YACnD,MAAM,mBAAmB,aAAa,KAAK,CAAC,UAAU,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC;YACrE,eAAe,mBAAmB,OAAO,SAAS,IAAI;YACtD,cAAc,aAAa,MAAM;QACnC,OAAO;YACL,gBAAgB,CAAC,eAAe,OAAO,EAAE,IAAI,SAAS,IAAI;YAC1D,cAAc,aAAa,MAAM;QACnC;IACF;IAEA,IAAI,aAAa,IAAI,GAAG,MAAM,GAAG,GAAG;QAClC,OAAO,IAAI,CAAC,aAAa,IAAI;IAC/B;IAEA,OAAO,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,GAAG,IAAI,+BAA+B;;AAClF;AAEO,SAAS,aAAa,IAAY;IACvC,OAAO,KACJ,OAAO,CAAC,QAAQ,KAChB,OAAO,CAAC,wBAAwB,IAChC,IAAI;AACT;AAEO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAEO,SAAS,YAAY,IAAU;IACpC,IAAI,CAAC,MAAM;QACT,OAAO;YAAE,SAAS;YAAO,OAAO;QAAmB;IACrD;IAEA,IAAI,KAAK,IAAI,KAAK,mBAAmB;QACnC,OAAO;YAAE,SAAS;YAAO,OAAO;QAAqB;IACvD;IAEA,aAAa;IACb,IAAI,KAAK,IAAI,GAAG,KAAK,OAAO,MAAM;QAChC,OAAO;YAAE,SAAS;YAAO,OAAO;QAAmC;IACrE;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAEO,SAAS,eAAe,IAAS,EAAE,QAAgB;IACxD,MAAM,OAAO,IAAI,KAAK;QAAC,KAAK,SAAS,CAAC,MAAM,MAAM;KAAG,EAAE;QAAE,MAAM;IAAmB;IAClF,MAAM,MAAM,IAAI,eAAe,CAAC;IAChC,MAAM,IAAI,SAAS,aAAa,CAAC;IACjC,EAAE,IAAI,GAAG;IACT,EAAE,QAAQ,GAAG;IACb,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,EAAE,KAAK;IACP,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,IAAI,eAAe,CAAC;AACtB;AAEO,SAAS,cAAc,IAAW,EAAE,QAAgB;IACzD,IAAI,KAAK,MAAM,KAAK,GAAG;IAEvB,MAAM,UAAU,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE;IACnC,MAAM,aAAa;QACjB,QAAQ,IAAI,CAAC;WACV,KAAK,GAAG,CAAC,CAAA,MACV,QAAQ,GAAG,CAAC,CAAA;gBACV,MAAM,QAAQ,GAAG,CAAC,OAAO;gBACzB,IAAI,OAAO,UAAU,YAAY,CAAC,MAAM,QAAQ,CAAC,QAAQ,MAAM,QAAQ,CAAC,IAAI,GAAG;oBAC7E,OAAO,CAAC,CAAC,EAAE,MAAM,OAAO,CAAC,MAAM,MAAM,CAAC,CAAC;gBACzC;gBACA,OAAO;YACT,GAAG,IAAI,CAAC;KAEX,CAAC,IAAI,CAAC;IAEP,MAAM,OAAO,IAAI,KAAK;QAAC;KAAW,EAAE;QAAE,MAAM;IAAW;IACvD,MAAM,MAAM,IAAI,eAAe,CAAC;IAChC,MAAM,IAAI,SAAS,aAAa,CAAC;IACjC,EAAE,IAAI,GAAG;IACT,EAAE,QAAQ,GAAG;IACb,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,EAAE,KAAK;IACP,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,IAAI,eAAe,CAAC;AACtB", "debugId": null}}, {"offset": {"line": 146, "column": 0}, "map": {"version": 3, "sources": ["file:///Z:/projectx/pdf-quiz-generator/src/components/FileUpload.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useCallback } from 'react';\nimport { useDropzone } from 'react-dropzone';\nimport { Upload, FileText, AlertCircle, CheckCircle } from 'lucide-react';\nimport { ProcessedDocument } from '@/types/quiz';\nimport { validatePDF, formatFileSize } from '@/lib/utils';\n\ninterface FileUploadProps {\n  onDocumentProcessed: (document: ProcessedDocument) => void;\n}\n\nexport function FileUpload({ onDocumentProcessed }: FileUploadProps) {\n  const [isUploading, setIsUploading] = useState(false);\n  const [uploadProgress, setUploadProgress] = useState(0);\n  const [error, setError] = useState<string | null>(null);\n  const [uploadedFile, setUploadedFile] = useState<File | null>(null);\n\n  const onDrop = useCallback(async (acceptedFiles: File[]) => {\n    const file = acceptedFiles[0];\n    if (!file) return;\n\n    // Validate file\n    const validation = validatePDF(file);\n    if (!validation.isValid) {\n      setError(validation.error || 'Invalid file');\n      return;\n    }\n\n    setError(null);\n    setUploadedFile(file);\n    setIsUploading(true);\n    setUploadProgress(0);\n\n    try {\n      const formData = new FormData();\n      formData.append('file', file);\n\n      // Simulate progress\n      const progressInterval = setInterval(() => {\n        setUploadProgress(prev => {\n          if (prev >= 90) {\n            clearInterval(progressInterval);\n            return prev;\n          }\n          return prev + 10;\n        });\n      }, 200);\n\n      const response = await fetch('/api/upload', {\n        method: 'POST',\n        body: formData,\n      });\n\n      clearInterval(progressInterval);\n      setUploadProgress(100);\n\n      if (!response.ok) {\n        let errorMessage = 'Upload failed';\n        try {\n          const errorData = await response.json();\n          errorMessage = errorData.error || errorMessage;\n        } catch (parseError) {\n          // If we can't parse JSON, it might be an HTML error page\n          const errorText = await response.text();\n          console.error('Server error response:', errorText);\n          errorMessage = `Server error (${response.status}): ${response.statusText}`;\n        }\n        throw new Error(errorMessage);\n      }\n\n      let result;\n      try {\n        result = await response.json();\n      } catch (parseError) {\n        console.error('Failed to parse response as JSON:', parseError);\n        throw new Error('Invalid response from server');\n      }\n      \n      if (result.success && result.document) {\n        onDocumentProcessed(result.document);\n      } else {\n        throw new Error('Invalid response from server');\n      }\n\n    } catch (err) {\n      console.error('Upload error:', err);\n      setError(err instanceof Error ? err.message : 'Upload failed');\n    } finally {\n      setIsUploading(false);\n      setUploadProgress(0);\n    }\n  }, [onDocumentProcessed]);\n\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\n    onDrop,\n    accept: {\n      'application/pdf': ['.pdf']\n    },\n    maxFiles: 1,\n    disabled: isUploading\n  });\n\n  return (\n    <div className=\"w-full max-w-2xl mx-auto\">\n      <div className=\"mb-6\">\n        <h2 className=\"text-2xl font-semibold text-gray-900 dark:text-white mb-2\">\n          Upload Your PDF Document\n        </h2>\n        <p className=\"text-gray-600 dark:text-gray-300\">\n          Upload a PDF document (up to 50MB) to generate intelligent quiz questions.\n          The system works best with text-rich documents like textbooks, research papers, or educational materials.\n        </p>\n      </div>\n\n      <div\n        {...getRootProps()}\n        className={`\n          relative border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors\n          ${isDragActive \n            ? 'border-blue-400 bg-blue-50 dark:bg-blue-900/20' \n            : 'border-gray-300 dark:border-gray-600 hover:border-blue-400 hover:bg-gray-50 dark:hover:bg-gray-700'\n          }\n          ${isUploading ? 'pointer-events-none opacity-75' : ''}\n        `}\n      >\n        <input {...getInputProps()} />\n        \n        <div className=\"flex flex-col items-center space-y-4\">\n          {isUploading ? (\n            <>\n              <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n              <div className=\"w-full max-w-xs\">\n                <div className=\"bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                  <div \n                    className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\n                    style={{ width: `${uploadProgress}%` }}\n                  ></div>\n                </div>\n                <p className=\"text-sm text-gray-600 dark:text-gray-300 mt-2\">\n                  Processing PDF... {uploadProgress}%\n                </p>\n              </div>\n            </>\n          ) : (\n            <>\n              <Upload className=\"h-12 w-12 text-gray-400\" />\n              <div>\n                <p className=\"text-lg font-medium text-gray-900 dark:text-white\">\n                  {isDragActive ? 'Drop your PDF here' : 'Drag & drop your PDF here'}\n                </p>\n                <p className=\"text-sm text-gray-500 dark:text-gray-400 mt-1\">\n                  or click to browse files\n                </p>\n              </div>\n            </>\n          )}\n        </div>\n\n        {uploadedFile && !isUploading && (\n          <div className=\"mt-4 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800\">\n            <div className=\"flex items-center space-x-2\">\n              <CheckCircle className=\"h-5 w-5 text-green-600\" />\n              <div className=\"flex-1 text-left\">\n                <p className=\"text-sm font-medium text-green-800 dark:text-green-200\">\n                  {uploadedFile.name}\n                </p>\n                <p className=\"text-xs text-green-600 dark:text-green-300\">\n                  {formatFileSize(uploadedFile.size)}\n                </p>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {error && (\n        <div className=\"mt-4 p-4 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800\">\n          <div className=\"flex items-center space-x-2\">\n            <AlertCircle className=\"h-5 w-5 text-red-600\" />\n            <p className=\"text-sm text-red-800 dark:text-red-200\">{error}</p>\n          </div>\n        </div>\n      )}\n\n      <div className=\"mt-6 grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600 dark:text-gray-300\">\n        <div className=\"flex items-center space-x-2\">\n          <FileText className=\"h-4 w-4\" />\n          <span>PDF files only</span>\n        </div>\n        <div className=\"flex items-center space-x-2\">\n          <Upload className=\"h-4 w-4\" />\n          <span>Max 50MB size</span>\n        </div>\n        <div className=\"flex items-center space-x-2\">\n          <CheckCircle className=\"h-4 w-4\" />\n          <span>Text-based content</span>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAEA;AANA;;;;;;AAYO,SAAS,WAAW,EAAE,mBAAmB,EAAmB;IACjE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAE9D,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAChC,MAAM,OAAO,aAAa,CAAC,EAAE;QAC7B,IAAI,CAAC,MAAM;QAEX,gBAAgB;QAChB,MAAM,aAAa,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,IAAI,CAAC,WAAW,OAAO,EAAE;YACvB,SAAS,WAAW,KAAK,IAAI;YAC7B;QACF;QAEA,SAAS;QACT,gBAAgB;QAChB,eAAe;QACf,kBAAkB;QAElB,IAAI;YACF,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YAExB,oBAAoB;YACpB,MAAM,mBAAmB,YAAY;gBACnC,kBAAkB,CAAA;oBAChB,IAAI,QAAQ,IAAI;wBACd,cAAc;wBACd,OAAO;oBACT;oBACA,OAAO,OAAO;gBAChB;YACF,GAAG;YAEH,MAAM,WAAW,MAAM,MAAM,eAAe;gBAC1C,QAAQ;gBACR,MAAM;YACR;YAEA,cAAc;YACd,kBAAkB;YAElB,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,IAAI,eAAe;gBACnB,IAAI;oBACF,MAAM,YAAY,MAAM,SAAS,IAAI;oBACrC,eAAe,UAAU,KAAK,IAAI;gBACpC,EAAE,OAAO,YAAY;oBACnB,yDAAyD;oBACzD,MAAM,YAAY,MAAM,SAAS,IAAI;oBACrC,QAAQ,KAAK,CAAC,0BAA0B;oBACxC,eAAe,CAAC,cAAc,EAAE,SAAS,MAAM,CAAC,GAAG,EAAE,SAAS,UAAU,EAAE;gBAC5E;gBACA,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI;YACJ,IAAI;gBACF,SAAS,MAAM,SAAS,IAAI;YAC9B,EAAE,OAAO,YAAY;gBACnB,QAAQ,KAAK,CAAC,qCAAqC;gBACnD,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,OAAO,OAAO,IAAI,OAAO,QAAQ,EAAE;gBACrC,oBAAoB,OAAO,QAAQ;YACrC,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QAEF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,eAAe;YACf,kBAAkB;QACpB;IACF,GAAG;QAAC;KAAoB;IAExB,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,wKAAA,CAAA,cAAW,AAAD,EAAE;QAChE;QACA,QAAQ;YACN,mBAAmB;gBAAC;aAAO;QAC7B;QACA,UAAU;QACV,UAAU;IACZ;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA4D;;;;;;kCAG1E,8OAAC;wBAAE,WAAU;kCAAmC;;;;;;;;;;;;0BAMlD,8OAAC;gBACE,GAAG,cAAc;gBAClB,WAAW,CAAC;;UAEV,EAAE,eACE,mDACA,qGACH;UACD,EAAE,cAAc,mCAAmC,GAAG;QACxD,CAAC;;kCAED,8OAAC;wBAAO,GAAG,eAAe;;;;;;kCAE1B,8OAAC;wBAAI,WAAU;kCACZ,4BACC;;8CACE,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,OAAO,GAAG,eAAe,CAAC,CAAC;gDAAC;;;;;;;;;;;sDAGzC,8OAAC;4CAAE,WAAU;;gDAAgD;gDACxC;gDAAe;;;;;;;;;;;;;;yDAKxC;;8CACE,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDACV,eAAe,uBAAuB;;;;;;sDAEzC,8OAAC;4CAAE,WAAU;sDAAgD;;;;;;;;;;;;;;;;;;;oBAQpE,gBAAgB,CAAC,6BAChB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,2NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDACV,aAAa,IAAI;;;;;;sDAEpB,8OAAC;4CAAE,WAAU;sDACV,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQ5C,uBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;;;;;;0BAK7D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,8MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC;0CAAK;;;;;;;;;;;;kCAER,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;0CAAK;;;;;;;;;;;;kCAER,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,2NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,8OAAC;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;AAKhB", "debugId": null}}, {"offset": {"line": 555, "column": 0}, "map": {"version": 3, "sources": ["file:///Z:/projectx/pdf-quiz-generator/src/components/QuizGenerator.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Brain } from 'lucide-react';\nimport { ProcessedDocument, Quiz, QuestionType, DifficultyLevel, QuizGenerationOptions } from '@/types/quiz';\n\ninterface QuizGeneratorProps {\n  document: ProcessedDocument;\n  onQuizGenerated: (quiz: Quiz) => void;\n  onBack: () => void;\n}\n\nexport function QuizGenerator({ document, onQuizGenerated, onBack }: QuizGeneratorProps) {\n  const [isGenerating, setIsGenerating] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [options, setOptions] = useState<QuizGenerationOptions>({\n    questionTypes: ['mcq'],\n    difficulty: ['medium'],\n    questionsPerType: 5,\n    includeExplanations: true,\n  });\n\n  const handleGenerate = async () => {\n    setIsGenerating(true);\n    setError(null);\n\n    try {\n      const response = await fetch('/api/generate-quiz', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          chunks: document.chunks,\n          options,\n          documentName: document.filename,\n          language: document.metadata?.language,\n        }),\n      });\n\n      if (!response.ok) {\n        let errorMessage = 'Failed to generate quiz';\n        try {\n          const errorData = await response.json();\n          errorMessage = errorData.error || errorMessage;\n        } catch (parseError) {\n          const errorText = await response.text();\n          console.error('Server error response:', errorText);\n          errorMessage = `Server error (${response.status}): ${response.statusText}`;\n        }\n        throw new Error(errorMessage);\n      }\n\n      let result;\n      try {\n        result = await response.json();\n      } catch (parseError) {\n        console.error('Failed to parse response as JSON:', parseError);\n        throw new Error('Invalid response from server');\n      }\n      \n      if (result.success && result.quiz) {\n        onQuizGenerated(result.quiz);\n      } else {\n        throw new Error('Invalid response from server');\n      }\n\n    } catch (err) {\n      console.error('Quiz generation error:', err);\n      setError(err instanceof Error ? err.message : 'Failed to generate quiz');\n    } finally {\n      setIsGenerating(false);\n    }\n  };\n\n  const handleQuestionTypeChange = (type: QuestionType, checked: boolean) => {\n    setOptions(prev => ({\n      ...prev,\n      questionTypes: checked \n        ? [...prev.questionTypes, type]\n        : prev.questionTypes.filter(t => t !== type)\n    }));\n  };\n\n  const handleDifficultyChange = (difficulty: DifficultyLevel, checked: boolean) => {\n    setOptions(prev => ({\n      ...prev,\n      difficulty: checked \n        ? [...prev.difficulty, difficulty]\n        : prev.difficulty.filter(d => d !== difficulty)\n    }));\n  };\n\n  return (\n    <div className=\"w-full max-w-4xl mx-auto\">\n      <div className=\"flex items-center mb-6\">\n        <button\n          onClick={onBack}\n          className=\"flex items-center space-x-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white\"\n        >\n          <ArrowLeft className=\"h-5 w-5\" />\n          <span>Back to Upload</span>\n        </button>\n      </div>\n\n      <div className=\"mb-6\">\n        <h2 className=\"text-2xl font-semibold text-gray-900 dark:text-white mb-2\">\n          Configure Quiz Generation\n        </h2>\n        <p className=\"text-gray-600 dark:text-gray-300\">\n          Customize your quiz settings and generate intelligent questions from your PDF document.\n        </p>\n      </div>\n\n      {/* Document Info */}\n      <div className=\"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 mb-6\">\n        <div className=\"flex items-center space-x-3\">\n          <BookOpen className=\"h-6 w-6 text-blue-600\" />\n          <div>\n            <h3 className=\"font-medium text-blue-900 dark:text-blue-100\">{document.filename}</h3>\n            <p className=\"text-sm text-blue-700 dark:text-blue-300\">\n              {document.chunks.length} text chunks • {document.totalPages} pages\n              {document.metadata?.language && (\n                <span className=\"ml-2 px-2 py-1 bg-blue-200 dark:bg-blue-800 rounded text-xs\">\n                  Language: {document.metadata.language}\n                </span>\n              )}\n            </p>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8\">\n        {/* Question Types */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6\">\n          <div className=\"flex items-center space-x-2 mb-4\">\n            <Settings className=\"h-5 w-5 text-gray-600 dark:text-gray-300\" />\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-white\">Question Types</h3>\n          </div>\n          \n          <div className=\"space-y-3\">\n            <label className=\"flex items-center space-x-3\">\n              <input\n                type=\"checkbox\"\n                checked={options.questionTypes.includes('mcq')}\n                onChange={(e) => handleQuestionTypeChange('mcq', e.target.checked)}\n                className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n              />\n              <div>\n                <span className=\"font-medium text-gray-900 dark:text-white\">Multiple Choice</span>\n                <p className=\"text-sm text-gray-500 dark:text-gray-400\">4-option multiple choice questions</p>\n              </div>\n            </label>\n            \n            <label className=\"flex items-center space-x-3\">\n              <input\n                type=\"checkbox\"\n                checked={options.questionTypes.includes('true-false')}\n                onChange={(e) => handleQuestionTypeChange('true-false', e.target.checked)}\n                className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n              />\n              <div>\n                <span className=\"font-medium text-gray-900 dark:text-white\">True/False</span>\n                <p className=\"text-sm text-gray-500 dark:text-gray-400\">Binary true or false questions</p>\n              </div>\n            </label>\n            \n            <label className=\"flex items-center space-x-3\">\n              <input\n                type=\"checkbox\"\n                checked={options.questionTypes.includes('fill-blank')}\n                onChange={(e) => handleQuestionTypeChange('fill-blank', e.target.checked)}\n                className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n              />\n              <div>\n                <span className=\"font-medium text-gray-900 dark:text-white\">Fill in the Blank</span>\n                <p className=\"text-sm text-gray-500 dark:text-gray-400\">Complete the missing word or phrase</p>\n              </div>\n            </label>\n          </div>\n        </div>\n\n        {/* Difficulty & Settings */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6\">\n          <div className=\"flex items-center space-x-2 mb-4\">\n            <Brain className=\"h-5 w-5 text-gray-600 dark:text-gray-300\" />\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-white\">Difficulty & Settings</h3>\n          </div>\n          \n          <div className=\"space-y-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Difficulty Levels\n              </label>\n              <div className=\"space-y-2\">\n                {(['easy', 'medium', 'hard'] as DifficultyLevel[]).map((level) => (\n                  <label key={level} className=\"flex items-center space-x-2\">\n                    <input\n                      type=\"checkbox\"\n                      checked={options.difficulty.includes(level)}\n                      onChange={(e) => handleDifficultyChange(level, e.target.checked)}\n                      className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                    />\n                    <span className=\"capitalize text-gray-900 dark:text-white\">{level}</span>\n                  </label>\n                ))}\n              </div>\n            </div>\n            \n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Questions per Type: {options.questionsPerType}\n              </label>\n              <input\n                type=\"range\"\n                min=\"1\"\n                max=\"10\"\n                value={options.questionsPerType}\n                onChange={(e) => setOptions(prev => ({ ...prev, questionsPerType: parseInt(e.target.value) }))}\n                className=\"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700\"\n              />\n              <div className=\"flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1\">\n                <span>1</span>\n                <span>10</span>\n              </div>\n            </div>\n            \n            <label className=\"flex items-center space-x-2\">\n              <input\n                type=\"checkbox\"\n                checked={options.includeExplanations}\n                onChange={(e) => setOptions(prev => ({ ...prev, includeExplanations: e.target.checked }))}\n                className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n              />\n              <span className=\"text-gray-900 dark:text-white\">Include explanations</span>\n            </label>\n          </div>\n        </div>\n      </div>\n\n      {error && (\n        <div className=\"mb-6 p-4 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800\">\n          <p className=\"text-sm text-red-800 dark:text-red-200\">{error}</p>\n        </div>\n      )}\n\n      <div className=\"flex justify-center\">\n        <button\n          onClick={handleGenerate}\n          disabled={isGenerating || options.questionTypes.length === 0 || options.difficulty.length === 0}\n          className=\"flex items-center space-x-2 px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n        >\n          {isGenerating ? (\n            <>\n              <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-white\"></div>\n              <span>Generating Quiz...</span>\n            </>\n          ) : (\n            <>\n              <Zap className=\"h-5 w-5\" />\n              <span>Generate Quiz</span>\n            </>\n          )}\n        </button>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAYO,SAAS,cAAc,EAAE,QAAQ,EAAE,eAAe,EAAE,MAAM,EAAsB;IACrF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;QAC5D,eAAe;YAAC;SAAM;QACtB,YAAY;YAAC;SAAS;QACtB,kBAAkB;QAClB,qBAAqB;IACvB;IAEA,MAAM,iBAAiB;QACrB,gBAAgB;QAChB,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ,SAAS,MAAM;oBACvB;oBACA,cAAc,SAAS,QAAQ;oBAC/B,UAAU,SAAS,QAAQ,EAAE;gBAC/B;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,IAAI,eAAe;gBACnB,IAAI;oBACF,MAAM,YAAY,MAAM,SAAS,IAAI;oBACrC,eAAe,UAAU,KAAK,IAAI;gBACpC,EAAE,OAAO,YAAY;oBACnB,MAAM,YAAY,MAAM,SAAS,IAAI;oBACrC,QAAQ,KAAK,CAAC,0BAA0B;oBACxC,eAAe,CAAC,cAAc,EAAE,SAAS,MAAM,CAAC,GAAG,EAAE,SAAS,UAAU,EAAE;gBAC5E;gBACA,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI;YACJ,IAAI;gBACF,SAAS,MAAM,SAAS,IAAI;YAC9B,EAAE,OAAO,YAAY;gBACnB,QAAQ,KAAK,CAAC,qCAAqC;gBACnD,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;gBACjC,gBAAgB,OAAO,IAAI;YAC7B,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QAEF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,0BAA0B;YACxC,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,2BAA2B,CAAC,MAAoB;QACpD,WAAW,CAAA,OAAQ,CAAC;gBAClB,GAAG,IAAI;gBACP,eAAe,UACX;uBAAI,KAAK,aAAa;oBAAE;iBAAK,GAC7B,KAAK,aAAa,CAAC,MAAM,CAAC,CAAA,IAAK,MAAM;YAC3C,CAAC;IACH;IAEA,MAAM,yBAAyB,CAAC,YAA6B;QAC3D,WAAW,CAAA,OAAQ,CAAC;gBAClB,GAAG,IAAI;gBACP,YAAY,UACR;uBAAI,KAAK,UAAU;oBAAE;iBAAW,GAChC,KAAK,UAAU,CAAC,MAAM,CAAC,CAAA,IAAK,MAAM;YACxC,CAAC;IACH;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,SAAS;oBACT,WAAU;;sCAEV,8OAAC,gNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;sCACrB,8OAAC;sCAAK;;;;;;;;;;;;;;;;;0BAIV,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA4D;;;;;;kCAG1E,8OAAC;wBAAE,WAAU;kCAAmC;;;;;;;;;;;;0BAMlD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,8MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAgD,SAAS,QAAQ;;;;;;8CAC/E,8OAAC;oCAAE,WAAU;;wCACV,SAAS,MAAM,CAAC,MAAM;wCAAC;wCAAgB,SAAS,UAAU;wCAAC;wCAC3D,SAAS,QAAQ,EAAE,0BAClB,8OAAC;4CAAK,WAAU;;gDAA8D;gDACjE,SAAS,QAAQ,CAAC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQjD,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;wCAAG,WAAU;kDAAoD;;;;;;;;;;;;0CAGpE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;;0DACf,8OAAC;gDACC,MAAK;gDACL,SAAS,QAAQ,aAAa,CAAC,QAAQ,CAAC;gDACxC,UAAU,CAAC,IAAM,yBAAyB,OAAO,EAAE,MAAM,CAAC,OAAO;gDACjE,WAAU;;;;;;0DAEZ,8OAAC;;kEACC,8OAAC;wDAAK,WAAU;kEAA4C;;;;;;kEAC5D,8OAAC;wDAAE,WAAU;kEAA2C;;;;;;;;;;;;;;;;;;kDAI5D,8OAAC;wCAAM,WAAU;;0DACf,8OAAC;gDACC,MAAK;gDACL,SAAS,QAAQ,aAAa,CAAC,QAAQ,CAAC;gDACxC,UAAU,CAAC,IAAM,yBAAyB,cAAc,EAAE,MAAM,CAAC,OAAO;gDACxE,WAAU;;;;;;0DAEZ,8OAAC;;kEACC,8OAAC;wDAAK,WAAU;kEAA4C;;;;;;kEAC5D,8OAAC;wDAAE,WAAU;kEAA2C;;;;;;;;;;;;;;;;;;kDAI5D,8OAAC;wCAAM,WAAU;;0DACf,8OAAC;gDACC,MAAK;gDACL,SAAS,QAAQ,aAAa,CAAC,QAAQ,CAAC;gDACxC,UAAU,CAAC,IAAM,yBAAyB,cAAc,EAAE,MAAM,CAAC,OAAO;gDACxE,WAAU;;;;;;0DAEZ,8OAAC;;kEACC,8OAAC;wDAAK,WAAU;kEAA4C;;;;;;kEAC5D,8OAAC;wDAAE,WAAU;kEAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOhE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;wCAAG,WAAU;kDAAoD;;;;;;;;;;;;0CAGpE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAkE;;;;;;0DAGnF,8OAAC;gDAAI,WAAU;0DACZ,AAAC;oDAAC;oDAAQ;oDAAU;iDAAO,CAAuB,GAAG,CAAC,CAAC,sBACtD,8OAAC;wDAAkB,WAAU;;0EAC3B,8OAAC;gEACC,MAAK;gEACL,SAAS,QAAQ,UAAU,CAAC,QAAQ,CAAC;gEACrC,UAAU,CAAC,IAAM,uBAAuB,OAAO,EAAE,MAAM,CAAC,OAAO;gEAC/D,WAAU;;;;;;0EAEZ,8OAAC;gEAAK,WAAU;0EAA4C;;;;;;;uDAPlD;;;;;;;;;;;;;;;;kDAalB,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;;oDAAkE;oDAC5D,QAAQ,gBAAgB;;;;;;;0DAE/C,8OAAC;gDACC,MAAK;gDACL,KAAI;gDACJ,KAAI;gDACJ,OAAO,QAAQ,gBAAgB;gDAC/B,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;wDAAE,CAAC;gDAC5F,WAAU;;;;;;0DAEZ,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;kEAAK;;;;;;kEACN,8OAAC;kEAAK;;;;;;;;;;;;;;;;;;kDAIV,8OAAC;wCAAM,WAAU;;0DACf,8OAAC;gDACC,MAAK;gDACL,SAAS,QAAQ,mBAAmB;gDACpC,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,qBAAqB,EAAE,MAAM,CAAC,OAAO;wDAAC,CAAC;gDACvF,WAAU;;;;;;0DAEZ,8OAAC;gDAAK,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAMvD,uBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BAA0C;;;;;;;;;;;0BAI3D,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,SAAS;oBACT,UAAU,gBAAgB,QAAQ,aAAa,CAAC,MAAM,KAAK,KAAK,QAAQ,UAAU,CAAC,MAAM,KAAK;oBAC9F,WAAU;8BAET,6BACC;;0CACE,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;0CAAK;;;;;;;qDAGR;;0CACE,8OAAC,gMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;0CACf,8OAAC;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;AAOpB", "debugId": null}}, {"offset": {"line": 1211, "column": 0}, "map": {"version": 3, "sources": ["file:///Z:/projectx/pdf-quiz-generator/src/components/QuizDisplay.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { ArrowLeft, Download, RotateCcw, CheckCircle, XCircle, HelpCircle } from 'lucide-react';\nimport { Quiz, Question, MCQQuestion, TrueFalseQuestion, FillBlankQuestion } from '@/types/quiz';\nimport { downloadAsJSON, downloadAsCSV } from '@/lib/utils';\n\ninterface QuizDisplayProps {\n  quiz: Quiz;\n  onReset: () => void;\n  onBack: () => void;\n}\n\ninterface UserAnswer {\n  questionId: string;\n  answer: string | boolean;\n  isCorrect?: boolean;\n}\n\nexport function QuizDisplay({ quiz, onReset, onBack }: QuizDisplayProps) {\n  const [userAnswers, setUserAnswers] = useState<UserAnswer[]>([]);\n  const [showResults, setShowResults] = useState(false);\n  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);\n\n  const handleAnswer = (questionId: string, answer: string | boolean) => {\n    setUserAnswers(prev => {\n      const existing = prev.find(a => a.questionId === questionId);\n      if (existing) {\n        return prev.map(a => a.questionId === questionId ? { ...a, answer } : a);\n      }\n      return [...prev, { questionId, answer }];\n    });\n  };\n\n  const checkAnswers = () => {\n    const checkedAnswers = userAnswers.map(userAnswer => {\n      const question = quiz.questions.find(q => q.id === userAnswer.questionId);\n      if (!question) return userAnswer;\n\n      let isCorrect = false;\n      \n      switch (question.type) {\n        case 'mcq':\n          const mcqQuestion = question as MCQQuestion;\n          const correctOption = mcqQuestion.options.find(opt => opt.isCorrect);\n          isCorrect = correctOption?.id === userAnswer.answer;\n          break;\n        case 'true-false':\n          const tfQuestion = question as TrueFalseQuestion;\n          isCorrect = tfQuestion.correctAnswer === userAnswer.answer;\n          break;\n        case 'fill-blank':\n          const fbQuestion = question as FillBlankQuestion;\n          const userAnswerStr = (userAnswer.answer as string).toLowerCase().trim();\n          isCorrect = fbQuestion.correctAnswer.toLowerCase() === userAnswerStr ||\n                     (fbQuestion.acceptableAnswers || []).some(acc => acc.toLowerCase() === userAnswerStr);\n          break;\n      }\n\n      return { ...userAnswer, isCorrect };\n    });\n\n    setUserAnswers(checkedAnswers);\n    setShowResults(true);\n  };\n\n  const exportQuiz = (format: 'json' | 'csv') => {\n    if (format === 'json') {\n      downloadAsJSON(quiz, `${quiz.title.replace(/[^a-zA-Z0-9]/g, '_')}.json`);\n    } else {\n      const csvData = quiz.questions.map(q => ({\n        type: q.type,\n        question: q.question,\n        difficulty: q.difficulty,\n        correctAnswer: getCorrectAnswerText(q),\n        explanation: q.explanation || '',\n      }));\n      downloadAsCSV(csvData, `${quiz.title.replace(/[^a-zA-Z0-9]/g, '_')}.csv`);\n    }\n  };\n\n  const getCorrectAnswerText = (question: Question): string => {\n    switch (question.type) {\n      case 'mcq':\n        const mcqQ = question as MCQQuestion;\n        return mcqQ.options.find(opt => opt.isCorrect)?.text || '';\n      case 'true-false':\n        const tfQ = question as TrueFalseQuestion;\n        return tfQ.correctAnswer.toString();\n      case 'fill-blank':\n        const fbQ = question as FillBlankQuestion;\n        return fbQ.correctAnswer;\n      default:\n        return '';\n    }\n  };\n\n  const getUserAnswer = (questionId: string) => {\n    return userAnswers.find(a => a.questionId === questionId);\n  };\n\n  const getScore = () => {\n    const correct = userAnswers.filter(a => a.isCorrect).length;\n    const total = quiz.questions.length;\n    return { correct, total, percentage: Math.round((correct / total) * 100) };\n  };\n\n  const renderQuestion = (question: Question, index: number) => {\n    const userAnswer = getUserAnswer(question.id);\n    \n    return (\n      <div key={question.id} className=\"bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 mb-6\">\n        <div className=\"flex items-start justify-between mb-4\">\n          <div className=\"flex-1\">\n            <div className=\"flex items-center space-x-2 mb-2\">\n              <span className=\"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded text-sm font-medium\">\n                Question {index + 1}\n              </span>\n              <span className=\"bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-2 py-1 rounded text-sm\">\n                {question.type.replace('-', ' ').toUpperCase()}\n              </span>\n              <span className={`px-2 py-1 rounded text-sm font-medium ${\n                question.difficulty === 'easy' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :\n                question.difficulty === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :\n                'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'\n              }`}>\n                {question.difficulty}\n              </span>\n            </div>\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">\n              {question.question}\n            </h3>\n          </div>\n          {showResults && userAnswer && (\n            <div className=\"ml-4\">\n              {userAnswer.isCorrect ? (\n                <CheckCircle className=\"h-6 w-6 text-green-600\" />\n              ) : (\n                <XCircle className=\"h-6 w-6 text-red-600\" />\n              )}\n            </div>\n          )}\n        </div>\n\n        {question.type === 'mcq' && (\n          <div className=\"space-y-3\">\n            {(question as MCQQuestion).options.map((option) => (\n              <label key={option.id} className={`flex items-center space-x-3 p-3 rounded-lg border cursor-pointer transition-colors ${\n                showResults \n                  ? option.isCorrect \n                    ? 'border-green-500 bg-green-50 dark:bg-green-900/20'\n                    : userAnswer?.answer === option.id && !option.isCorrect\n                    ? 'border-red-500 bg-red-50 dark:bg-red-900/20'\n                    : 'border-gray-200 dark:border-gray-600'\n                  : 'border-gray-200 dark:border-gray-600 hover:border-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900/20'\n              }`}>\n                <input\n                  type=\"radio\"\n                  name={`question-${question.id}`}\n                  value={option.id}\n                  checked={userAnswer?.answer === option.id}\n                  onChange={() => !showResults && handleAnswer(question.id, option.id)}\n                  disabled={showResults}\n                  className=\"text-blue-600 focus:ring-blue-500\"\n                />\n                <span className=\"text-gray-900 dark:text-white\">{option.text}</span>\n              </label>\n            ))}\n          </div>\n        )}\n\n        {question.type === 'true-false' && (\n          <div className=\"space-y-3\">\n            {[true, false].map((value) => (\n              <label key={value.toString()} className={`flex items-center space-x-3 p-3 rounded-lg border cursor-pointer transition-colors ${\n                showResults \n                  ? (question as TrueFalseQuestion).correctAnswer === value\n                    ? 'border-green-500 bg-green-50 dark:bg-green-900/20'\n                    : userAnswer?.answer === value && (question as TrueFalseQuestion).correctAnswer !== value\n                    ? 'border-red-500 bg-red-50 dark:bg-red-900/20'\n                    : 'border-gray-200 dark:border-gray-600'\n                  : 'border-gray-200 dark:border-gray-600 hover:border-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900/20'\n              }`}>\n                <input\n                  type=\"radio\"\n                  name={`question-${question.id}`}\n                  value={value.toString()}\n                  checked={userAnswer?.answer === value}\n                  onChange={() => !showResults && handleAnswer(question.id, value)}\n                  disabled={showResults}\n                  className=\"text-blue-600 focus:ring-blue-500\"\n                />\n                <span className=\"text-gray-900 dark:text-white\">{value ? 'True' : 'False'}</span>\n              </label>\n            ))}\n          </div>\n        )}\n\n        {question.type === 'fill-blank' && (\n          <div>\n            <input\n              type=\"text\"\n              value={userAnswer?.answer as string || ''}\n              onChange={(e) => !showResults && handleAnswer(question.id, e.target.value)}\n              disabled={showResults}\n              placeholder=\"Enter your answer...\"\n              className={`w-full p-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${\n                showResults \n                  ? userAnswer?.isCorrect\n                    ? 'border-green-500 bg-green-50 dark:bg-green-900/20'\n                    : 'border-red-500 bg-red-50 dark:bg-red-900/20'\n                  : 'border-gray-300 dark:border-gray-600'\n              } dark:bg-gray-700 dark:text-white`}\n            />\n            {showResults && (\n              <p className=\"mt-2 text-sm text-gray-600 dark:text-gray-300\">\n                Correct answer: <span className=\"font-medium\">{(question as FillBlankQuestion).correctAnswer}</span>\n              </p>\n            )}\n          </div>\n        )}\n\n        {showResults && question.explanation && (\n          <div className=\"mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800\">\n            <div className=\"flex items-start space-x-2\">\n              <HelpCircle className=\"h-5 w-5 text-blue-600 mt-0.5\" />\n              <div>\n                <p className=\"text-sm font-medium text-blue-900 dark:text-blue-100\">Explanation</p>\n                <p className=\"text-sm text-blue-800 dark:text-blue-200\">{question.explanation}</p>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    );\n  };\n\n  const score = showResults ? getScore() : null;\n\n  return (\n    <div className=\"w-full max-w-4xl mx-auto\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <button\n          onClick={onBack}\n          className=\"flex items-center space-x-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white\"\n        >\n          <ArrowLeft className=\"h-5 w-5\" />\n          <span>Back to Generator</span>\n        </button>\n        \n        <div className=\"flex items-center space-x-2\">\n          <button\n            onClick={() => exportQuiz('json')}\n            className=\"flex items-center space-x-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\"\n          >\n            <Download className=\"h-4 w-4\" />\n            <span>JSON</span>\n          </button>\n          <button\n            onClick={() => exportQuiz('csv')}\n            className=\"flex items-center space-x-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\"\n          >\n            <Download className=\"h-4 w-4\" />\n            <span>CSV</span>\n          </button>\n          <button\n            onClick={onReset}\n            className=\"flex items-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors\"\n          >\n            <RotateCcw className=\"h-4 w-4\" />\n            <span>New Quiz</span>\n          </button>\n        </div>\n      </div>\n\n      <div className=\"mb-6\">\n        <h2 className=\"text-2xl font-semibold text-gray-900 dark:text-white mb-2\">\n          {quiz.title}\n        </h2>\n        <p className=\"text-gray-600 dark:text-gray-300\">\n          {quiz.description}\n        </p>\n        {showResults && score && (\n          <div className=\"mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-lg font-semibold text-blue-900 dark:text-blue-100\">\n                  Score: {score.correct}/{score.total} ({score.percentage}%)\n                </p>\n                <p className=\"text-sm text-blue-700 dark:text-blue-300\">\n                  {score.percentage >= 80 ? 'Excellent work!' : \n                   score.percentage >= 60 ? 'Good job!' : \n                   'Keep studying!'}\n                </p>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n\n      <div className=\"space-y-6\">\n        {quiz.questions.map((question, index) => renderQuestion(question, index))}\n      </div>\n\n      {!showResults && (\n        <div className=\"flex justify-center mt-8\">\n          <button\n            onClick={checkAnswers}\n            disabled={userAnswers.length !== quiz.questions.length}\n            className=\"px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n          >\n            Submit Quiz\n          </button>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AALA;;;;;AAmBO,SAAS,YAAY,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAoB;IACrE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjE,MAAM,eAAe,CAAC,YAAoB;QACxC,eAAe,CAAA;YACb,MAAM,WAAW,KAAK,IAAI,CAAC,CAAA,IAAK,EAAE,UAAU,KAAK;YACjD,IAAI,UAAU;gBACZ,OAAO,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,UAAU,KAAK,aAAa;wBAAE,GAAG,CAAC;wBAAE;oBAAO,IAAI;YACxE;YACA,OAAO;mBAAI;gBAAM;oBAAE;oBAAY;gBAAO;aAAE;QAC1C;IACF;IAEA,MAAM,eAAe;QACnB,MAAM,iBAAiB,YAAY,GAAG,CAAC,CAAA;YACrC,MAAM,WAAW,KAAK,SAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,WAAW,UAAU;YACxE,IAAI,CAAC,UAAU,OAAO;YAEtB,IAAI,YAAY;YAEhB,OAAQ,SAAS,IAAI;gBACnB,KAAK;oBACH,MAAM,cAAc;oBACpB,MAAM,gBAAgB,YAAY,OAAO,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,SAAS;oBACnE,YAAY,eAAe,OAAO,WAAW,MAAM;oBACnD;gBACF,KAAK;oBACH,MAAM,aAAa;oBACnB,YAAY,WAAW,aAAa,KAAK,WAAW,MAAM;oBAC1D;gBACF,KAAK;oBACH,MAAM,aAAa;oBACnB,MAAM,gBAAgB,AAAC,WAAW,MAAM,CAAY,WAAW,GAAG,IAAI;oBACtE,YAAY,WAAW,aAAa,CAAC,WAAW,OAAO,iBAC5C,CAAC,WAAW,iBAAiB,IAAI,EAAE,EAAE,IAAI,CAAC,CAAA,MAAO,IAAI,WAAW,OAAO;oBAClF;YACJ;YAEA,OAAO;gBAAE,GAAG,UAAU;gBAAE;YAAU;QACpC;QAEA,eAAe;QACf,eAAe;IACjB;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,WAAW,QAAQ;YACrB,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,GAAG,KAAK,KAAK,CAAC,OAAO,CAAC,iBAAiB,KAAK,KAAK,CAAC;QACzE,OAAO;YACL,MAAM,UAAU,KAAK,SAAS,CAAC,GAAG,CAAC,CAAA,IAAK,CAAC;oBACvC,MAAM,EAAE,IAAI;oBACZ,UAAU,EAAE,QAAQ;oBACpB,YAAY,EAAE,UAAU;oBACxB,eAAe,qBAAqB;oBACpC,aAAa,EAAE,WAAW,IAAI;gBAChC,CAAC;YACD,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,GAAG,KAAK,KAAK,CAAC,OAAO,CAAC,iBAAiB,KAAK,IAAI,CAAC;QAC1E;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,OAAQ,SAAS,IAAI;YACnB,KAAK;gBACH,MAAM,OAAO;gBACb,OAAO,KAAK,OAAO,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,SAAS,GAAG,QAAQ;YAC1D,KAAK;gBACH,MAAM,MAAM;gBACZ,OAAO,IAAI,aAAa,CAAC,QAAQ;YACnC,KAAK;gBACH,MAAM,MAAM;gBACZ,OAAO,IAAI,aAAa;YAC1B;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAO,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,UAAU,KAAK;IAChD;IAEA,MAAM,WAAW;QACf,MAAM,UAAU,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,EAAE,MAAM;QAC3D,MAAM,QAAQ,KAAK,SAAS,CAAC,MAAM;QACnC,OAAO;YAAE;YAAS;YAAO,YAAY,KAAK,KAAK,CAAC,AAAC,UAAU,QAAS;QAAK;IAC3E;IAEA,MAAM,iBAAiB,CAAC,UAAoB;QAC1C,MAAM,aAAa,cAAc,SAAS,EAAE;QAE5C,qBACE,8OAAC;YAAsB,WAAU;;8BAC/B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;;gDAAsG;gDAC1G,QAAQ;;;;;;;sDAEpB,8OAAC;4CAAK,WAAU;sDACb,SAAS,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK,WAAW;;;;;;sDAE9C,8OAAC;4CAAK,WAAW,CAAC,sCAAsC,EACtD,SAAS,UAAU,KAAK,SAAS,sEACjC,SAAS,UAAU,KAAK,WAAW,0EACnC,6DACA;sDACC,SAAS,UAAU;;;;;;;;;;;;8CAGxB,8OAAC;oCAAG,WAAU;8CACX,SAAS,QAAQ;;;;;;;;;;;;wBAGrB,eAAe,4BACd,8OAAC;4BAAI,WAAU;sCACZ,WAAW,SAAS,iBACnB,8OAAC,2NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;qDAEvB,8OAAC,4MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;;;;;;;;;;;;gBAM1B,SAAS,IAAI,KAAK,uBACjB,8OAAC;oBAAI,WAAU;8BACZ,AAAC,SAAyB,OAAO,CAAC,GAAG,CAAC,CAAC,uBACtC,8OAAC;4BAAsB,WAAW,CAAC,mFAAmF,EACpH,cACI,OAAO,SAAS,GACd,sDACA,YAAY,WAAW,OAAO,EAAE,IAAI,CAAC,OAAO,SAAS,GACrD,gDACA,yCACF,yGACJ;;8CACA,8OAAC;oCACC,MAAK;oCACL,MAAM,CAAC,SAAS,EAAE,SAAS,EAAE,EAAE;oCAC/B,OAAO,OAAO,EAAE;oCAChB,SAAS,YAAY,WAAW,OAAO,EAAE;oCACzC,UAAU,IAAM,CAAC,eAAe,aAAa,SAAS,EAAE,EAAE,OAAO,EAAE;oCACnE,UAAU;oCACV,WAAU;;;;;;8CAEZ,8OAAC;oCAAK,WAAU;8CAAiC,OAAO,IAAI;;;;;;;2BAlBlD,OAAO,EAAE;;;;;;;;;;gBAwB1B,SAAS,IAAI,KAAK,8BACjB,8OAAC;oBAAI,WAAU;8BACZ;wBAAC;wBAAM;qBAAM,CAAC,GAAG,CAAC,CAAC,sBAClB,8OAAC;4BAA6B,WAAW,CAAC,mFAAmF,EAC3H,cACI,AAAC,SAA+B,aAAa,KAAK,QAChD,sDACA,YAAY,WAAW,SAAS,AAAC,SAA+B,aAAa,KAAK,QAClF,gDACA,yCACF,yGACJ;;8CACA,8OAAC;oCACC,MAAK;oCACL,MAAM,CAAC,SAAS,EAAE,SAAS,EAAE,EAAE;oCAC/B,OAAO,MAAM,QAAQ;oCACrB,SAAS,YAAY,WAAW;oCAChC,UAAU,IAAM,CAAC,eAAe,aAAa,SAAS,EAAE,EAAE;oCAC1D,UAAU;oCACV,WAAU;;;;;;8CAEZ,8OAAC;oCAAK,WAAU;8CAAiC,QAAQ,SAAS;;;;;;;2BAlBxD,MAAM,QAAQ;;;;;;;;;;gBAwB/B,SAAS,IAAI,KAAK,8BACjB,8OAAC;;sCACC,8OAAC;4BACC,MAAK;4BACL,OAAO,YAAY,UAAoB;4BACvC,UAAU,CAAC,IAAM,CAAC,eAAe,aAAa,SAAS,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;4BACzE,UAAU;4BACV,aAAY;4BACZ,WAAW,CAAC,oFAAoF,EAC9F,cACI,YAAY,YACV,sDACA,gDACF,uCACL,iCAAiC,CAAC;;;;;;wBAEpC,6BACC,8OAAC;4BAAE,WAAU;;gCAAgD;8CAC3C,8OAAC;oCAAK,WAAU;8CAAe,AAAC,SAA+B,aAAa;;;;;;;;;;;;;;;;;;gBAMnG,eAAe,SAAS,WAAW,kBAClC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,8NAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;0CACtB,8OAAC;;kDACC,8OAAC;wCAAE,WAAU;kDAAuD;;;;;;kDACpE,8OAAC;wCAAE,WAAU;kDAA4C,SAAS,WAAW;;;;;;;;;;;;;;;;;;;;;;;;WArH7E,SAAS,EAAE;;;;;IA4HzB;IAEA,MAAM,QAAQ,cAAc,aAAa;IAEzC,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,SAAS;wBACT,WAAU;;0CAEV,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;0CACrB,8OAAC;0CAAK;;;;;;;;;;;;kCAGR,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,WAAW;gCAC1B,WAAU;;kDAEV,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC;gCACC,SAAS,IAAM,WAAW;gCAC1B,WAAU;;kDAEV,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;0BAKZ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCACX,KAAK,KAAK;;;;;;kCAEb,8OAAC;wBAAE,WAAU;kCACV,KAAK,WAAW;;;;;;oBAElB,eAAe,uBACd,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;;kDACC,8OAAC;wCAAE,WAAU;;4CAAyD;4CAC5D,MAAM,OAAO;4CAAC;4CAAE,MAAM,KAAK;4CAAC;4CAAG,MAAM,UAAU;4CAAC;;;;;;;kDAE1D,8OAAC;wCAAE,WAAU;kDACV,MAAM,UAAU,IAAI,KAAK,oBACzB,MAAM,UAAU,IAAI,KAAK,cACzB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQb,8OAAC;gBAAI,WAAU;0BACZ,KAAK,SAAS,CAAC,GAAG,CAAC,CAAC,UAAU,QAAU,eAAe,UAAU;;;;;;YAGnE,CAAC,6BACA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,SAAS;oBACT,UAAU,YAAY,MAAM,KAAK,KAAK,SAAS,CAAC,MAAM;oBACtD,WAAU;8BACX;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}, {"offset": {"line": 1803, "column": 0}, "map": {"version": 3, "sources": ["file:///Z:/projectx/pdf-quiz-generator/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { FileUpload } from '@/components/FileUpload';\nimport { QuizGenerator } from '@/components/QuizGenerator';\nimport { QuizDisplay } from '@/components/QuizDisplay';\nimport { ProcessedDocument, Quiz } from '@/types/quiz';\n\nexport default function Home() {\n  const [processedDocument, setProcessedDocument] = useState<ProcessedDocument | null>(null);\n  const [generatedQuiz, setGeneratedQuiz] = useState<Quiz | null>(null);\n  const [currentStep, setCurrentStep] = useState<'upload' | 'generate' | 'display'>('upload');\n\n  const handleDocumentProcessed = (document: ProcessedDocument) => {\n    setProcessedDocument(document);\n    setCurrentStep('generate');\n  };\n\n  const handleQuizGenerated = (quiz: Quiz) => {\n    setGeneratedQuiz(quiz);\n    setCurrentStep('display');\n  };\n\n  const handleReset = () => {\n    setProcessedDocument(null);\n    setGeneratedQuiz(null);\n    setCurrentStep('upload');\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800\">\n      <div className=\"container mx-auto px-4 py-8\">\n        <header className=\"text-center mb-8\">\n          <h1 className=\"text-4xl font-bold text-gray-900 dark:text-white mb-2\">\n            PDF Quiz Generator\n          </h1>\n          <p className=\"text-lg text-gray-600 dark:text-gray-300\">\n            Upload large PDFs and generate intelligent quizzes using AI\n          </p>\n          <div className=\"mt-2\">\n            <span className=\"inline-block bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full\">\n              v2.2.0 - FIXED: Real PDF Text Extraction with pdfjs-dist\n            </span>\n          </div>\n        </header>\n\n        <div className=\"max-w-4xl mx-auto\">\n          {/* Progress Steps */}\n          <div className=\"flex items-center justify-center mb-8\">\n            <div className=\"flex items-center space-x-4\">\n              <div className={`flex items-center ${currentStep === 'upload' ? 'text-blue-600' : currentStep === 'generate' || currentStep === 'display' ? 'text-green-600' : 'text-gray-400'}`}>\n                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${currentStep === 'upload' ? 'bg-blue-600 text-white' : currentStep === 'generate' || currentStep === 'display' ? 'bg-green-600 text-white' : 'bg-gray-300'}`}>\n                  1\n                </div>\n                <span className=\"ml-2 font-medium\">Upload PDF</span>\n              </div>\n              <div className=\"w-8 h-0.5 bg-gray-300\"></div>\n              <div className={`flex items-center ${currentStep === 'generate' ? 'text-blue-600' : currentStep === 'display' ? 'text-green-600' : 'text-gray-400'}`}>\n                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${currentStep === 'generate' ? 'bg-blue-600 text-white' : currentStep === 'display' ? 'bg-green-600 text-white' : 'bg-gray-300'}`}>\n                  2\n                </div>\n                <span className=\"ml-2 font-medium\">Generate Quiz</span>\n              </div>\n              <div className=\"w-8 h-0.5 bg-gray-300\"></div>\n              <div className={`flex items-center ${currentStep === 'display' ? 'text-blue-600' : 'text-gray-400'}`}>\n                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${currentStep === 'display' ? 'bg-blue-600 text-white' : 'bg-gray-300'}`}>\n                  3\n                </div>\n                <span className=\"ml-2 font-medium\">View Quiz</span>\n              </div>\n            </div>\n          </div>\n\n          {/* Main Content */}\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n            {currentStep === 'upload' && (\n              <FileUpload onDocumentProcessed={handleDocumentProcessed} />\n            )}\n\n            {currentStep === 'generate' && processedDocument && (\n              <QuizGenerator\n                document={processedDocument}\n                onQuizGenerated={handleQuizGenerated}\n                onBack={() => setCurrentStep('upload')}\n              />\n            )}\n\n            {currentStep === 'display' && generatedQuiz && (\n              <QuizDisplay\n                quiz={generatedQuiz}\n                onReset={handleReset}\n                onBack={() => setCurrentStep('generate')}\n              />\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAQe,SAAS;IACtB,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4B;IACrF,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAChE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqC;IAElF,MAAM,0BAA0B,CAAC;QAC/B,qBAAqB;QACrB,eAAe;IACjB;IAEA,MAAM,sBAAsB,CAAC;QAC3B,iBAAiB;QACjB,eAAe;IACjB;IAEA,MAAM,cAAc;QAClB,qBAAqB;QACrB,iBAAiB;QACjB,eAAe;IACjB;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAO,WAAU;;sCAChB,8OAAC;4BAAG,WAAU;sCAAwD;;;;;;sCAGtE,8OAAC;4BAAE,WAAU;sCAA2C;;;;;;sCAGxD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAU;0CAA0E;;;;;;;;;;;;;;;;;8BAM9F,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAW,CAAC,kBAAkB,EAAE,gBAAgB,WAAW,kBAAkB,gBAAgB,cAAc,gBAAgB,YAAY,mBAAmB,iBAAiB;;0DAC9K,8OAAC;gDAAI,WAAW,CAAC,sDAAsD,EAAE,gBAAgB,WAAW,2BAA2B,gBAAgB,cAAc,gBAAgB,YAAY,4BAA4B,eAAe;0DAAE;;;;;;0DAGtO,8OAAC;gDAAK,WAAU;0DAAmB;;;;;;;;;;;;kDAErC,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAW,CAAC,kBAAkB,EAAE,gBAAgB,aAAa,kBAAkB,gBAAgB,YAAY,mBAAmB,iBAAiB;;0DAClJ,8OAAC;gDAAI,WAAW,CAAC,sDAAsD,EAAE,gBAAgB,aAAa,2BAA2B,gBAAgB,YAAY,4BAA4B,eAAe;0DAAE;;;;;;0DAG1M,8OAAC;gDAAK,WAAU;0DAAmB;;;;;;;;;;;;kDAErC,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAW,CAAC,kBAAkB,EAAE,gBAAgB,YAAY,kBAAkB,iBAAiB;;0DAClG,8OAAC;gDAAI,WAAW,CAAC,sDAAsD,EAAE,gBAAgB,YAAY,2BAA2B,eAAe;0DAAE;;;;;;0DAGjJ,8OAAC;gDAAK,WAAU;0DAAmB;;;;;;;;;;;;;;;;;;;;;;;sCAMzC,8OAAC;4BAAI,WAAU;;gCACZ,gBAAgB,0BACf,8OAAC,gIAAA,CAAA,aAAU;oCAAC,qBAAqB;;;;;;gCAGlC,gBAAgB,cAAc,mCAC7B,8OAAC,mIAAA,CAAA,gBAAa;oCACZ,UAAU;oCACV,iBAAiB;oCACjB,QAAQ,IAAM,eAAe;;;;;;gCAIhC,gBAAgB,aAAa,+BAC5B,8OAAC,iIAAA,CAAA,cAAW;oCACV,MAAM;oCACN,SAAS;oCACT,QAAQ,IAAM,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ7C", "debugId": null}}]}