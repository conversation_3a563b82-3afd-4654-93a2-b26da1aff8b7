# PDF Quiz Generator

A powerful AI-powered application that generates intelligent quiz questions from PDF documents using DeepSeek's language models.

## Features

- **Large PDF Support**: Handles PDFs up to 50MB with 300+ pages
- **Intelligent Chunking**: Automatically splits documents into semantic chunks
- **AI-Powered Questions**: Generates multiple question types using DeepSeek API:
  - Multiple Choice Questions (MCQs)
  - True/False Questions
  - Fill-in-the-blank Questions
- **Difficulty Levels**: Easy, Medium, and Hard questions
- **Semantic Understanding**: Uses embeddings for better content comprehension
- **Export Options**: Export quizzes as JSON or CSV
- **Interactive UI**: Modern, responsive interface with progress tracking

## Quick Start

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Start the development server:**
   ```bash
   npm run dev
   ```

3. **Open your browser:**
   Navigate to [http://localhost:3000](http://localhost:3000)

## Usage

### Step 1: Upload PDF
- Drag and drop a PDF file or click to browse
- Supports PDFs up to 50MB
- Works best with text-rich documents

### Step 2: Configure Quiz Generation
- Select question types (MCQ, True/False, Fill-in-the-blank)
- Choose difficulty levels (Easy, Medium, Hard)
- Set number of questions per type (1-10)

### Step 3: View and Take Quiz
- Interactive quiz interface with real-time feedback
- Score calculation and explanations
- Export options (JSON/CSV)

## Tech Stack

- **Frontend**: Next.js 15, React, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes
- **AI/ML**: DeepSeek API for LLM and embeddings
- **PDF Processing**: pdf-parse library

## API Configuration

The DeepSeek API key is already configured in the `.env.local` file. The application uses:
- `deepseek-embed` model for text embeddings
- `deepseek-chat` model for question generation

## Project Structure

```
pdf-quiz-generator/
├── src/
│   ├── app/
│   │   ├── api/           # API routes
│   │   └── page.tsx       # Main page
│   ├── components/        # React components
│   ├── lib/              # Utility functions
│   └── types/            # TypeScript types
└── package.json
```

## Troubleshooting

- **PDF Processing Failed**: Ensure the PDF contains extractable text
- **Generation Issues**: Try reducing the number of questions for large documents
- **Upload Errors**: Check file size (max 50MB) and format (PDF only)

---

Ready to generate intelligent quizzes from your PDF documents! 🚀
