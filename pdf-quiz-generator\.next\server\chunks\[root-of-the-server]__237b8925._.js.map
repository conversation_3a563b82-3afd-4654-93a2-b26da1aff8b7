{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Z:/projectx/pdf-quiz-generator/src/lib/deepseek.ts"], "sourcesContent": ["import OpenAI from 'openai';\nimport { Question, QuestionType, DifficultyLevel, DocumentChunk } from '@/types/quiz';\n\nconst deepseek = new OpenAI({\n  apiKey: process.env.DEEPSEEK_API_KEY,\n  baseURL: process.env.DEEPSEEK_BASE_URL || 'https://api.deepseek.com',\n});\n\n// Note: DeepSeek doesn't provide embedding models, so we'll skip embeddings\n// and focus on direct question generation from text chunks\nexport function generateEmbedding(text: string): number[] {\n  // Return a simple hash-based pseudo-embedding for compatibility\n  // This is just for maintaining the interface, not for semantic similarity\n  const hash = text.split('').reduce((a, b) => {\n    a = ((a << 5) - a) + b.charCodeAt(0);\n    return a & a;\n  }, 0);\n\n  // Generate a simple 384-dimensional vector based on text characteristics\n  const embedding = Array.from({ length: 384 }, (_, i) => {\n    return Math.sin(hash * (i + 1) * 0.01) * 0.1;\n  });\n\n  return embedding;\n}\n\nexport async function generateQuestions(\n  chunks: DocumentChunk[],\n  questionTypes: QuestionType[],\n  difficulty: DifficultyLevel[],\n  questionsPerType: number,\n  language?: string\n): Promise<Question[]> {\n  const questions: Question[] = [];\n\n  // Limit total questions to prevent long generation times\n  const maxTotalQuestions = 10;\n  const totalRequestedQuestions = questionTypes.length * difficulty.length * questionsPerType;\n\n  if (totalRequestedQuestions > maxTotalQuestions) {\n    console.warn(`Requested ${totalRequestedQuestions} questions, limiting to ${maxTotalQuestions} for performance`);\n  }\n\n  // Generate questions more efficiently - batch by type\n  for (const questionType of questionTypes) {\n    for (const diff of difficulty) {\n      const adjustedCount = Math.min(questionsPerType, Math.ceil(maxTotalQuestions / (questionTypes.length * difficulty.length)));\n      const typeQuestions = await generateQuestionsForType(\n        chunks,\n        questionType,\n        diff,\n        adjustedCount,\n        language\n      );\n      questions.push(...typeQuestions);\n\n      // Stop if we've reached the limit\n      if (questions.length >= maxTotalQuestions) {\n        break;\n      }\n    }\n    if (questions.length >= maxTotalQuestions) {\n      break;\n    }\n  }\n\n  return questions.slice(0, maxTotalQuestions);\n}\n\nasync function generateQuestionsForType(\n  chunks: DocumentChunk[],\n  questionType: QuestionType,\n  difficulty: DifficultyLevel,\n  count: number,\n  language?: string\n): Promise<Question[]> {\n  const selectedChunks = selectRelevantChunks(chunks, count);\n  const questions: Question[] = [];\n  \n  for (let i = 0; i < Math.min(count, selectedChunks.length); i++) {\n    const chunk = selectedChunks[i];\n    \n    try {\n      const question = await generateSingleQuestion(chunk, questionType, difficulty, language);\n      if (question) {\n        questions.push(question);\n      }\n    } catch (error) {\n      console.error(`Error generating question for chunk ${chunk.id}:`, error);\n    }\n  }\n  \n  return questions;\n}\n\nasync function generateSingleQuestion(\n  chunk: DocumentChunk,\n  questionType: QuestionType,\n  difficulty: DifficultyLevel,\n  language?: string\n): Promise<Question | null> {\n  const prompt = createPrompt(chunk.content, questionType, difficulty, language);\n\n  try {\n    const systemMessage = language === 'arabic'\n      ? 'أنت خبير في إنشاء الاختبارات. قم بإنشاء أسئلة فقط من النص المقدم. مهم: استجب فقط بتنسيق JSON صالح. لا تتضمن كتل كود markdown أو نص توضيحي أو أي تنسيق آخر. أرجع فقط كائن JSON الخام.'\n      : 'You are an expert quiz generator. Create questions ONLY from the provided text content. IMPORTANT: Respond ONLY with valid JSON format. Do not include markdown code blocks, explanatory text, or any other formatting. Return only the raw JSON object.';\n\n    const response = await deepseek.chat.completions.create({\n      model: 'deepseek-chat',\n      messages: [\n        {\n          role: 'system',\n          content: systemMessage\n        },\n        {\n          role: 'user',\n          content: prompt\n        }\n      ],\n      temperature: 0.3, // Lower temperature for more focused responses\n      max_tokens: 800,  // Increased for Arabic text which may need more tokens\n    });\n    \n    const content = response.choices[0]?.message?.content;\n    if (!content) return null;\n\n    // Clean the response - remove markdown code blocks if present\n    let cleanContent = content.trim();\n    if (cleanContent.startsWith('```json')) {\n      cleanContent = cleanContent.replace(/^```json\\s*/, '').replace(/\\s*```$/, '');\n    } else if (cleanContent.startsWith('```')) {\n      cleanContent = cleanContent.replace(/^```\\s*/, '').replace(/\\s*```$/, '');\n    }\n\n    const questionData = JSON.parse(cleanContent);\n    return createQuestionObject(questionData, questionType, difficulty, chunk.content);\n  } catch (error) {\n    console.error('Error generating question:', error);\n    return null;\n  }\n}\n\nfunction createPrompt(text: string, questionType: QuestionType, difficulty: DifficultyLevel, language?: string): string {\n  const isArabic = language === 'arabic';\n\n  const basePrompt = isArabic\n    ? `مهم: قم بإنشاء سؤال فقط بناءً على المعلومات المذكورة صراحة في النص التالي. لا تستخدم أي معرفة خارجية أو معلومات غير موجودة في هذا النص.\n\nالنص: \"${text}\"\n\nقم بإنشاء سؤال بمستوى صعوبة ${difficulty} من نوع ${questionType} باستخدام المعلومات من النص أعلاه فقط. يجب أن يكون السؤال قابلاً للإجابة من المحتوى المقدم فقط.\n\n`\n    : `IMPORTANT: Create a question ONLY based on the information explicitly stated in the following text. Do NOT use any external knowledge or information not present in this text.\n\nText: \"${text}\"\n\nGenerate a ${difficulty} difficulty ${questionType} question using ONLY the information from the above text. The question must be answerable solely from the provided content.\n\n`;\n\n  switch (questionType) {\n    case 'mcq':\n      return basePrompt + `Create a multiple-choice question with 4 options where the correct answer is directly found in the provided text. Respond in JSON format:\n{\n  \"question\": \"Your question based only on the provided text\",\n  \"options\": [\n    {\"text\": \"Option A\", \"isCorrect\": false},\n    {\"text\": \"Option B\", \"isCorrect\": true},\n    {\"text\": \"Option C\", \"isCorrect\": false},\n    {\"text\": \"Option D\", \"isCorrect\": false}\n  ],\n  \"explanation\": \"Brief explanation referencing the provided text\"\n}`;\n\n    case 'true-false':\n      return basePrompt + `Create a true/false statement that can be verified directly from the provided text. Respond in JSON format:\n{\n  \"question\": \"Your true/false statement based only on the provided text\",\n  \"correctAnswer\": true,\n  \"explanation\": \"Brief explanation referencing the provided text\"\n}`;\n\n    case 'fill-blank':\n      return basePrompt + `Create a fill-in-the-blank question where the missing word/phrase appears in the provided text. Respond in JSON format:\n{\n  \"question\": \"Your question with _____ blank (based only on provided text)\",\n  \"correctAnswer\": \"correct answer from the text\",\n  \"acceptableAnswers\": [\"alternative answer 1\", \"alternative answer 2\"],\n  \"explanation\": \"Brief explanation referencing the provided text\"\n}`;\n\n    default:\n      return basePrompt;\n  }\n}\n\nfunction createQuestionObject(\n  data: any,\n  questionType: QuestionType,\n  difficulty: DifficultyLevel,\n  sourceChunk: string\n): Question {\n  const baseQuestion = {\n    id: Math.random().toString(36).substring(2, 11),\n    difficulty,\n    sourceChunk,\n    explanation: data.explanation,\n  };\n  \n  switch (questionType) {\n    case 'mcq':\n      return {\n        ...baseQuestion,\n        type: 'mcq',\n        question: data.question,\n        options: data.options.map((opt: any, index: number) => ({\n          id: `opt_${index}`,\n          text: opt.text,\n          isCorrect: opt.isCorrect,\n        })),\n      };\n    \n    case 'true-false':\n      return {\n        ...baseQuestion,\n        type: 'true-false',\n        question: data.question,\n        correctAnswer: data.correctAnswer,\n      };\n    \n    case 'fill-blank':\n      return {\n        ...baseQuestion,\n        type: 'fill-blank',\n        question: data.question,\n        correctAnswer: data.correctAnswer,\n        acceptableAnswers: data.acceptableAnswers || [],\n      };\n    \n    default:\n      throw new Error(`Unsupported question type: ${questionType}`);\n  }\n}\n\nfunction selectRelevantChunks(chunks: DocumentChunk[], count: number): DocumentChunk[] {\n  // Simple selection - in a real app, you might use embedding similarity\n  const shuffled = [...chunks].sort(() => Math.random() - 0.5);\n  return shuffled.slice(0, count);\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAGA,MAAM,WAAW,IAAI,wKAAA,CAAA,UAAM,CAAC;IAC1B,QAAQ,QAAQ,GAAG,CAAC,gBAAgB;IACpC,SAAS,QAAQ,GAAG,CAAC,iBAAiB,IAAI;AAC5C;AAIO,SAAS,kBAAkB,IAAY;IAC5C,gEAAgE;IAChE,0EAA0E;IAC1E,MAAM,OAAO,KAAK,KAAK,CAAC,IAAI,MAAM,CAAC,CAAC,GAAG;QACrC,IAAI,AAAC,CAAC,KAAK,CAAC,IAAI,IAAK,EAAE,UAAU,CAAC;QAClC,OAAO,IAAI;IACb,GAAG;IAEH,yEAAyE;IACzE,MAAM,YAAY,MAAM,IAAI,CAAC;QAAE,QAAQ;IAAI,GAAG,CAAC,GAAG;QAChD,OAAO,KAAK,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,QAAQ;IAC3C;IAEA,OAAO;AACT;AAEO,eAAe,kBACpB,MAAuB,EACvB,aAA6B,EAC7B,UAA6B,EAC7B,gBAAwB,EACxB,QAAiB;IAEjB,MAAM,YAAwB,EAAE;IAEhC,yDAAyD;IACzD,MAAM,oBAAoB;IAC1B,MAAM,0BAA0B,cAAc,MAAM,GAAG,WAAW,MAAM,GAAG;IAE3E,IAAI,0BAA0B,mBAAmB;QAC/C,QAAQ,IAAI,CAAC,CAAC,UAAU,EAAE,wBAAwB,wBAAwB,EAAE,kBAAkB,gBAAgB,CAAC;IACjH;IAEA,sDAAsD;IACtD,KAAK,MAAM,gBAAgB,cAAe;QACxC,KAAK,MAAM,QAAQ,WAAY;YAC7B,MAAM,gBAAgB,KAAK,GAAG,CAAC,kBAAkB,KAAK,IAAI,CAAC,oBAAoB,CAAC,cAAc,MAAM,GAAG,WAAW,MAAM;YACxH,MAAM,gBAAgB,MAAM,yBAC1B,QACA,cACA,MACA,eACA;YAEF,UAAU,IAAI,IAAI;YAElB,kCAAkC;YAClC,IAAI,UAAU,MAAM,IAAI,mBAAmB;gBACzC;YACF;QACF;QACA,IAAI,UAAU,MAAM,IAAI,mBAAmB;YACzC;QACF;IACF;IAEA,OAAO,UAAU,KAAK,CAAC,GAAG;AAC5B;AAEA,eAAe,yBACb,MAAuB,EACvB,YAA0B,EAC1B,UAA2B,EAC3B,KAAa,EACb,QAAiB;IAEjB,MAAM,iBAAiB,qBAAqB,QAAQ;IACpD,MAAM,YAAwB,EAAE;IAEhC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,GAAG,CAAC,OAAO,eAAe,MAAM,GAAG,IAAK;QAC/D,MAAM,QAAQ,cAAc,CAAC,EAAE;QAE/B,IAAI;YACF,MAAM,WAAW,MAAM,uBAAuB,OAAO,cAAc,YAAY;YAC/E,IAAI,UAAU;gBACZ,UAAU,IAAI,CAAC;YACjB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,oCAAoC,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE;QACpE;IACF;IAEA,OAAO;AACT;AAEA,eAAe,uBACb,KAAoB,EACpB,YAA0B,EAC1B,UAA2B,EAC3B,QAAiB;IAEjB,MAAM,SAAS,aAAa,MAAM,OAAO,EAAE,cAAc,YAAY;IAErE,IAAI;QACF,MAAM,gBAAgB,aAAa,WAC/B,yLACA;QAEJ,MAAM,WAAW,MAAM,SAAS,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YACtD,OAAO;YACP,UAAU;gBACR;oBACE,MAAM;oBACN,SAAS;gBACX;gBACA;oBACE,MAAM;oBACN,SAAS;gBACX;aACD;YACD,aAAa;YACb,YAAY;QACd;QAEA,MAAM,UAAU,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS;QAC9C,IAAI,CAAC,SAAS,OAAO;QAErB,8DAA8D;QAC9D,IAAI,eAAe,QAAQ,IAAI;QAC/B,IAAI,aAAa,UAAU,CAAC,YAAY;YACtC,eAAe,aAAa,OAAO,CAAC,eAAe,IAAI,OAAO,CAAC,WAAW;QAC5E,OAAO,IAAI,aAAa,UAAU,CAAC,QAAQ;YACzC,eAAe,aAAa,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,WAAW;QACxE;QAEA,MAAM,eAAe,KAAK,KAAK,CAAC;QAChC,OAAO,qBAAqB,cAAc,cAAc,YAAY,MAAM,OAAO;IACnF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO;IACT;AACF;AAEA,SAAS,aAAa,IAAY,EAAE,YAA0B,EAAE,UAA2B,EAAE,QAAiB;IAC5G,MAAM,WAAW,aAAa;IAE9B,MAAM,aAAa,WACf,CAAC;;OAEA,EAAE,KAAK;;4BAEc,EAAE,WAAW,QAAQ,EAAE,aAAa;;AAEhE,CAAC,GACK,CAAC;;OAEA,EAAE,KAAK;;WAEH,EAAE,WAAW,YAAY,EAAE,aAAa;;AAEnD,CAAC;IAEC,OAAQ;QACN,KAAK;YACH,OAAO,aAAa,CAAC;;;;;;;;;;CAU1B,CAAC;QAEE,KAAK;YACH,OAAO,aAAa,CAAC;;;;;CAK1B,CAAC;QAEE,KAAK;YACH,OAAO,aAAa,CAAC;;;;;;CAM1B,CAAC;QAEE;YACE,OAAO;IACX;AACF;AAEA,SAAS,qBACP,IAAS,EACT,YAA0B,EAC1B,UAA2B,EAC3B,WAAmB;IAEnB,MAAM,eAAe;QACnB,IAAI,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;QAC5C;QACA;QACA,aAAa,KAAK,WAAW;IAC/B;IAEA,OAAQ;QACN,KAAK;YACH,OAAO;gBACL,GAAG,YAAY;gBACf,MAAM;gBACN,UAAU,KAAK,QAAQ;gBACvB,SAAS,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,KAAU,QAAkB,CAAC;wBACtD,IAAI,CAAC,IAAI,EAAE,OAAO;wBAClB,MAAM,IAAI,IAAI;wBACd,WAAW,IAAI,SAAS;oBAC1B,CAAC;YACH;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,YAAY;gBACf,MAAM;gBACN,UAAU,KAAK,QAAQ;gBACvB,eAAe,KAAK,aAAa;YACnC;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,YAAY;gBACf,MAAM;gBACN,UAAU,KAAK,QAAQ;gBACvB,eAAe,KAAK,aAAa;gBACjC,mBAAmB,KAAK,iBAAiB,IAAI,EAAE;YACjD;QAEF;YACE,MAAM,IAAI,MAAM,CAAC,2BAA2B,EAAE,cAAc;IAChE;AACF;AAEA,SAAS,qBAAqB,MAAuB,EAAE,KAAa;IAClE,uEAAuE;IACvE,MAAM,WAAW;WAAI;KAAO,CAAC,IAAI,CAAC,IAAM,KAAK,MAAM,KAAK;IACxD,OAAO,SAAS,KAAK,CAAC,GAAG;AAC3B", "debugId": null}}, {"offset": {"line": 260, "column": 0}, "map": {"version": 3, "sources": ["file:///Z:/projectx/pdf-quiz-generator/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9)\n}\n\nexport function chunkText(text: string, maxChunkSize: number = 1000, overlap: number = 100): string[] {\n  const chunks: string[] = []\n  const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0)\n  \n  let currentChunk = ''\n  let currentSize = 0\n  \n  for (const sentence of sentences) {\n    const sentenceSize = sentence.trim().length\n    \n    if (currentSize + sentenceSize > maxChunkSize && currentChunk.length > 0) {\n      chunks.push(currentChunk.trim())\n      \n      // Create overlap by keeping the last few sentences\n      const overlapSentences = currentChunk.split(/[.!?]+/).slice(-2).join('. ')\n      currentChunk = overlapSentences + '. ' + sentence.trim()\n      currentSize = currentChunk.length\n    } else {\n      currentChunk += (currentChunk ? '. ' : '') + sentence.trim()\n      currentSize = currentChunk.length\n    }\n  }\n  \n  if (currentChunk.trim().length > 0) {\n    chunks.push(currentChunk.trim())\n  }\n  \n  return chunks.filter(chunk => chunk.length > 50) // Filter out very short chunks\n}\n\nexport function sanitizeText(text: string): string {\n  return text\n    .replace(/\\s+/g, ' ')\n    .replace(/[^\\w\\s.,!?;:()\\-\"']/g, '')\n    .trim()\n}\n\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes'\n  \n  const k = 1024\n  const sizes = ['Bytes', 'KB', 'MB', 'GB']\n  const i = Math.floor(Math.log(bytes) / Math.log(k))\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n}\n\nexport function validatePDF(file: File): { isValid: boolean; error?: string } {\n  if (!file) {\n    return { isValid: false, error: 'No file provided' }\n  }\n  \n  if (file.type !== 'application/pdf') {\n    return { isValid: false, error: 'File must be a PDF' }\n  }\n  \n  // 50MB limit\n  if (file.size > 50 * 1024 * 1024) {\n    return { isValid: false, error: 'File size must be less than 50MB' }\n  }\n  \n  return { isValid: true }\n}\n\nexport function downloadAsJSON(data: any, filename: string) {\n  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })\n  const url = URL.createObjectURL(blob)\n  const a = document.createElement('a')\n  a.href = url\n  a.download = filename\n  document.body.appendChild(a)\n  a.click()\n  document.body.removeChild(a)\n  URL.revokeObjectURL(url)\n}\n\nexport function downloadAsCSV(data: any[], filename: string) {\n  if (data.length === 0) return\n  \n  const headers = Object.keys(data[0])\n  const csvContent = [\n    headers.join(','),\n    ...data.map(row => \n      headers.map(header => {\n        const value = row[header]\n        if (typeof value === 'string' && (value.includes(',') || value.includes('\"'))) {\n          return `\"${value.replace(/\"/g, '\"\"')}\"`\n        }\n        return value\n      }).join(',')\n    )\n  ].join('\\n')\n  \n  const blob = new Blob([csvContent], { type: 'text/csv' })\n  const url = URL.createObjectURL(blob)\n  const a = document.createElement('a')\n  a.href = url\n  a.download = filename\n  document.body.appendChild(a)\n  a.click()\n  document.body.removeChild(a)\n  URL.revokeObjectURL(url)\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,uIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,UAAU,IAAY,EAAE,eAAuB,IAAI,EAAE,UAAkB,GAAG;IACxF,MAAM,SAAmB,EAAE;IAC3B,MAAM,YAAY,KAAK,KAAK,CAAC,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,GAAG,MAAM,GAAG;IAErE,IAAI,eAAe;IACnB,IAAI,cAAc;IAElB,KAAK,MAAM,YAAY,UAAW;QAChC,MAAM,eAAe,SAAS,IAAI,GAAG,MAAM;QAE3C,IAAI,cAAc,eAAe,gBAAgB,aAAa,MAAM,GAAG,GAAG;YACxE,OAAO,IAAI,CAAC,aAAa,IAAI;YAE7B,mDAAmD;YACnD,MAAM,mBAAmB,aAAa,KAAK,CAAC,UAAU,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC;YACrE,eAAe,mBAAmB,OAAO,SAAS,IAAI;YACtD,cAAc,aAAa,MAAM;QACnC,OAAO;YACL,gBAAgB,CAAC,eAAe,OAAO,EAAE,IAAI,SAAS,IAAI;YAC1D,cAAc,aAAa,MAAM;QACnC;IACF;IAEA,IAAI,aAAa,IAAI,GAAG,MAAM,GAAG,GAAG;QAClC,OAAO,IAAI,CAAC,aAAa,IAAI;IAC/B;IAEA,OAAO,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,GAAG,IAAI,+BAA+B;;AAClF;AAEO,SAAS,aAAa,IAAY;IACvC,OAAO,KACJ,OAAO,CAAC,QAAQ,KAChB,OAAO,CAAC,wBAAwB,IAChC,IAAI;AACT;AAEO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAEO,SAAS,YAAY,IAAU;IACpC,IAAI,CAAC,MAAM;QACT,OAAO;YAAE,SAAS;YAAO,OAAO;QAAmB;IACrD;IAEA,IAAI,KAAK,IAAI,KAAK,mBAAmB;QACnC,OAAO;YAAE,SAAS;YAAO,OAAO;QAAqB;IACvD;IAEA,aAAa;IACb,IAAI,KAAK,IAAI,GAAG,KAAK,OAAO,MAAM;QAChC,OAAO;YAAE,SAAS;YAAO,OAAO;QAAmC;IACrE;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAEO,SAAS,eAAe,IAAS,EAAE,QAAgB;IACxD,MAAM,OAAO,IAAI,KAAK;QAAC,KAAK,SAAS,CAAC,MAAM,MAAM;KAAG,EAAE;QAAE,MAAM;IAAmB;IAClF,MAAM,MAAM,IAAI,eAAe,CAAC;IAChC,MAAM,IAAI,SAAS,aAAa,CAAC;IACjC,EAAE,IAAI,GAAG;IACT,EAAE,QAAQ,GAAG;IACb,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,EAAE,KAAK;IACP,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,IAAI,eAAe,CAAC;AACtB;AAEO,SAAS,cAAc,IAAW,EAAE,QAAgB;IACzD,IAAI,KAAK,MAAM,KAAK,GAAG;IAEvB,MAAM,UAAU,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE;IACnC,MAAM,aAAa;QACjB,QAAQ,IAAI,CAAC;WACV,KAAK,GAAG,CAAC,CAAA,MACV,QAAQ,GAAG,CAAC,CAAA;gBACV,MAAM,QAAQ,GAAG,CAAC,OAAO;gBACzB,IAAI,OAAO,UAAU,YAAY,CAAC,MAAM,QAAQ,CAAC,QAAQ,MAAM,QAAQ,CAAC,IAAI,GAAG;oBAC7E,OAAO,CAAC,CAAC,EAAE,MAAM,OAAO,CAAC,MAAM,MAAM,CAAC,CAAC;gBACzC;gBACA,OAAO;YACT,GAAG,IAAI,CAAC;KAEX,CAAC,IAAI,CAAC;IAEP,MAAM,OAAO,IAAI,KAAK;QAAC;KAAW,EAAE;QAAE,MAAM;IAAW;IACvD,MAAM,MAAM,IAAI,eAAe,CAAC;IAChC,MAAM,IAAI,SAAS,aAAa,CAAC;IACjC,EAAE,IAAI,GAAG;IACT,EAAE,QAAQ,GAAG;IACb,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,EAAE,KAAK;IACP,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,IAAI,eAAe,CAAC;AACtB", "debugId": null}}, {"offset": {"line": 391, "column": 0}, "map": {"version": 3, "sources": ["file:///Z:/projectx/pdf-quiz-generator/src/app/api/generate-quiz/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { generateQuestions, generateEmbedding } from '@/lib/deepseek';\nimport { DocumentChunk, QuizGenerationOptions, Quiz } from '@/types/quiz';\nimport { generateId } from '@/lib/utils';\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const { chunks, options, documentName, language } = body as {\n      chunks: DocumentChunk[];\n      options: QuizGenerationOptions;\n      documentName: string;\n      language?: string;\n    };\n    \n    if (!chunks || chunks.length === 0) {\n      return NextResponse.json({ error: 'No document chunks provided' }, { status: 400 });\n    }\n    \n    if (!options) {\n      return NextResponse.json({ error: 'Quiz generation options not provided' }, { status: 400 });\n    }\n    \n    // Validate options\n    if (!options.questionTypes || options.questionTypes.length === 0) {\n      return NextResponse.json({ error: 'At least one question type must be selected' }, { status: 400 });\n    }\n    \n    if (!options.difficulty || options.difficulty.length === 0) {\n      return NextResponse.json({ error: 'At least one difficulty level must be selected' }, { status: 400 });\n    }\n    \n    if (options.questionsPerType < 1 || options.questionsPerType > 10) {\n      return NextResponse.json({ error: 'Questions per type must be between 1 and 10' }, { status: 400 });\n    }\n    \n    try {\n      // Generate simple embeddings for chunks if not already present\n      // Note: DeepSeek doesn't provide embedding models, so we use a simple approach\n      const chunksWithEmbeddings = chunks.map((chunk) => {\n        if (!chunk.embedding) {\n          try {\n            const embedding = generateEmbedding(chunk.content);\n            return { ...chunk, embedding };\n          } catch (embeddingError) {\n            console.warn(`Failed to generate embedding for chunk ${chunk.id}:`, embeddingError);\n            return chunk; // Return chunk without embedding\n          }\n        }\n        return chunk;\n      });\n      \n      // Generate questions\n      const questions = await generateQuestions(\n        chunksWithEmbeddings,\n        options.questionTypes,\n        options.difficulty,\n        options.questionsPerType,\n        language\n      );\n      \n      if (questions.length === 0) {\n        return NextResponse.json(\n          { error: 'Failed to generate any questions. Please try with different options or check your document content.' },\n          { status: 400 }\n        );\n      }\n      \n      // Create quiz object\n      const quiz: Quiz = {\n        id: generateId(),\n        title: `Quiz: ${documentName}`,\n        description: `Generated quiz with ${questions.length} questions from ${documentName}`,\n        questions,\n        createdAt: new Date(),\n        sourceDocument: documentName,\n      };\n      \n      return NextResponse.json({\n        success: true,\n        quiz,\n        stats: {\n          totalQuestions: questions.length,\n          questionTypes: options.questionTypes,\n          difficulties: options.difficulty,\n          questionsPerType: options.questionsPerType,\n        },\n      });\n      \n    } catch (generationError) {\n      console.error('Quiz generation error:', generationError);\n      \n      // Check if it's an API key issue\n      if (generationError instanceof Error && generationError.message.includes('API key')) {\n        return NextResponse.json(\n          { error: 'DeepSeek API key is not configured or invalid. Please check your environment variables.' },\n          { status: 401 }\n        );\n      }\n      \n      return NextResponse.json(\n        { error: 'Failed to generate quiz. Please try again or contact support if the issue persists.' },\n        { status: 500 }\n      );\n    }\n    \n  } catch (error) {\n    console.error('Request processing error:', error);\n    return NextResponse.json(\n      { error: 'Invalid request format or internal server error' },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function GET() {\n  return NextResponse.json({ \n    message: 'Quiz generation endpoint',\n    supportedQuestionTypes: ['mcq', 'true-false', 'fill-blank'],\n    supportedDifficulties: ['easy', 'medium', 'hard'],\n  });\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAEA;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,QAAQ,EAAE,GAAG;QAOpD,IAAI,CAAC,UAAU,OAAO,MAAM,KAAK,GAAG;YAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAA8B,GAAG;gBAAE,QAAQ;YAAI;QACnF;QAEA,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAuC,GAAG;gBAAE,QAAQ;YAAI;QAC5F;QAEA,mBAAmB;QACnB,IAAI,CAAC,QAAQ,aAAa,IAAI,QAAQ,aAAa,CAAC,MAAM,KAAK,GAAG;YAChE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAA8C,GAAG;gBAAE,QAAQ;YAAI;QACnG;QAEA,IAAI,CAAC,QAAQ,UAAU,IAAI,QAAQ,UAAU,CAAC,MAAM,KAAK,GAAG;YAC1D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAiD,GAAG;gBAAE,QAAQ;YAAI;QACtG;QAEA,IAAI,QAAQ,gBAAgB,GAAG,KAAK,QAAQ,gBAAgB,GAAG,IAAI;YACjE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAA8C,GAAG;gBAAE,QAAQ;YAAI;QACnG;QAEA,IAAI;YACF,+DAA+D;YAC/D,+EAA+E;YAC/E,MAAM,uBAAuB,OAAO,GAAG,CAAC,CAAC;gBACvC,IAAI,CAAC,MAAM,SAAS,EAAE;oBACpB,IAAI;wBACF,MAAM,YAAY,CAAA,GAAA,wHAAA,CAAA,oBAAiB,AAAD,EAAE,MAAM,OAAO;wBACjD,OAAO;4BAAE,GAAG,KAAK;4BAAE;wBAAU;oBAC/B,EAAE,OAAO,gBAAgB;wBACvB,QAAQ,IAAI,CAAC,CAAC,uCAAuC,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE;wBACpE,OAAO,OAAO,iCAAiC;oBACjD;gBACF;gBACA,OAAO;YACT;YAEA,qBAAqB;YACrB,MAAM,YAAY,MAAM,CAAA,GAAA,wHAAA,CAAA,oBAAiB,AAAD,EACtC,sBACA,QAAQ,aAAa,EACrB,QAAQ,UAAU,EAClB,QAAQ,gBAAgB,EACxB;YAGF,IAAI,UAAU,MAAM,KAAK,GAAG;gBAC1B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAsG,GAC/G;oBAAE,QAAQ;gBAAI;YAElB;YAEA,qBAAqB;YACrB,MAAM,OAAa;gBACjB,IAAI,CAAA,GAAA,qHAAA,CAAA,aAAU,AAAD;gBACb,OAAO,CAAC,MAAM,EAAE,cAAc;gBAC9B,aAAa,CAAC,oBAAoB,EAAE,UAAU,MAAM,CAAC,gBAAgB,EAAE,cAAc;gBACrF;gBACA,WAAW,IAAI;gBACf,gBAAgB;YAClB;YAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT;gBACA,OAAO;oBACL,gBAAgB,UAAU,MAAM;oBAChC,eAAe,QAAQ,aAAa;oBACpC,cAAc,QAAQ,UAAU;oBAChC,kBAAkB,QAAQ,gBAAgB;gBAC5C;YACF;QAEF,EAAE,OAAO,iBAAiB;YACxB,QAAQ,KAAK,CAAC,0BAA0B;YAExC,iCAAiC;YACjC,IAAI,2BAA2B,SAAS,gBAAgB,OAAO,CAAC,QAAQ,CAAC,YAAY;gBACnF,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAA0F,GACnG;oBAAE,QAAQ;gBAAI;YAElB;YAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAsF,GAC/F;gBAAE,QAAQ;YAAI;QAElB;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAkD,GAC3D;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe;IACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QACvB,SAAS;QACT,wBAAwB;YAAC;YAAO;YAAc;SAAa;QAC3D,uBAAuB;YAAC;YAAQ;YAAU;SAAO;IACnD;AACF", "debugId": null}}]}